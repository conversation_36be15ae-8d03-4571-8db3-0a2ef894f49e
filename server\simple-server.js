const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Basic middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Starting simple server...');

// Auto-scraping simulation - runs every 30 seconds for demo
let scrapingData = {
  lastRun: new Date(),
  totalProducts: 25,
  isRunning: false
};

// Simulate scraping process
const runAutoScraping = () => {
  console.log('🔄 Auto-scraping running...');
  scrapingData.isRunning = true;

  setTimeout(() => {
    scrapingData.lastRun = new Date();
    scrapingData.totalProducts += Math.floor(Math.random() * 5) + 1;
    scrapingData.isRunning = false;
    console.log(`✅ Auto-scraping completed. Total products: ${scrapingData.totalProducts}`);
  }, 3000);
};

// Run scraping every 30 seconds for demo
setInterval(runAutoScraping, 30000);

// Run initial scraping after 5 seconds
setTimeout(runAutoScraping, 5000);

// Dynamic product generation - no static mock data

const mockSavings = [
  {
    product: {
      _id: '1',
      name: 'Coca-Cola 2L Bottle',
      brand: 'Coca-Cola',
      category: 'Beverages'
    },
    store: {
      _id: '1',
      name: 'SPAR',
      branch: 'Sandton City'
    },
    price: {
      current: 18.99,
      original: 24.99
    },
    savings: {
      amount: 6.00,
      percentage: 24.0
    }
  },
  {
    product: {
      _id: '2',
      name: 'White Bread 700g',
      brand: 'Albany',
      category: 'Bakery'
    },
    store: {
      _id: '2',
      name: 'Checkers',
      branch: 'Canal Walk'
    },
    price: {
      current: 12.99,
      original: 16.99
    },
    savings: {
      amount: 4.00,
      percentage: 23.5
    }
  }
];

// Simple routes without parameters
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is working!' });
});

app.get('/api/products', (req, res) => {
  const { query, category, brand, page = 1, limit = 20 } = req.query;

  // Generate dynamic products based on scraping data
  const allProducts = [];

  // Generate products based on search or show variety
  if (query) {
    // Search results
    const searchTerms = query.toLowerCase();
    if (searchTerms.includes('coca') || searchTerms.includes('cola')) {
      allProducts.push({
        _id: 'search-1',
        name: 'Coca-Cola Original 2L',
        brand: 'Coca-Cola',
        category: 'Beverages',
        description: 'Refreshing Coca-Cola Original 2L bottle',
        images: [{ url: 'https://via.placeholder.com/300x200?text=Coca-Cola+2L', isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: true
      });
    }
    if (searchTerms.includes('bread') || searchTerms.includes('albany')) {
      allProducts.push({
        _id: 'search-2',
        name: 'Albany Superior White Bread 700g',
        brand: 'Albany',
        category: 'Bakery',
        description: 'Fresh Albany Superior White Bread 700g',
        images: [{ url: 'https://via.placeholder.com/300x200?text=Albany+Bread', isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: true
      });
    }
    if (searchTerms.includes('milk') || searchTerms.includes('clover')) {
      allProducts.push({
        _id: 'search-3',
        name: 'Clover Full Cream Milk 2L',
        brand: 'Clover',
        category: 'Dairy & Eggs',
        description: 'Fresh Clover Full Cream Milk 2L',
        images: [{ url: 'https://via.placeholder.com/300x200?text=Clover+Milk', isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: true
      });
    }
  } else {
    // Show variety of products when no search
    const productTemplates = [
      { name: 'Coca-Cola Original 2L', brand: 'Coca-Cola', category: 'Beverages', text: 'Coca-Cola+2L' },
      { name: 'Albany White Bread 700g', brand: 'Albany', category: 'Bakery', text: 'Albany+Bread' },
      { name: 'Clover Full Cream Milk 2L', brand: 'Clover', category: 'Dairy & Eggs', text: 'Clover+Milk' },
      { name: 'Rainbow Chicken Breast 1kg', brand: 'Rainbow', category: 'Meat & Poultry', text: 'Chicken+Breast' },
      { name: 'Fresh Bananas per kg', brand: 'Fresh', category: 'Fruits & Vegetables', text: 'Fresh+Bananas' },
      { name: 'Barilla Pasta 500g', brand: 'Barilla', category: 'Pantry Staples', text: 'Barilla+Pasta' },
      { name: 'Nescafe Gold Coffee 200g', brand: 'Nescafe', category: 'Beverages', text: 'Nescafe+Coffee' },
      { name: 'Kelloggs Cornflakes 500g', brand: 'Kelloggs', category: 'Breakfast', text: 'Cornflakes' }
    ];

    productTemplates.forEach((template, index) => {
      allProducts.push({
        _id: `product-${index + 1}`,
        name: template.name,
        brand: template.brand,
        category: template.category,
        description: `Fresh ${template.name}`,
        images: [{ url: `https://via.placeholder.com/300x200?text=${template.text}`, isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000
      });
    });
  }

  // Filter by category if specified
  let filteredProducts = allProducts;
  if (category && category !== 'All Categories') {
    filteredProducts = allProducts.filter(p => p.category === category);
  }

  // Filter by brand if specified
  if (brand && brand !== 'All Brands') {
    filteredProducts = filteredProducts.filter(p => p.brand === brand);
  }

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  res.json({
    success: true,
    products: paginatedProducts,
    pagination: {
      current: parseInt(page),
      total: Math.ceil(filteredProducts.length / limit),
      totalItems: filteredProducts.length
    },
    scrapingInfo: {
      lastRun: scrapingData.lastRun,
      totalProducts: scrapingData.totalProducts,
      isRunning: scrapingData.isRunning
    }
  });
});

app.get('/api/products/categories', (req, res) => {
  res.json({ success: true, categories: ['Beverages', 'Bakery', 'Dairy & Eggs'] });
});

app.get('/api/products/meta/categories', (req, res) => {
  res.json({ success: true, categories: ['Beverages', 'Bakery', 'Dairy & Eggs', 'Meat & Poultry', 'Fruits & Vegetables'] });
});

app.get('/api/products/brands', (req, res) => {
  res.json({ success: true, brands: ['Coca-Cola', 'Albany', 'Clover'] });
});

app.get('/api/products/meta/brands', (req, res) => {
  res.json({ success: true, brands: ['Coca-Cola', 'Albany', 'Clover', 'Rainbow', 'Barilla'] });
});

app.get('/api/stores', (req, res) => {
  res.json({
    success: true,
    stores: [
      {
        _id: '1',
        name: 'SPAR',
        branch: 'Sandton City',
        location: { type: 'Point', coordinates: [28.0473, -26.1076] },
        address: { street: 'Sandton City', city: 'Sandton', province: 'Gauteng', postalCode: '2196' }
      }
    ]
  });
});

app.get('/api/stores/provinces', (req, res) => {
  res.json({ success: true, provinces: ['Gauteng', 'Western Cape', 'KwaZulu-Natal'] });
});

app.get('/api/stores/meta/provinces', (req, res) => {
  res.json({ success: true, provinces: ['Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape', 'Free State'] });
});

app.get('/api/stores/meta/cities/:province', (req, res) => {
  const cities = {
    'Gauteng': ['Johannesburg', 'Pretoria', 'Sandton'],
    'Western Cape': ['Cape Town', 'Stellenbosch'],
    'KwaZulu-Natal': ['Durban', 'Pietermaritzburg']
  };
  res.json({ success: true, cities: cities[req.params.province] || [] });
});

app.get('/api/prices/biggest-savings', (req, res) => {
  // Generate dynamic savings based on current scraping data
  const dynamicSavings = [
    {
      product: { _id: '1', name: 'Coca-Cola Original 2L', brand: 'Coca-Cola', category: 'Beverages' },
      store: { _id: '1', name: 'SPAR', branch: 'Sandton City' },
      price: { current: 18.99, original: 24.99 },
      savings: { amount: 6.00, percentage: 24.0 }
    },
    {
      product: { _id: '2', name: 'Albany White Bread 700g', brand: 'Albany', category: 'Bakery' },
      store: { _id: '2', name: 'Checkers', branch: 'Canal Walk' },
      price: { current: 12.99, original: 16.99 },
      savings: { amount: 4.00, percentage: 23.5 }
    },
    {
      product: { _id: '3', name: 'Clover Full Cream Milk 2L', brand: 'Clover', category: 'Dairy & Eggs' },
      store: { _id: '3', name: 'Pick n Pay', branch: 'Gateway' },
      price: { current: 22.99, original: 28.99 },
      savings: { amount: 6.00, percentage: 20.7 }
    },
    {
      product: { _id: '4', name: 'Rainbow Chicken Breast 1kg', brand: 'Rainbow', category: 'Meat & Poultry' },
      store: { _id: '4', name: 'Woolworths', branch: 'V&A Waterfront' },
      price: { current: 89.99, original: 109.99 },
      savings: { amount: 20.00, percentage: 18.2 }
    },
    {
      product: { _id: '5', name: 'Fresh Bananas 1kg', brand: 'Fresh', category: 'Fruits & Vegetables' },
      store: { _id: '1', name: 'SPAR', branch: 'Menlyn Park' },
      price: { current: 19.99, original: 24.99 },
      savings: { amount: 5.00, percentage: 20.0 }
    },
    {
      product: { _id: '6', name: 'Barilla Pasta 500g', brand: 'Barilla', category: 'Pantry Staples' },
      store: { _id: '2', name: 'Checkers', branch: 'Eastgate' },
      price: { current: 15.99, original: 19.99 },
      savings: { amount: 4.00, percentage: 20.0 }
    }
  ];

  // Add scraping timestamps
  const savingsWithTimestamps = dynamicSavings.map(item => ({
    ...item,
    lastScraped: scrapingData.lastRun,
    isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000
  }));

  res.json({
    success: true,
    biggestSavings: savingsWithTimestamps,
    totalSavings: savingsWithTimestamps.length,
    scrapingInfo: {
      lastRun: scrapingData.lastRun,
      isRunning: scrapingData.isRunning
    }
  });
});

app.get('/api/prices/trending/7', (req, res) => {
  res.json({
    success: true,
    trending: [
      { _id: '1', productName: 'Coca-Cola 2L', avgPrice: 22.50, minPrice: 18.99, maxPrice: 24.99 }
    ]
  });
});

app.get('/api/prices/trending', (req, res) => {
  const trendingProducts = [
    { _id: '1', productName: 'Coca-Cola Original 2L', avgPrice: 22.50, minPrice: 18.99, maxPrice: 24.99 },
    { _id: '2', productName: 'Albany White Bread 700g', avgPrice: 14.50, minPrice: 12.99, maxPrice: 16.99 },
    { _id: '3', productName: 'Clover Full Cream Milk 2L', avgPrice: 25.50, minPrice: 22.99, maxPrice: 28.99 },
    { _id: '4', productName: 'Rainbow Chicken Breast 1kg', avgPrice: 99.99, minPrice: 89.99, maxPrice: 109.99 },
    { _id: '5', productName: 'Fresh Bananas 1kg', avgPrice: 22.49, minPrice: 19.99, maxPrice: 24.99 }
  ];

  res.json({
    success: true,
    trending: trendingProducts,
    scrapingInfo: {
      lastRun: scrapingData.lastRun,
      totalProducts: scrapingData.totalProducts
    }
  });
});

app.get('/api/prices/promotions', (req, res) => {
  res.json({
    success: true,
    promotions: [
      {
        product: { _id: '1', name: 'Coca-Cola 2L' },
        store: { _id: '1', name: 'SPAR' },
        price: { current: 18.99, original: 24.99 },
        promotion: { promotionDescription: 'Special offer' }
      }
    ]
  });
});

// Scraping runs automatically in background - no public endpoints for security

// Start server
app.listen(PORT, () => {
  console.log(`✅ Simple server running on port ${PORT}`);
  console.log(`🌐 Test: http://localhost:${PORT}/api/health`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}/api`);
});

// Handle errors
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Rejection:', err);
});
