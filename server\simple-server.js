const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Basic middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Starting simple server...');

// Mock data
const mockProducts = [
  {
    _id: '1',
    name: 'Coca-Cola 2L',
    brand: 'Coca-Cola',
    category: 'Beverages',
    description: 'Refreshing cola drink',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Coca-Cola', isPrimary: true }]
  },
  {
    _id: '2',
    name: 'White Bread 700g',
    brand: 'Albany',
    category: 'Bakery',
    description: 'Fresh white bread',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Bread', isPrimary: true }]
  }
];

const mockSavings = [
  {
    product: {
      _id: '1',
      name: 'Coca-Cola 2L Bottle',
      brand: 'Coca-Cola',
      category: 'Beverages'
    },
    store: {
      _id: '1',
      name: '<PERSON><PERSON>',
      branch: 'Sandton City'
    },
    price: {
      current: 18.99,
      original: 24.99
    },
    savings: {
      amount: 6.00,
      percentage: 24.0
    }
  },
  {
    product: {
      _id: '2',
      name: 'White Bread 700g',
      brand: 'Albany',
      category: 'Bakery'
    },
    store: {
      _id: '2',
      name: 'Checkers',
      branch: 'Canal Walk'
    },
    price: {
      current: 12.99,
      original: 16.99
    },
    savings: {
      amount: 4.00,
      percentage: 23.5
    }
  }
];

// Simple routes without parameters
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is working!' });
});

app.get('/api/products', (req, res) => {
  res.json({
    success: true,
    products: mockProducts,
    pagination: { current: 1, total: 1, totalItems: mockProducts.length }
  });
});

app.get('/api/products/categories', (req, res) => {
  res.json({ success: true, categories: ['Beverages', 'Bakery', 'Dairy & Eggs'] });
});

app.get('/api/products/meta/categories', (req, res) => {
  res.json({ success: true, categories: ['Beverages', 'Bakery', 'Dairy & Eggs', 'Meat & Poultry', 'Fruits & Vegetables'] });
});

app.get('/api/products/brands', (req, res) => {
  res.json({ success: true, brands: ['Coca-Cola', 'Albany', 'Clover'] });
});

app.get('/api/products/meta/brands', (req, res) => {
  res.json({ success: true, brands: ['Coca-Cola', 'Albany', 'Clover', 'Rainbow', 'Barilla'] });
});

app.get('/api/stores', (req, res) => {
  res.json({
    success: true,
    stores: [
      {
        _id: '1',
        name: 'SPAR',
        branch: 'Sandton City',
        location: { type: 'Point', coordinates: [28.0473, -26.1076] },
        address: { street: 'Sandton City', city: 'Sandton', province: 'Gauteng', postalCode: '2196' }
      }
    ]
  });
});

app.get('/api/stores/provinces', (req, res) => {
  res.json({ success: true, provinces: ['Gauteng', 'Western Cape', 'KwaZulu-Natal'] });
});

app.get('/api/stores/meta/provinces', (req, res) => {
  res.json({ success: true, provinces: ['Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape', 'Free State'] });
});

app.get('/api/stores/meta/cities/:province', (req, res) => {
  const cities = {
    'Gauteng': ['Johannesburg', 'Pretoria', 'Sandton'],
    'Western Cape': ['Cape Town', 'Stellenbosch'],
    'KwaZulu-Natal': ['Durban', 'Pietermaritzburg']
  };
  res.json({ success: true, cities: cities[req.params.province] || [] });
});

app.get('/api/prices/biggest-savings', (req, res) => {
  res.json({
    success: true,
    biggestSavings: mockSavings,
    totalSavings: mockSavings.length
  });
});

app.get('/api/prices/trending/7', (req, res) => {
  res.json({
    success: true,
    trending: [
      { _id: '1', productName: 'Coca-Cola 2L', avgPrice: 22.50, minPrice: 18.99, maxPrice: 24.99 }
    ]
  });
});

app.get('/api/prices/trending', (req, res) => {
  res.json({
    success: true,
    trending: [
      { _id: '1', productName: 'Coca-Cola 2L', avgPrice: 22.50, minPrice: 18.99, maxPrice: 24.99 },
      { _id: '2', productName: 'White Bread 700g', avgPrice: 14.50, minPrice: 12.99, maxPrice: 16.99 },
      { _id: '3', productName: 'Full Cream Milk 2L', avgPrice: 25.50, minPrice: 22.99, maxPrice: 28.99 }
    ]
  });
});

app.get('/api/prices/promotions', (req, res) => {
  res.json({
    success: true,
    promotions: [
      {
        product: { _id: '1', name: 'Coca-Cola 2L' },
        store: { _id: '1', name: 'SPAR' },
        price: { current: 18.99, original: 24.99 },
        promotion: { promotionDescription: 'Special offer' }
      }
    ]
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`✅ Simple server running on port ${PORT}`);
  console.log(`🌐 Test: http://localhost:${PORT}/api/health`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}/api`);
});

// Handle errors
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Rejection:', err);
});
