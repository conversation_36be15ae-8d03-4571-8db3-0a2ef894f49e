const express = require('express');
const router = express.Router();
const { Product, Price } = require('../models');

// GET /api/products - Search and list products
router.get('/', async (req, res) => {
  try {
    const {
      q: query,
      category,
      brand,
      page = 1,
      limit = 20,
      sortBy = 'relevance'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const products = await Product.search(query, {
      category,
      brand,
      limit: parseInt(limit),
      skip,
      sortBy
    });

    const total = await Product.countDocuments({
      ...(query && { $text: { $search: query } }),
      ...(category && { category }),
      ...(brand && { brand: new RegExp(brand, 'i') }),
      isActive: true
    });

    res.json({
      products,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: products.length,
        totalItems: total
      }
    });
  } catch (error) {
    console.error('Error searching products:', error);
    res.status(500).json({ message: 'Error searching products', error: error.message });
  }
});

// GET /api/products/:id - Get single product with price comparison
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { lat, lng, maxDistance = 10000 } = req.query;

    const product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Get price comparison
    const location = lat && lng ? {
      coordinates: [parseFloat(lng), parseFloat(lat)]
    } : null;

    const priceComparison = await Price.compareProduct(id, {
      location,
      maxDistance: parseInt(maxDistance)
    });

    // Get similar products
    const similarProducts = await Product.findSimilar(id);

    res.json({
      product,
      priceComparison,
      similarProducts
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ message: 'Error fetching product', error: error.message });
  }
});

// GET /api/products/:id/prices - Get price history for a product
router.get('/:id/prices', async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId, days = 30 } = req.query;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    let query = {
      product: id,
      'scrapingInfo.lastScraped': { $gte: startDate },
      isActive: true
    };

    if (storeId) {
      query.store = storeId;
    }

    const prices = await Price.find(query)
      .populate('store', 'name branch location address')
      .sort({ 'scrapingInfo.lastScraped': -1 });

    res.json({ prices });
  } catch (error) {
    console.error('Error fetching price history:', error);
    res.status(500).json({ message: 'Error fetching price history', error: error.message });
  }
});

// GET /api/products/categories - Get all product categories
router.get('/meta/categories', async (req, res) => {
  try {
    const categories = await Product.distinct('category', { isActive: true });
    res.json({ categories });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ message: 'Error fetching categories', error: error.message });
  }
});

// GET /api/products/brands - Get all brands
router.get('/meta/brands', async (req, res) => {
  try {
    const { category } = req.query;
    
    let query = { isActive: true, brand: { $ne: null, $ne: '' } };
    if (category) {
      query.category = category;
    }

    const brands = await Product.distinct('brand', query);
    res.json({ brands: brands.sort() });
  } catch (error) {
    console.error('Error fetching brands:', error);
    res.status(500).json({ message: 'Error fetching brands', error: error.message });
  }
});

// POST /api/products - Create new product (for admin use)
router.post('/', async (req, res) => {
  try {
    const product = new Product(req.body);
    await product.save();
    res.status(201).json({ product });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(400).json({ message: 'Error creating product', error: error.message });
  }
});

// PUT /api/products/:id - Update product (for admin use)
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const product = await Product.findByIdAndUpdate(id, req.body, { 
      new: true, 
      runValidators: true 
    });
    
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    
    res.json({ product });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(400).json({ message: 'Error updating product', error: error.message });
  }
});

// DELETE /api/products/:id - Soft delete product (for admin use)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const product = await Product.findByIdAndUpdate(id, { isActive: false }, { new: true });
    
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    
    res.json({ message: 'Product deactivated successfully' });
  } catch (error) {
    console.error('Error deactivating product:', error);
    res.status(500).json({ message: 'Error deactivating product', error: error.message });
  }
});

module.exports = router;
