{"name": "basketcase-nest", "version": "1.0.0", "description": "BasketCase Price Comparison Platform - NestJS + TypeScript + React", "private": true, "scripts": {"install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run start:dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm run start:prod", "start:frontend": "cd frontend && serve -s build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm test -- --coverage --watchAll=false", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "clean": "rimraf backend/dist frontend/build backend/node_modules frontend/node_modules node_modules", "setup": "npm run install:all && npm run setup:env", "setup:env": "cd backend && cp .env.example .env"}, "dependencies": {"concurrently": "^7.6.0"}, "devDependencies": {"rimraf": "^5.0.1", "serve": "^14.2.0"}, "workspaces": ["backend", "frontend"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/basketcase-nest.git"}, "keywords": ["price-comparison", "grocery", "south-africa", "<PERSON><PERSON><PERSON>", "typescript", "react", "mongodb", "web-scraping"], "author": "BasketCase Team", "license": "MIT"}