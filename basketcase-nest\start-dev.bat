@echo off
echo 🚀 Starting BasketCase Development Environment...
echo.

echo 🔧 Setting up environment...
cd backend
if not exist .env (
    copy .env.example .env
    echo ✅ Created .env file from template
    echo ⚠️  Please edit .env with your MongoDB connection string if needed
) else (
    echo ✅ .env file already exists
)

echo.
echo 📦 Installing Backend Dependencies (this may take a few minutes)...
call npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo ❌ Backend dependency installation failed!
    echo Trying with --force flag...
    call npm install --force
    if %errorlevel% neq 0 (
        echo ❌ Backend installation still failed. Please run manually:
        echo cd backend
        echo npm install --legacy-peer-deps
        pause
        exit /b 1
    )
)

echo.
echo 📦 Installing Frontend Dependencies...
cd ..\frontend
call npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo ❌ Frontend dependency installation failed!
    echo Trying with --force flag...
    call npm install --force
    if %errorlevel% neq 0 (
        echo ❌ Frontend installation still failed. Please run manually:
        echo cd frontend
        echo npm install --legacy-peer-deps
        pause
        exit /b 1
    )
)

echo.
echo 🚀 Starting Development Servers...
echo.
echo Opening two terminals:
echo 1. Backend API (NestJS) - http://localhost:5000
echo 2. Frontend App (React) - http://localhost:3000
echo.

cd ..\backend
start "BasketCase Backend" cmd /k "npm run start:dev"
cd ..\frontend
start "BasketCase Frontend" cmd /k "npm start"

echo.
echo ✅ Development environment started!
echo.
echo 📖 API Documentation: http://localhost:5000/api/docs
echo 🌐 Frontend App: http://localhost:3000
echo 🔍 Health Check: http://localhost:5000/api/health
echo.
echo Press any key to exit...
pause > nul
