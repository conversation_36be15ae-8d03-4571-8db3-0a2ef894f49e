@echo off
title BasketCase Development
cls
echo.
echo 🚀 Starting BasketCase Development Environment...
echo.

REM Get current directory
set CURRENT_DIR=%cd%

REM Check backend dependencies
if not exist "%CURRENT_DIR%\backend\node_modules" (
    echo 📦 Installing backend dependencies...
    cd "%CURRENT_DIR%\backend"
    call npm install --legacy-peer-deps --silent
)

REM Check frontend dependencies
if not exist "%CURRENT_DIR%\frontend\node_modules" (
    echo 📦 Installing frontend dependencies...
    cd "%CURRENT_DIR%\frontend"
    call npm install --legacy-peer-deps --silent
)

REM Setup environment
cd "%CURRENT_DIR%\backend"
if not exist .env copy .env.example .env > nul

echo.
echo ✅ Backend API: http://localhost:5000
echo ✅ Frontend App: http://localhost:3000
echo ✅ API Docs: http://localhost:5000/api/docs
echo.
echo Starting servers... (Press Ctrl+C to stop both)
echo.

REM Start simple backend server
start "BasketCase Backend" cmd /k "cd /d %CURRENT_DIR%\backend && node simple-server.js"

REM Wait 3 seconds for backend to start
timeout /t 3 /nobreak > nul

REM Start frontend in new window
start "BasketCase Frontend" cmd /k "cd /d %CURRENT_DIR%\frontend && npm start"

echo ✅ Both servers started in separate windows
echo.
echo To stop: Close both terminal windows or press Ctrl+C in each
pause
