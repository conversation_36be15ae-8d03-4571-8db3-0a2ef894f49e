@echo off
title BasketCase Development Environment
echo.
echo 🚀 Starting BasketCase Development Environment...
echo.

REM Check if dependencies are installed
cd backend
if not exist node_modules (
    echo 📦 Installing backend dependencies...
    call npm install --legacy-peer-deps --silent
)

cd ..\frontend
if not exist node_modules (
    echo 📦 Installing frontend dependencies...
    call npm install --legacy-peer-deps --silent
)

REM Setup environment file
cd ..\backend
if not exist .env (
    copy .env.example .env > nul
    echo ✅ Created .env file
)

echo.
echo 🚀 Starting servers...
echo.
echo ✅ Backend API: http://localhost:5000
echo ✅ Frontend App: http://localhost:3000
echo ✅ API Docs: http://localhost:5000/api/docs
echo.

REM Start both servers in the same window using concurrently
call npx concurrently --kill-others --prefix-colors "cyan,magenta" --prefix "[{name}]" "cd backend && npm run start:dev" "cd frontend && npm start"

echo.
echo 👋 Development servers stopped.
pause
