@echo off
echo 🚀 Starting BasketCase Development Environment...
echo.

echo 📦 Installing Backend Dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Backend dependency installation failed!
    pause
    exit /b 1
)

echo.
echo 📦 Installing Frontend Dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Frontend dependency installation failed!
    pause
    exit /b 1
)

echo.
echo 🔧 Setting up environment...
cd ..\backend
if not exist .env (
    copy .env.example .env
    echo ✅ Created .env file from template
    echo ⚠️  Please edit .env with your MongoDB connection string
) else (
    echo ✅ .env file already exists
)

echo.
echo 🚀 Starting Development Servers...
echo.
echo Opening two terminals:
echo 1. Backend API (NestJS) - http://localhost:5000
echo 2. Frontend App (React) - http://localhost:3000
echo.

start "BasketCase Backend" cmd /k "cd /d %cd% && npm run start:dev"
start "BasketCase Frontend" cmd /k "cd /d %cd%\..\frontend && npm start"

echo.
echo ✅ Development environment started!
echo.
echo 📖 API Documentation: http://localhost:5000/api/docs
echo 🌐 Frontend App: http://localhost:3000
echo 🔍 Health Check: http://localhost:5000/api/health
echo.
echo Press any key to exit...
pause > nul
