{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nconst useGeolocation = (options = {}) => {\n  _s();\n  const [location, setLocation] = useState(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const defaultOptions = {\n    enableHighAccuracy: true,\n    timeout: 10000,\n    maximumAge: 300000,\n    // 5 minutes\n    ...options\n  };\n  const getCurrentPosition = useCallback(() => {\n    if (!navigator.geolocation) {\n      setError('Geolocation is not supported by this browser');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    navigator.geolocation.getCurrentPosition(position => {\n      const {\n        latitude,\n        longitude\n      } = position.coords;\n      setLocation({\n        lat: latitude,\n        lng: longitude,\n        accuracy: position.coords.accuracy,\n        timestamp: position.timestamp\n      });\n      setLoading(false);\n    }, err => {\n      let errorMessage = 'Unable to retrieve location';\n      switch (err.code) {\n        case err.PERMISSION_DENIED:\n          errorMessage = 'Location access denied by user';\n          break;\n        case err.POSITION_UNAVAILABLE:\n          errorMessage = 'Location information unavailable';\n          break;\n        case err.TIMEOUT:\n          errorMessage = 'Location request timed out';\n          break;\n        default:\n          errorMessage = 'Unknown location error';\n          break;\n      }\n      setError(errorMessage);\n      setLoading(false);\n    }, defaultOptions);\n  }, [defaultOptions]);\n  const watchPosition = useCallback(() => {\n    if (!navigator.geolocation) {\n      setError('Geolocation is not supported by this browser');\n      return null;\n    }\n    setLoading(true);\n    setError(null);\n    const watchId = navigator.geolocation.watchPosition(position => {\n      const {\n        latitude,\n        longitude\n      } = position.coords;\n      setLocation({\n        lat: latitude,\n        lng: longitude,\n        accuracy: position.coords.accuracy,\n        timestamp: position.timestamp\n      });\n      setLoading(false);\n    }, err => {\n      let errorMessage = 'Unable to retrieve location';\n      switch (err.code) {\n        case err.PERMISSION_DENIED:\n          errorMessage = 'Location access denied by user';\n          break;\n        case err.POSITION_UNAVAILABLE:\n          errorMessage = 'Location information unavailable';\n          break;\n        case err.TIMEOUT:\n          errorMessage = 'Location request timed out';\n          break;\n        default:\n          errorMessage = 'Unknown location error';\n          break;\n      }\n      setError(errorMessage);\n      setLoading(false);\n    }, defaultOptions);\n    return watchId;\n  }, [defaultOptions]);\n  const clearWatch = useCallback(watchId => {\n    if (watchId && navigator.geolocation) {\n      navigator.geolocation.clearWatch(watchId);\n    }\n  }, []);\n\n  // Auto-get location on mount if requested\n  useEffect(() => {\n    if (options.autoStart) {\n      getCurrentPosition();\n    }\n  }, [getCurrentPosition, options.autoStart]);\n  return {\n    location,\n    error,\n    loading,\n    getCurrentPosition,\n    watchPosition,\n    clearWatch,\n    isSupported: !!navigator.geolocation\n  };\n};\n_s(useGeolocation, \"P62Kx5ltgboXG1ZaCNhp2XmoR+g=\");\nexport default useGeolocation;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useGeolocation", "options", "_s", "location", "setLocation", "error", "setError", "loading", "setLoading", "defaultOptions", "enableHighAccuracy", "timeout", "maximumAge", "getCurrentPosition", "navigator", "geolocation", "position", "latitude", "longitude", "coords", "lat", "lng", "accuracy", "timestamp", "err", "errorMessage", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "watchPosition", "watchId", "clearWatch", "autoStart", "isSupported"], "sources": ["c:/laragon/www/basketcase/client/src/hooks/useGeolocation.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\n\nconst useGeolocation = (options = {}) => {\n  const [location, setLocation] = useState(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  const defaultOptions = {\n    enableHighAccuracy: true,\n    timeout: 10000,\n    maximumAge: 300000, // 5 minutes\n    ...options,\n  };\n\n  const getCurrentPosition = useCallback(() => {\n    if (!navigator.geolocation) {\n      setError('Geolocation is not supported by this browser');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    navigator.geolocation.getCurrentPosition(\n      (position) => {\n        const { latitude, longitude } = position.coords;\n        setLocation({\n          lat: latitude,\n          lng: longitude,\n          accuracy: position.coords.accuracy,\n          timestamp: position.timestamp,\n        });\n        setLoading(false);\n      },\n      (err) => {\n        let errorMessage = 'Unable to retrieve location';\n        \n        switch (err.code) {\n          case err.PERMISSION_DENIED:\n            errorMessage = 'Location access denied by user';\n            break;\n          case err.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information unavailable';\n            break;\n          case err.TIMEOUT:\n            errorMessage = 'Location request timed out';\n            break;\n          default:\n            errorMessage = 'Unknown location error';\n            break;\n        }\n        \n        setError(errorMessage);\n        setLoading(false);\n      },\n      defaultOptions\n    );\n  }, [defaultOptions]);\n\n  const watchPosition = useCallback(() => {\n    if (!navigator.geolocation) {\n      setError('Geolocation is not supported by this browser');\n      return null;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    const watchId = navigator.geolocation.watchPosition(\n      (position) => {\n        const { latitude, longitude } = position.coords;\n        setLocation({\n          lat: latitude,\n          lng: longitude,\n          accuracy: position.coords.accuracy,\n          timestamp: position.timestamp,\n        });\n        setLoading(false);\n      },\n      (err) => {\n        let errorMessage = 'Unable to retrieve location';\n        \n        switch (err.code) {\n          case err.PERMISSION_DENIED:\n            errorMessage = 'Location access denied by user';\n            break;\n          case err.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information unavailable';\n            break;\n          case err.TIMEOUT:\n            errorMessage = 'Location request timed out';\n            break;\n          default:\n            errorMessage = 'Unknown location error';\n            break;\n        }\n        \n        setError(errorMessage);\n        setLoading(false);\n      },\n      defaultOptions\n    );\n\n    return watchId;\n  }, [defaultOptions]);\n\n  const clearWatch = useCallback((watchId) => {\n    if (watchId && navigator.geolocation) {\n      navigator.geolocation.clearWatch(watchId);\n    }\n  }, []);\n\n  // Auto-get location on mount if requested\n  useEffect(() => {\n    if (options.autoStart) {\n      getCurrentPosition();\n    }\n  }, [getCurrentPosition, options.autoStart]);\n\n  return {\n    location,\n    error,\n    loading,\n    getCurrentPosition,\n    watchPosition,\n    clearWatch,\n    isSupported: !!navigator.geolocation,\n  };\n};\n\nexport default useGeolocation;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAExD,MAAMC,cAAc,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMY,cAAc,GAAG;IACrBC,kBAAkB,EAAE,IAAI;IACxBC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,MAAM;IAAE;IACpB,GAAGX;EACL,CAAC;EAED,MAAMY,kBAAkB,GAAGd,WAAW,CAAC,MAAM;IAC3C,IAAI,CAACe,SAAS,CAACC,WAAW,EAAE;MAC1BT,QAAQ,CAAC,8CAA8C,CAAC;MACxD;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IAEdQ,SAAS,CAACC,WAAW,CAACF,kBAAkB,CACrCG,QAAQ,IAAK;MACZ,MAAM;QAAEC,QAAQ;QAAEC;MAAU,CAAC,GAAGF,QAAQ,CAACG,MAAM;MAC/Cf,WAAW,CAAC;QACVgB,GAAG,EAAEH,QAAQ;QACbI,GAAG,EAAEH,SAAS;QACdI,QAAQ,EAAEN,QAAQ,CAACG,MAAM,CAACG,QAAQ;QAClCC,SAAS,EAAEP,QAAQ,CAACO;MACtB,CAAC,CAAC;MACFf,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EACAgB,GAAG,IAAK;MACP,IAAIC,YAAY,GAAG,6BAA6B;MAEhD,QAAQD,GAAG,CAACE,IAAI;QACd,KAAKF,GAAG,CAACG,iBAAiB;UACxBF,YAAY,GAAG,gCAAgC;UAC/C;QACF,KAAKD,GAAG,CAACI,oBAAoB;UAC3BH,YAAY,GAAG,kCAAkC;UACjD;QACF,KAAKD,GAAG,CAACK,OAAO;UACdJ,YAAY,GAAG,4BAA4B;UAC3C;QACF;UACEA,YAAY,GAAG,wBAAwB;UACvC;MACJ;MAEAnB,QAAQ,CAACmB,YAAY,CAAC;MACtBjB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EACDC,cACF,CAAC;EACH,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAMqB,aAAa,GAAG/B,WAAW,CAAC,MAAM;IACtC,IAAI,CAACe,SAAS,CAACC,WAAW,EAAE;MAC1BT,QAAQ,CAAC,8CAA8C,CAAC;MACxD,OAAO,IAAI;IACb;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IAEd,MAAMyB,OAAO,GAAGjB,SAAS,CAACC,WAAW,CAACe,aAAa,CAChDd,QAAQ,IAAK;MACZ,MAAM;QAAEC,QAAQ;QAAEC;MAAU,CAAC,GAAGF,QAAQ,CAACG,MAAM;MAC/Cf,WAAW,CAAC;QACVgB,GAAG,EAAEH,QAAQ;QACbI,GAAG,EAAEH,SAAS;QACdI,QAAQ,EAAEN,QAAQ,CAACG,MAAM,CAACG,QAAQ;QAClCC,SAAS,EAAEP,QAAQ,CAACO;MACtB,CAAC,CAAC;MACFf,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EACAgB,GAAG,IAAK;MACP,IAAIC,YAAY,GAAG,6BAA6B;MAEhD,QAAQD,GAAG,CAACE,IAAI;QACd,KAAKF,GAAG,CAACG,iBAAiB;UACxBF,YAAY,GAAG,gCAAgC;UAC/C;QACF,KAAKD,GAAG,CAACI,oBAAoB;UAC3BH,YAAY,GAAG,kCAAkC;UACjD;QACF,KAAKD,GAAG,CAACK,OAAO;UACdJ,YAAY,GAAG,4BAA4B;UAC3C;QACF;UACEA,YAAY,GAAG,wBAAwB;UACvC;MACJ;MAEAnB,QAAQ,CAACmB,YAAY,CAAC;MACtBjB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EACDC,cACF,CAAC;IAED,OAAOsB,OAAO;EAChB,CAAC,EAAE,CAACtB,cAAc,CAAC,CAAC;EAEpB,MAAMuB,UAAU,GAAGjC,WAAW,CAAEgC,OAAO,IAAK;IAC1C,IAAIA,OAAO,IAAIjB,SAAS,CAACC,WAAW,EAAE;MACpCD,SAAS,CAACC,WAAW,CAACiB,UAAU,CAACD,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjC,SAAS,CAAC,MAAM;IACd,IAAIG,OAAO,CAACgC,SAAS,EAAE;MACrBpB,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEZ,OAAO,CAACgC,SAAS,CAAC,CAAC;EAE3C,OAAO;IACL9B,QAAQ;IACRE,KAAK;IACLE,OAAO;IACPM,kBAAkB;IAClBiB,aAAa;IACbE,UAAU;IACVE,WAAW,EAAE,CAAC,CAACpB,SAAS,CAACC;EAC3B,CAAC;AACH,CAAC;AAACb,EAAA,CA9HIF,cAAc;AAgIpB,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}