declare class StoreLocationDto {
    type: string;
    coordinates: [number, number];
}
declare class StoreAddressDto {
    street: string;
    city: string;
    province: string;
    postalCode?: string;
    country?: string;
}
declare class StoreContactDto {
    phone?: string;
    email?: string;
    website?: string;
}
declare class OperatingHoursDto {
    open?: string;
    close?: string;
}
declare class StoreOperatingHoursDto {
    monday?: OperatingHoursDto;
    tuesday?: OperatingHoursDto;
    wednesday?: OperatingHoursDto;
    thursday?: OperatingHoursDto;
    friday?: OperatingHoursDto;
    saturday?: OperatingHoursDto;
    sunday?: OperatingHoursDto;
}
export declare class CreateStoreDto {
    name: string;
    branch: string;
    location?: StoreLocationDto;
    address: StoreAddressDto;
    contact?: StoreContactDto;
    operatingHours?: StoreOperatingHoursDto;
    isActive?: boolean;
}
export {};
