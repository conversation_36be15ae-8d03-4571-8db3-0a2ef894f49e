"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStoreDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class StoreLocationDto {
    constructor() {
        this.type = 'Point';
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location type', default: 'Point' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreLocationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Coordinates [longitude, latitude]', example: [28.0473, -26.1076] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], StoreLocationDto.prototype, "coordinates", void 0);
class StoreAddressDto {
    constructor() {
        this.country = 'South Africa';
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Street address' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreAddressDto.prototype, "street", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'City' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreAddressDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Province' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreAddressDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Postal code' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreAddressDto.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Country', default: 'South Africa' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreAddressDto.prototype, "country", void 0);
class StoreContactDto {
}
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Phone number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreContactDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Email address' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreContactDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Website URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StoreContactDto.prototype, "website", void 0);
class OperatingHoursDto {
}
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Opening time' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OperatingHoursDto.prototype, "open", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Closing time' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OperatingHoursDto.prototype, "close", void 0);
class StoreOperatingHoursDto {
}
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Monday hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OperatingHoursDto),
    __metadata("design:type", OperatingHoursDto)
], StoreOperatingHoursDto.prototype, "monday", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tuesday hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OperatingHoursDto),
    __metadata("design:type", OperatingHoursDto)
], StoreOperatingHoursDto.prototype, "tuesday", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Wednesday hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OperatingHoursDto),
    __metadata("design:type", OperatingHoursDto)
], StoreOperatingHoursDto.prototype, "wednesday", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Thursday hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OperatingHoursDto),
    __metadata("design:type", OperatingHoursDto)
], StoreOperatingHoursDto.prototype, "thursday", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Friday hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OperatingHoursDto),
    __metadata("design:type", OperatingHoursDto)
], StoreOperatingHoursDto.prototype, "friday", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Saturday hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OperatingHoursDto),
    __metadata("design:type", OperatingHoursDto)
], StoreOperatingHoursDto.prototype, "saturday", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Sunday hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => OperatingHoursDto),
    __metadata("design:type", OperatingHoursDto)
], StoreOperatingHoursDto.prototype, "sunday", void 0);
class CreateStoreDto {
}
exports.CreateStoreDto = CreateStoreDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store name', example: 'SPAR' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store branch', example: 'Sandton City' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateStoreDto.prototype, "branch", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Store location coordinates' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => StoreLocationDto),
    __metadata("design:type", StoreLocationDto)
], CreateStoreDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store address' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => StoreAddressDto),
    __metadata("design:type", StoreAddressDto)
], CreateStoreDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Store contact information' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => StoreContactDto),
    __metadata("design:type", StoreContactDto)
], CreateStoreDto.prototype, "contact", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Store operating hours' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => StoreOperatingHoursDto),
    __metadata("design:type", StoreOperatingHoursDto)
], CreateStoreDto.prototype, "operatingHours", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Is store active', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateStoreDto.prototype, "isActive", void 0);
//# sourceMappingURL=create-store.dto.js.map