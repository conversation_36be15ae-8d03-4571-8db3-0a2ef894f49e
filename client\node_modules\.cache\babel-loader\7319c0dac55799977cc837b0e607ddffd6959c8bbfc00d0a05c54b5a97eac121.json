{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport SearchBar from './SearchBar';\nimport LocationSelector from './LocationSelector';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  const handleSearch = (query, filters) => {\n    const searchParams = new URLSearchParams();\n    if (query) searchParams.set('q', query);\n    if (filters.category) searchParams.set('category', filters.category);\n    if (filters.brand) searchParams.set('brand', filters.brand);\n    navigate(`/?${searchParams.toString()}`);\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-brand\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"brand-link\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"brand-name\",\n            children: \"BasketCase\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-search\",\n        children: /*#__PURE__*/_jsxDEV(SearchBar, {\n          onSearch: handleSearch\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-location\",\n        children: /*#__PURE__*/_jsxDEV(LocationSelector, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: `header-nav ${isMenuOpen ? 'nav-open' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"nav-link\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/compare\",\n              className: \"nav-link\",\n              children: \"Compare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/stores\",\n              className: \"nav-link\",\n              children: \"Stores\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"menu-toggle\",\n        onClick: toggleMenu,\n        \"aria-label\": \"Toggle menu\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hamburger\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hamburger\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hamburger\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"5bDz1P/CDiQaTeCGRGLL1agRZDk=\", false, function () {\n  return [useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "SearchBar", "LocationSelector", "jsxDEV", "_jsxDEV", "Header", "_s", "isMenuOpen", "setIsMenuOpen", "navigate", "handleSearch", "query", "filters", "searchParams", "URLSearchParams", "set", "category", "brand", "toString", "toggleMenu", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSearch", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/client/src/components/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport SearchBar from './SearchBar';\nimport LocationSelector from './LocationSelector';\nimport './Header.css';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSearch = (query, filters) => {\n    const searchParams = new URLSearchParams();\n    if (query) searchParams.set('q', query);\n    if (filters.category) searchParams.set('category', filters.category);\n    if (filters.brand) searchParams.set('brand', filters.brand);\n    \n    navigate(`/?${searchParams.toString()}`);\n  };\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"header-container\">\n        {/* Logo and Brand */}\n        <div className=\"header-brand\">\n          <Link to=\"/\" className=\"brand-link\">\n            <div className=\"logo\">\n              🛒\n            </div>\n            <span className=\"brand-name\">BasketCase</span>\n          </Link>\n        </div>\n\n        {/* Search Bar */}\n        <div className=\"header-search\">\n          <SearchBar onSearch={handleSearch} />\n        </div>\n\n        {/* Location Selector */}\n        <div className=\"header-location\">\n          <LocationSelector />\n        </div>\n\n        {/* Navigation Menu */}\n        <nav className={`header-nav ${isMenuOpen ? 'nav-open' : ''}`}>\n          <ul className=\"nav-list\">\n            <li className=\"nav-item\">\n              <Link to=\"/\" className=\"nav-link\">\n                Home\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link to=\"/compare\" className=\"nav-link\">\n                Compare\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link to=\"/stores\" className=\"nav-link\">\n                Stores\n              </Link>\n            </li>\n          </ul>\n        </nav>\n\n        {/* Mobile Menu Toggle */}\n        <button \n          className=\"menu-toggle\"\n          onClick={toggleMenu}\n          aria-label=\"Toggle menu\"\n        >\n          <span className=\"hamburger\"></span>\n          <span className=\"hamburger\"></span>\n          <span className=\"hamburger\"></span>\n        </button>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMW,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,YAAY,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IACvC,MAAMC,YAAY,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC1C,IAAIH,KAAK,EAAEE,YAAY,CAACE,GAAG,CAAC,GAAG,EAAEJ,KAAK,CAAC;IACvC,IAAIC,OAAO,CAACI,QAAQ,EAAEH,YAAY,CAACE,GAAG,CAAC,UAAU,EAAEH,OAAO,CAACI,QAAQ,CAAC;IACpE,IAAIJ,OAAO,CAACK,KAAK,EAAEJ,YAAY,CAACE,GAAG,CAAC,OAAO,EAAEH,OAAO,CAACK,KAAK,CAAC;IAE3DR,QAAQ,CAAC,KAAKI,YAAY,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC1C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBX,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,oBACEH,OAAA;IAAQgB,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBjB,OAAA;MAAKgB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BjB,OAAA;QAAKgB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BjB,OAAA,CAACL,IAAI;UAACuB,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACjCjB,OAAA;YAAKgB,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAEtB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAMgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BjB,OAAA,CAACH,SAAS;UAAC0B,QAAQ,EAAEjB;QAAa;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BjB,OAAA,CAACF,gBAAgB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAE,cAAcb,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;QAAAc,QAAA,eAC3DjB,OAAA;UAAIgB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACtBjB,OAAA;YAAIgB,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBjB,OAAA,CAACL,IAAI;cAACuB,EAAE,EAAC,GAAG;cAACF,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLtB,OAAA;YAAIgB,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBjB,OAAA,CAACL,IAAI;cAACuB,EAAE,EAAC,UAAU;cAACF,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAEzC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLtB,OAAA;YAAIgB,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBjB,OAAA,CAACL,IAAI;cAACuB,EAAE,EAAC,SAAS;cAACF,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAExC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNtB,OAAA;QACEgB,SAAS,EAAC,aAAa;QACvBQ,OAAO,EAAET,UAAW;QACpB,cAAW,aAAa;QAAAE,QAAA,gBAExBjB,OAAA;UAAMgB,SAAS,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnCtB,OAAA;UAAMgB,SAAS,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnCtB,OAAA;UAAMgB,SAAS,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACpB,EAAA,CA1EID,MAAM;EAAA,QAEOL,WAAW;AAAA;AAAA6B,EAAA,GAFxBxB,MAAM;AA4EZ,eAAeA,MAAM;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}