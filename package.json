{"name": "basketcase", "version": "1.0.0", "description": "South African grocery price comparison app", "main": "index.js", "scripts": {"quickstart": "node scripts/quickstart.js", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "start": "concurrently \"npm run server:start\" \"npm run client:start\"", "build": "npm run client:build", "server:dev": "cd server && npm run dev", "server:start": "cd server && npm start", "server:install": "cd server && npm install", "client:dev": "cd client && npm start", "client:start": "cd client && npm start", "client:build": "cd client && npm run build", "client:install": "cd client && npm install", "install:all": "npm run server:install && npm run client:install", "scrape": "cd server && npm run scrape", "scrape:spar": "cd server && node scripts/scraper.js spar", "scrape:checkers": "cd server && node scripts/scraper.js checkers", "setup": "npm run install:all && npm run setup:env", "setup:env": "node scripts/setup-env.js", "setup:stores": "node scripts/create-sample-stores.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:dev:build": "docker-compose -f docker-compose.dev.yml up --build", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:logs": "docker-compose -f docker-compose.dev.yml logs -f", "test": "npm run server:test && npm run client:test", "server:test": "cd server && npm test", "client:test": "cd client && npm test"}, "keywords": ["grocery", "price-comparison", "south-africa", "mern", "react", "nodejs", "mongodb"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/basketcase.git"}}