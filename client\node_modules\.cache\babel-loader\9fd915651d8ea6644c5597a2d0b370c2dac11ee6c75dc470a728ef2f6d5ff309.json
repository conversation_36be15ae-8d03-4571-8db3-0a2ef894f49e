{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\n\n// Import pages\nimport HomePage from './pages/HomePage';\nimport ProductPage from './pages/ProductPage';\nimport ComparePage from './pages/ComparePage';\nimport StoresPage from './pages/StoresPage';\n\n// Import components\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/product/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProductPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/compare\",\n            element: /*#__PURE__*/_jsxDEV(ComparePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/stores\",\n            element: /*#__PURE__*/_jsxDEV(StoresPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "HomePage", "ProductPage", "ComparePage", "StoresPage", "Header", "Footer", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\n\n// Import pages\nimport HomePage from './pages/HomePage';\nimport ProductPage from './pages/ProductPage';\nimport ComparePage from './pages/ComparePage';\nimport StoresPage from './pages/StoresPage';\n\n// Import components\nimport Header from './components/Header';\nimport Footer from './components/Footer';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Header />\n        <main className=\"main-content\">\n          <Routes>\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/product/:id\" element={<ProductPage />} />\n            <Route path=\"/compare\" element={<ComparePage />} />\n            <Route path=\"/stores\" element={<StoresPage />} />\n          </Routes>\n        </main>\n        <Footer />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAO,WAAW;;AAElB;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,MAAM;IAAAY,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA,CAACH,MAAM;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA;QAAMG,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC5BF,OAAA,CAACT,MAAM;UAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;YAACgB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACP,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCP,OAAA,CAACR,KAAK;YAACgB,IAAI,EAAC,cAAc;YAACC,OAAO,eAAET,OAAA,CAACN,WAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDP,OAAA,CAACR,KAAK;YAACgB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAET,OAAA,CAACL,WAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDP,OAAA,CAACR,KAAK;YAACgB,IAAI,EAAC,SAAS;YAACC,OAAO,eAAET,OAAA,CAACJ,UAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPP,OAAA,CAACF,MAAM;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACG,EAAA,GAjBQT,GAAG;AAmBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}