import React from 'react';

const AboutPage: React.FC = () => {
  return (
    <div className="about-page">
      <div className="container py-4">
        <div className="row mb-4">
          <div className="col-12">
            <h1 className="page-title">
              <i className="fas fa-info-circle me-2"></i>
              About BasketCase
            </h1>
            <p className="text-muted">Your smart grocery price comparison platform</p>
          </div>
        </div>

        <div className="row">
          <div className="col-lg-8">
            <div className="card">
              <div className="card-body">
                <h2>Our Mission</h2>
                <p>
                  BasketCase is dedicated to helping South African families save money on their grocery shopping 
                  by providing real-time price comparisons across major retail stores.
                </p>
                
                <h3>What We Do</h3>
                <ul>
                  <li><strong>Price Monitoring:</strong> We track prices across SPAR, Checkers, Pick n Pay, and Woolworths</li>
                  <li><strong>Real-time Updates:</strong> Prices are updated every 30 minutes to ensure accuracy</li>
                  <li><strong>Smart Comparisons:</strong> Find the best deals and biggest savings automatically</li>
                  <li><strong>Store Locator:</strong> Find the nearest stores with the best prices</li>
                </ul>

                <h3>Technology</h3>
                <p>
                  Built with modern web technologies including:
                </p>
                <ul>
                  <li><strong>Backend:</strong> NestJS with TypeScript</li>
                  <li><strong>Frontend:</strong> React with TypeScript</li>
                  <li><strong>Database:</strong> MongoDB</li>
                  <li><strong>Scraping:</strong> Automated web scraping with Puppeteer</li>
                </ul>

                <h3>Contact Us</h3>
                <p>
                  Have questions or suggestions? We'd love to hear from you!
                </p>
                <p>
                  <i className="fas fa-envelope me-2"></i>
                  Email: <EMAIL>
                </p>
              </div>
            </div>
          </div>
          
          <div className="col-lg-4">
            <div className="card">
              <div className="card-body">
                <h5>Quick Stats</h5>
                <div className="stat-item">
                  <div className="stat-number">1000+</div>
                  <div className="stat-label">Products Tracked</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">50+</div>
                  <div className="stat-label">Store Locations</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">4</div>
                  <div className="stat-label">Major Retailers</div>
                </div>
                <div className="stat-item">
                  <div className="stat-number">30min</div>
                  <div className="stat-label">Update Frequency</div>
                </div>
              </div>
            </div>
            
            <div className="card mt-4">
              <div className="card-body">
                <h5>Supported Stores</h5>
                <div className="store-list">
                  <div className="store-item">
                    <i className="fas fa-store me-2"></i>
                    SPAR
                  </div>
                  <div className="store-item">
                    <i className="fas fa-store me-2"></i>
                    Checkers
                  </div>
                  <div className="store-item">
                    <i className="fas fa-store me-2"></i>
                    Pick n Pay <small className="text-muted">(Coming Soon)</small>
                  </div>
                  <div className="store-item">
                    <i className="fas fa-store me-2"></i>
                    Woolworths <small className="text-muted">(Coming Soon)</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
