declare class PriceAvailabilityDto {
    inStock?: boolean;
    stockLevel?: string;
    lastChecked?: Date;
}
declare class PricePromotionDto {
    isOnPromotion?: boolean;
    promotionDescription?: string;
    promotionType?: string;
    validFrom?: Date;
    validUntil?: Date;
}
declare class PriceHistoryDto {
    price: number;
    date: Date;
    source?: string;
}
declare class ScrapingInfoDto {
    lastScraped?: Date;
    sourceUrl?: string;
    scrapingMethod?: string;
    confidence?: number;
}
export declare class CreatePriceDto {
    product: string;
    store: string;
    current: number;
    original?: number;
    currency?: string;
    availability?: PriceAvailabilityDto;
    promotion?: PricePromotionDto;
    priceHistory?: PriceHistoryDto[];
    scrapingInfo?: ScrapingInfoDto;
    isActive?: boolean;
}
export {};
