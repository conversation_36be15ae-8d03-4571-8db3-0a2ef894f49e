import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import ProductGrid from '../components/ProductGrid';
import FilterPanel from '../components/FilterPanel';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import './HomePage.css';

const HomePage = () => {
  const navigate = useNavigate();
  const [products, setProducts] = useState([]);
  const [biggestSavings, setBiggestSavings] = useState([]);
  const [trendingProducts, setTrendingProducts] = useState([]);
  const [promotions, setPromotions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState(null);
  const [filters, setFilters] = useState({
    query: '',
    category: '',
    brand: '',
    province: '',
    city: '',
    sortBy: 'relevance'
  });

  // Load products
  const loadProducts = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...filters
      });

      const response = await fetch(`http://localhost:5000/api/products?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setProducts(data.products || []);
        setPagination(data.pagination);
      } else {
        setError('Failed to load products');
      }
    } catch (err) {
      setError('Failed to load products. Please try again.');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load biggest savings
  const loadBiggestSavings = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/prices/biggest-savings?limit=6');
      const data = await response.json();
      setBiggestSavings(data.biggestSavings || []);
    } catch (err) {
      console.error('Error loading biggest savings:', err);
    }
  };

  // Load trending products
  const loadTrendingProducts = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/prices/trending?days=7');
      const data = await response.json();
      setTrendingProducts(data.trending || []);
    } catch (err) {
      console.error('Error loading trending products:', err);
    }
  };

  // Load promotions
  const loadPromotions = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/prices/promotions');
      const data = await response.json();
      setPromotions(data.promotions || []);
    } catch (err) {
      console.error('Error loading promotions:', err);
    }
  };

  useEffect(() => {
    loadProducts();
    loadBiggestSavings();
    loadTrendingProducts();
    loadPromotions();
  }, []);

  useEffect(() => {
    loadProducts(1);
  }, [filters]);

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handlePageChange = (page) => {
    loadProducts(page);
  };

  const handleProductClick = (product) => {
    navigate(`/product/${product._id}`);
  };

  const hasActiveFilters = filters.query || filters.category || filters.brand;

  return (
    <div className="home-page">
      <div className="home-container">
        
        {/* Hero Section */}
        {!hasActiveFilters && (
          <section className="hero-section">
            <div className="hero-content">
              <h1 className="hero-title">
                Compare Grocery Prices Across South Africa
              </h1>
              <p className="hero-subtitle">
                Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths
              </p>
            </div>
          </section>
        )}

        {/* Featured Sections */}
        {!hasActiveFilters && (
          <div className="container-fluid mb-4">
            {/* Biggest Savings */}
            {biggestSavings.length > 0 && (
              <div className="row mb-4">
                <div className="col-12">
                  <h2 className="text-center mb-4">🔥 Biggest Savings Today</h2>
                  <div className="row">
                    {biggestSavings.map(item => (
                      <div key={`${item.product._id}-${item.store._id}`} className="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <div className="card h-100 border-danger">
                          <div className="card-body text-center">
                            <div className="badge bg-danger mb-2">
                              Save R{item.savings.amount.toFixed(2)} ({item.savings.percentage.toFixed(0)}% off)
                            </div>
                            <h6 className="card-title">{item.product.name}</h6>
                            <p className="card-text small text-muted">{item.product.brand} at {item.store.name}</p>
                            <div className="price-info">
                              <span className="h5 text-success">R{item.price.current.toFixed(2)}</span>
                              <br />
                              <small className="text-decoration-line-through text-muted">was R{item.price.original.toFixed(2)}</small>
                            </div>
                            <button 
                              className="btn btn-sm btn-outline-primary mt-2"
                              onClick={() => handleProductClick(item.product)}
                            >
                              View Deal
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Trending and Promotions */}
            <div className="row mb-4">
              <div className="col-md-6">
                <div className="card">
                  <div className="card-body">
                    <h3 className="card-title">📈 Trending Products</h3>
                    <div className="row">
                      {trendingProducts.slice(0, 3).map(item => (
                        <div key={item._id} className="col-12 mb-2">
                          <div className="d-flex justify-content-between align-items-center p-2 border rounded">
                            <div>
                              <h6 className="mb-1">{item.productName}</h6>
                              <small className="text-muted">Avg: R{item.avgPrice.toFixed(2)}</small>
                            </div>
                            <div className="text-end">
                              <small className="text-muted">R{item.minPrice.toFixed(2)} - R{item.maxPrice.toFixed(2)}</small>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-md-6">
                <div className="card">
                  <div className="card-body">
                    <h3 className="card-title">🏷️ Current Promotions</h3>
                    <div className="row">
                      {promotions.slice(0, 3).map((promo, index) => (
                        <div key={index} className="col-12 mb-2">
                          <div className="d-flex justify-content-between align-items-center p-2 border rounded">
                            <div>
                              <h6 className="mb-1">{promo.product.name}</h6>
                              <small className="text-muted">{promo.store.name}</small>
                            </div>
                            <div className="text-end">
                              <span className="text-success">R{promo.price.current.toFixed(2)}</span>
                              {promo.price.original > promo.price.current && (
                                <>
                                  <br />
                                  <small className="text-decoration-line-through text-muted">R{promo.price.original.toFixed(2)}</small>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="container-fluid">
          <div className="row">
            <div className="col-md-4 col-lg-3">
              <FilterPanel
                filters={filters}
                onFilterChange={handleFilterChange}
              />
            </div>

            <div className="col-md-8 col-lg-9">
              {hasActiveFilters && (
                <div className="search-results-header">
                  <h2 className="results-title">
                    {filters.query ? `Search results for "${filters.query}"` : 'Products'}
                  </h2>
                  {pagination && (
                    <div className="results-info">
                      Showing {((pagination.current - 1) * 20) + 1} - {Math.min(pagination.current * 20, pagination.totalItems)} of {pagination.totalItems} products
                    </div>
                  )}
                </div>
              )}

              {loading && <LoadingSpinner />}
              {error && <ErrorMessage message={error} onRetry={() => loadProducts()} />}

              {!loading && !error && (
                <>
                  {products.length > 0 ? (
                    <ProductGrid
                      products={products}
                      onProductClick={handleProductClick}
                    />
                  ) : hasActiveFilters ? (
                    <div className="no-results">
                      <h3>No products found</h3>
                      <p>Try adjusting your search criteria or filters.</p>
                    </div>
                  ) : null}

                  {pagination && pagination.total > 1 && (
                    <div className="pagination">
                      <button
                        className="pagination-button"
                        disabled={pagination.current <= 1}
                        onClick={() => handlePageChange(pagination.current - 1)}
                      >
                        Previous
                      </button>
                      
                      <span className="pagination-info">
                        Page {pagination.current} of {pagination.total}
                      </span>
                      
                      <button
                        className="pagination-button"
                        disabled={pagination.current >= pagination.total}
                        onClick={() => handlePageChange(pagination.current + 1)}
                      >
                        Next
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
