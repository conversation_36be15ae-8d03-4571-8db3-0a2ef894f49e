import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { productsAPI, pricesAPI } from '../services/api';
import ProductGrid from '../components/ProductGrid';
import FilterPanel from '../components/FilterPanel';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import './HomePage.css';

const HomePage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState(null);
  const [trendingProducts, setTrendingProducts] = useState([]);
  const [promotions, setPromotions] = useState([]);
  const [biggestSavings, setBiggestSavings] = useState([]);

  // Filter states
  const [filters, setFilters] = useState({
    query: searchParams.get('q') || '',
    category: searchParams.get('category') || '',
    brand: searchParams.get('brand') || '',
    sortBy: searchParams.get('sortBy') || 'relevance',
    page: parseInt(searchParams.get('page')) || 1
  });

  // Load products based on current filters
  const loadProducts = async (currentFilters = filters) => {
    setLoading(true);
    setError(null);
    
    try {
      const searchFilters = {
        ...currentFilters,
        limit: 20
      };
      
      // Remove empty filters
      Object.keys(searchFilters).forEach(key => {
        if (!searchFilters[key]) {
          delete searchFilters[key];
        }
      });
      
      const response = await productsAPI.search(searchFilters);
      setProducts(response.products || []);
      setPagination(response.pagination);
      
    } catch (err) {
      setError('Failed to load products. Please try again.');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load trending products
  const loadTrendingProducts = async () => {
    try {
      const response = await pricesAPI.getTrending(7);
      setTrendingProducts(response.trending || []);
    } catch (err) {
      console.error('Error loading trending products:', err);
    }
  };

  // Load current promotions
  const loadPromotions = async () => {
    try {
      const response = await pricesAPI.getPromotions({ limit: 10 });
      setPromotions(response.promotions || []);
    } catch (err) {
      console.error('Error loading promotions:', err);
    }
  };

  // Load biggest savings
  const loadBiggestSavings = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/prices/biggest-savings?limit=6');
      const data = await response.json();
      setBiggestSavings(data.biggestSavings || []);
    } catch (err) {
      console.error('Error loading biggest savings:', err);
    }
  };

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'relevance' && value !== 1) {
        params.set(key, value.toString());
      }
    });
    
    setSearchParams(params);
  }, [filters, setSearchParams]);

  // Load products when filters change
  useEffect(() => {
    loadProducts();
  }, [filters]);

  // Load initial data
  useEffect(() => {
    loadTrendingProducts();
    loadPromotions();
    loadBiggestSavings();
  }, []);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  };

  // Handle page change
  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle product click
  const handleProductClick = (product) => {
    navigate(`/product/${product._id}`);
  };

  // Check if we have active search/filters
  const hasActiveFilters = filters.query || filters.category || filters.brand;

  return (
    <div className="home-page">
      <div className="home-container">
        
        {/* Hero Section - only show when no active search */}
        {!hasActiveFilters && (
          <section className="hero-section">
            <div className="hero-content">
              <h1 className="hero-title">
                Compare Grocery Prices Across South Africa
              </h1>
              <p className="hero-subtitle">
                Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths
              </p>
            </div>
          </section>
        )}

        <div className="main-content">
          {/* Filter Panel */}
          <aside className="filters-sidebar">
            <FilterPanel
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </aside>

          {/* Products Section */}
          <main className="products-section">
            
            {/* Search Results Header */}
            {hasActiveFilters && (
              <div className="search-results-header">
                <h2 className="results-title">
                  {filters.query ? `Search results for "${filters.query}"` : 'Products'}
                </h2>
                
                {pagination && (
                  <div className="results-info">
                    Showing {((pagination.current - 1) * 20) + 1} - {Math.min(pagination.current * 20, pagination.totalItems)} of {pagination.totalItems} products
                  </div>
                )}
              </div>
            )}

            {/* Loading State */}
            {loading && <LoadingSpinner />}

            {/* Error State */}
            {error && <ErrorMessage message={error} onRetry={() => loadProducts()} />}

            {/* Products Grid */}
            {!loading && !error && (
              <>
                {products.length > 0 ? (
                  <ProductGrid
                    products={products}
                    onProductClick={handleProductClick}
                  />
                ) : hasActiveFilters ? (
                  <div className="no-results">
                    <h3>No products found</h3>
                    <p>Try adjusting your search criteria or filters.</p>
                  </div>
                ) : null}

                {/* Pagination */}
                {pagination && pagination.total > 1 && (
                  <div className="pagination">
                    <button
                      className="pagination-button"
                      disabled={pagination.current <= 1}
                      onClick={() => handlePageChange(pagination.current - 1)}
                    >
                      Previous
                    </button>
                    
                    <span className="pagination-info">
                      Page {pagination.current} of {pagination.total}
                    </span>
                    
                    <button
                      className="pagination-button"
                      disabled={pagination.current >= pagination.total}
                      onClick={() => handlePageChange(pagination.current + 1)}
                    >
                      Next
                    </button>
                  </div>
                )}
              </>
            )}
          </main>
        </div>

        {/* Featured Sections - only show when no active search */}
        {!hasActiveFilters && !loading && (
          <section className="featured-sections">

            {/* Biggest Savings */}
            {biggestSavings.length > 0 && (
              <div className="featured-section">
                <h2 className="section-title">🔥 Biggest Savings Today</h2>
                <div className="savings-grid">
                  {biggestSavings.map(item => (
                    <div key={`${item.product._id}-${item.store._id}`} className="savings-item">
                      <div className="savings-badge">
                        Save R{item.savings.amount.toFixed(2)}
                        <span className="savings-percentage">({item.savings.percentage.toFixed(0)}% off)</span>
                      </div>
                      <div className="savings-product">
                        <h4 className="savings-product-name">{item.product.name}</h4>
                        <div className="savings-brand">{item.product.brand}</div>
                        <div className="savings-store">at {item.store.name}</div>
                      </div>
                      <div className="savings-prices">
                        <div className="savings-current-price">R{item.price.current.toFixed(2)}</div>
                        <div className="savings-original-price">was R{item.price.original.toFixed(2)}</div>
                      </div>
                      <button
                        className="view-deal-button"
                        onClick={() => handleProductClick(item.product)}
                      >
                        View Deal
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Bottom Section - Trending and Promotions Side by Side */}
            <div className="bottom-sections">
              {/* Trending Products - Left */}
              <div className="featured-section trending-section">
                <h2 className="section-title">📈 Trending Products (TEST)</h2>
                  <div className="trending-grid">
                    {trendingProducts.slice(0, 6).map(item => (
                      <div key={item._id} className="trending-item">
                        <h3 className="trending-product-name">{item.productName}</h3>
                        <div className="trending-stats">
                          <span className="avg-price">Avg: R{item.avgPrice.toFixed(2)}</span>
                          <span className="price-range">
                            R{item.minPrice.toFixed(2)} - R{item.maxPrice.toFixed(2)}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Current Promotions - Right */}
              <div className="featured-section promotions-section">
                <h2 className="section-title">🏷️ Current Promotions (TEST)</h2>
                  <div className="promotions-grid">
                    {promotions.slice(0, 8).map(promo => (
                      <div key={`${promo.product._id}-${promo.store._id}`} className="promotion-item">
                        <div className="promotion-product">
                          <h4>{promo.product.name}</h4>
                          <span className="promotion-store">{promo.store.name}</span>
                        </div>
                        <div className="promotion-price">
                          <span className="current-price">R{promo.price.current.toFixed(2)}</span>
                          {promo.price.original > promo.price.current && (
                            <span className="original-price">R{promo.price.original.toFixed(2)}</span>
                          )}
                        </div>
                        {promo.promotion.promotionDescription && (
                          <div className="promotion-description">
                            {promo.promotion.promotionDescription}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default HomePage;
