/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: var(--shadow);
  z-index: 1000;
  height: 80px;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 2rem;
}

/* Brand */
.header-brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: var(--primary-color);
}

.logo {
  font-size: 2rem;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: bold;
}

/* Search */
.header-search {
  flex: 1;
  max-width: 500px;
}

/* Location */
.header-location {
  flex-shrink: 0;
}

/* Navigation */
.header-nav {
  flex-shrink: 0;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: var(--dark-color);
  font-weight: 500;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: var(--transition);
}

.nav-link:hover {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  text-decoration: none;
}

/* Mobile Menu Toggle */
.menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  padding: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
}

.hamburger {
  width: 25px;
  height: 3px;
  background-color: var(--dark-color);
  transition: var(--transition);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .header {
    height: 70px;
  }
  
  .header-container {
    gap: 1rem;
  }
  
  .header-search {
    max-width: none;
  }
  
  .header-location {
    display: none;
  }
  
  .menu-toggle {
    display: flex;
  }
  
  .header-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    box-shadow: var(--shadow);
    padding: 1rem;
  }
  
  .header-nav.nav-open {
    display: block;
  }
  
  .nav-list {
    flex-direction: column;
    gap: 1rem;
  }
  
  .brand-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 0.5rem;
  }
  
  .header-search {
    min-width: 0;
  }
}
