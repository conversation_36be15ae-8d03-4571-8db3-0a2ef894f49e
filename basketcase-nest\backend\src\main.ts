import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS
  app.enableCors({
    origin: process.env.CLIENT_URL || 'http://localhost:3000',
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // API prefix
  app.setGlobalPrefix('api');

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('BasketCase API')
    .setDescription('Price comparison platform API')
    .setVersion('1.0')
    .addTag('products', 'Product management')
    .addTag('stores', 'Store management')
    .addTag('prices', 'Price tracking and comparison')
    .addTag('scraping', 'Web scraping operations')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = process.env.PORT || 5000;
  await app.listen(port);

  console.log('🚀 BasketCase API started successfully!');
  console.log(`📖 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`🌐 API Base URL: http://localhost:${port}/api`);
  console.log(`🔗 Frontend URL: ${process.env.CLIENT_URL || 'http://localhost:3000'}`);
}

bootstrap();
