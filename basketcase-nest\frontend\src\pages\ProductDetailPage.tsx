import React from 'react';
import { useParams } from 'react-router-dom';

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="product-detail-page">
      <div className="container py-4">
        <div className="row">
          <div className="col-12">
            <h1 className="page-title">
              <i className="fas fa-box me-2"></i>
              Product Details
            </h1>
            <p className="text-muted">Product ID: {id}</p>
            
            <div className="alert alert-info">
              <i className="fas fa-info-circle me-2"></i>
              Product detail page is under development. This will show detailed product information and price comparisons across stores.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
