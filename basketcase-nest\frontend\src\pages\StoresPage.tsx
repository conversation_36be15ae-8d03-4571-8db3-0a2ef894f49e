import React, { useState, useEffect } from 'react';
import { storesApi } from '@/services/api';
import { Store } from '@/types';

const StoresPage: React.FC = () => {
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStores = async () => {
      try {
        setLoading(true);
        const response = await storesApi.getAll();
        if (response.success) {
          setStores(response.stores || []);
        }
      } catch (error) {
        console.error('Error fetching stores:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, []);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner-border loading-spinner text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="stores-page">
      <div className="container py-4">
        <div className="row mb-4">
          <div className="col-12">
            <h1 className="page-title">
              <i className="fas fa-store me-2"></i>
              Stores
            </h1>
            <p className="text-muted">Find stores near you</p>
          </div>
        </div>

        <div className="row">
          {stores.length > 0 ? (
            stores.map((store) => (
              <div key={store._id} className="col-lg-4 col-md-6 mb-4">
                <div className="card store-card h-100">
                  <div className="card-body">
                    <h5 className="card-title">{store.name}</h5>
                    <h6 className="card-subtitle mb-2 text-muted">{store.branch}</h6>
                    <div className="store-address">
                      <p className="mb-1">
                        <i className="fas fa-map-marker-alt me-2"></i>
                        {store.address.street}
                      </p>
                      <p className="mb-1">
                        {store.address.city}, {store.address.province}
                      </p>
                      {store.address.postalCode && (
                        <p className="mb-1">{store.address.postalCode}</p>
                      )}
                    </div>
                    {store.contact?.phone && (
                      <p className="mb-1">
                        <i className="fas fa-phone me-2"></i>
                        {store.contact.phone}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-12">
              <div className="text-center py-5">
                <i className="fas fa-store fa-3x text-muted mb-3"></i>
                <h4>No stores found</h4>
                <p className="text-muted">Store data is being loaded...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StoresPage;
