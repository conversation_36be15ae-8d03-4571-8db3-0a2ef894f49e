/* Location Selector Styles */
.location-selector {
  position: relative;
}

.location-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.9rem;
  transition: var(--transition);
  min-width: 200px;
}

.location-button:hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.location-icon {
  font-size: 1rem;
  color: var(--primary-color);
}

.location-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  font-size: 0.8rem;
  color: #6c757d;
  transition: var(--transition);
}

.location-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.location-methods {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.location-method {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.method-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
}

.method-label input[type="radio"] {
  width: auto;
  margin: 0;
}

.auto-location-status {
  margin-left: 1.5rem;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.85rem;
}

.status-message.loading {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-message.error {
  background-color: #ffebee;
  color: #d32f2f;
}

.status-message.success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.retry-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: var(--transition);
}

.retry-button:hover {
  background-color: #1e3d72;
}

.manual-location-form {
  margin-left: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.form-group label {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--dark-color);
}

.location-select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
}

.location-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.location-select:disabled {
  background-color: var(--secondary-color);
  cursor: not-allowed;
  opacity: 0.6;
}

.apply-location-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.apply-location-button:hover {
  background-color: #218838;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .location-button {
    min-width: 150px;
    padding: 0.4rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .location-dropdown {
    left: -100px;
    right: -100px;
    max-height: 300px;
  }
  
  .manual-location-form {
    margin-left: 1rem;
  }
}

@media (max-width: 480px) {
  .location-dropdown {
    left: -150px;
    right: -150px;
    padding: 0.75rem;
  }
}
