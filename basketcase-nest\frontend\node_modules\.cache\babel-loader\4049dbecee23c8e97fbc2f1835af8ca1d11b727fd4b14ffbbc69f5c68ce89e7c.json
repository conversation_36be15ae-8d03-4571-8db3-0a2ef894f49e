{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\basketcase-nest\\\\frontend\\\\src\\\\pages\\\\HomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { pricesApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [biggestSavings, setBiggestSavings] = useState([]);\n  const [trendingProducts, setTrendingProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch biggest savings\n        const savingsResponse = await pricesApi.getBiggestSavings(6);\n        if (savingsResponse.success) {\n          setBiggestSavings(savingsResponse.data || []);\n        }\n\n        // Fetch trending products\n        const trendingResponse = await pricesApi.getTrending();\n        if (trendingResponse.success) {\n          setTrendingProducts(trendingResponse.data || []);\n        }\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border loading-spinner text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"hero-title\",\n              children: [\"Compare Grocery Prices\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-warning\",\n                children: \"Save Money\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"hero-subtitle\",\n              children: \"Find the best deals across South African grocery stores. Compare prices from SPAR, Checkers, Pick n Pay, and more.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hero-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products\",\n                className: \"btn btn-light btn-lg me-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-search me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this), \"Start Comparing\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/stores\",\n                className: \"btn btn-outline-light btn-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-store me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this), \"Find Stores\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hero-stats\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-card\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"stat-number\",\n                      children: \"1000+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 79,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"stat-label\",\n                      children: \"Products\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 80,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-card\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"stat-number\",\n                      children: \"50+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 85,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"stat-label\",\n                      children: \"Stores\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-card\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"stat-number\",\n                      children: \"R500+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 91,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"stat-label\",\n                      children: \"Avg Savings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 92,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-fire text-danger me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), \"Biggest Savings Right Now\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Don't miss these amazing deals!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: biggestSavings.slice(0, 6).map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-md-6 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card savings-card h-100\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"card-title\",\n                      children: item.product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 122,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted mb-1\",\n                      children: item.product.brand\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-store me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 125,\n                        columnNumber: 27\n                      }, this), item.store.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-danger\",\n                    children: [\"Save R\", item.savings.amount.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"current-price\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"h4 text-success\",\n                      children: [\"R\", item.price.current.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"original-price ms-2\",\n                      children: [\"R\", item.price.original.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"savings-percentage\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-success\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-arrow-down me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 141,\n                        columnNumber: 27\n                      }, this), item.savings.percentage.toFixed(1), \"% off\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-primary btn-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-eye me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \"View All Deals\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5 bg-light\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-trending-up text-primary me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), \"Trending Products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Most compared products this week\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: trendingProducts.slice(0, 4).map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-3 col-md-6 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card trending-card h-100\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"trending-rank\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-primary rounded-pill\",\n                    children: [\"#\", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"card-title mt-3\",\n                  children: product.productName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-range\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"avg-price\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"h5 text-primary\",\n                      children: [\"R\", product.avgPrice.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted d-block\",\n                      children: \"Average Price\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-range-info mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [\"Range: R\", product.minPrice.toFixed(2), \" - R\", product.maxPrice.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)\n          }, product._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section-title\",\n              children: \"Why Choose BasketCase?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Save time and money with our smart comparison tools\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-md-6 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-sync-alt text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Real-time Updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"Prices updated every 30 minutes to ensure you get the latest deals.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-md-6 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-map-marker-alt text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Store Locator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"Find the nearest stores with the best prices in your area.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-md-6 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-chart-line text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Price History\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"Track price trends and know when to buy for maximum savings.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"tnfzH6Q7xxRFYasX3+8d2ihNlNg=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "pricesApi", "jsxDEV", "_jsxDEV", "HomePage", "_s", "biggestSavings", "setBiggestSavings", "trendingProducts", "setTrendingProducts", "loading", "setLoading", "fetchData", "savingsResponse", "getBiggestSavings", "success", "data", "trendingResponse", "getTrending", "error", "console", "className", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "slice", "map", "item", "index", "product", "name", "brand", "store", "savings", "amount", "toFixed", "price", "current", "original", "percentage", "productName", "avgPrice", "minPrice", "maxPrice", "_id", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/basketcase-nest/frontend/src/pages/HomePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { pricesApi } from '../services/api';\nimport { SavingsItem, TrendingProduct } from '../types';\n\nconst HomePage: React.FC = () => {\n  const [biggestSavings, setBiggestSavings] = useState<SavingsItem[]>([]);\n  const [trendingProducts, setTrendingProducts] = useState<TrendingProduct[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        \n        // Fetch biggest savings\n        const savingsResponse = await pricesApi.getBiggestSavings(6);\n        if (savingsResponse.success) {\n          setBiggestSavings(savingsResponse.data || []);\n        }\n\n        // Fetch trending products\n        const trendingResponse = await pricesApi.getTrending();\n        if (trendingResponse.success) {\n          setTrendingProducts(trendingResponse.data || []);\n        }\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"spinner-border loading-spinner text-primary\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"home-page\">\n      {/* Hero Section */}\n      <section className=\"hero-section\">\n        <div className=\"container\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-lg-6\">\n              <h1 className=\"hero-title\">\n                Compare Grocery Prices\n                <br />\n                <span className=\"text-warning\">Save Money</span>\n              </h1>\n              <p className=\"hero-subtitle\">\n                Find the best deals across South African grocery stores. \n                Compare prices from SPAR, Checkers, Pick n Pay, and more.\n              </p>\n              <div className=\"hero-buttons\">\n                <Link to=\"/products\" className=\"btn btn-light btn-lg me-3\">\n                  <i className=\"fas fa-search me-2\"></i>\n                  Start Comparing\n                </Link>\n                <Link to=\"/stores\" className=\"btn btn-outline-light btn-lg\">\n                  <i className=\"fas fa-store me-2\"></i>\n                  Find Stores\n                </Link>\n              </div>\n            </div>\n            <div className=\"col-lg-6\">\n              <div className=\"hero-stats\">\n                <div className=\"row text-center\">\n                  <div className=\"col-4\">\n                    <div className=\"stat-card\">\n                      <h3 className=\"stat-number\">1000+</h3>\n                      <p className=\"stat-label\">Products</p>\n                    </div>\n                  </div>\n                  <div className=\"col-4\">\n                    <div className=\"stat-card\">\n                      <h3 className=\"stat-number\">50+</h3>\n                      <p className=\"stat-label\">Stores</p>\n                    </div>\n                  </div>\n                  <div className=\"col-4\">\n                    <div className=\"stat-card\">\n                      <h3 className=\"stat-number\">R500+</h3>\n                      <p className=\"stat-label\">Avg Savings</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Biggest Savings Section */}\n      <section className=\"py-5\">\n        <div className=\"container\">\n          <div className=\"row mb-4\">\n            <div className=\"col-12\">\n              <h2 className=\"section-title\">\n                <i className=\"fas fa-fire text-danger me-2\"></i>\n                Biggest Savings Right Now\n              </h2>\n              <p className=\"text-muted\">Don't miss these amazing deals!</p>\n            </div>\n          </div>\n          \n          <div className=\"row\">\n            {biggestSavings.slice(0, 6).map((item, index) => (\n              <div key={index} className=\"col-lg-4 col-md-6 mb-4\">\n                <div className=\"card savings-card h-100\">\n                  <div className=\"card-body\">\n                    <div className=\"d-flex justify-content-between align-items-start mb-3\">\n                      <div>\n                        <h5 className=\"card-title\">{item.product.name}</h5>\n                        <p className=\"text-muted mb-1\">{item.product.brand}</p>\n                        <small className=\"text-muted\">\n                          <i className=\"fas fa-store me-1\"></i>\n                          {item.store.name}\n                        </small>\n                      </div>\n                      <span className=\"badge bg-danger\">\n                        Save R{item.savings.amount.toFixed(2)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"price-info\">\n                      <div className=\"current-price\">\n                        <span className=\"h4 text-success\">R{item.price.current.toFixed(2)}</span>\n                        <span className=\"original-price ms-2\">R{item.price.original.toFixed(2)}</span>\n                      </div>\n                      <div className=\"savings-percentage\">\n                        <small className=\"text-success\">\n                          <i className=\"fas fa-arrow-down me-1\"></i>\n                          {item.savings.percentage.toFixed(1)}% off\n                        </small>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          \n          <div className=\"text-center\">\n            <Link to=\"/products\" className=\"btn btn-primary btn-lg\">\n              <i className=\"fas fa-eye me-2\"></i>\n              View All Deals\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Trending Products Section */}\n      <section className=\"py-5 bg-light\">\n        <div className=\"container\">\n          <div className=\"row mb-4\">\n            <div className=\"col-12\">\n              <h2 className=\"section-title\">\n                <i className=\"fas fa-trending-up text-primary me-2\"></i>\n                Trending Products\n              </h2>\n              <p className=\"text-muted\">Most compared products this week</p>\n            </div>\n          </div>\n          \n          <div className=\"row\">\n            {trendingProducts.slice(0, 4).map((product, index) => (\n              <div key={product._id} className=\"col-lg-3 col-md-6 mb-4\">\n                <div className=\"card trending-card h-100\">\n                  <div className=\"card-body text-center\">\n                    <div className=\"trending-rank\">\n                      <span className=\"badge bg-primary rounded-pill\">#{index + 1}</span>\n                    </div>\n                    <h5 className=\"card-title mt-3\">{product.productName}</h5>\n                    <div className=\"price-range\">\n                      <div className=\"avg-price\">\n                        <span className=\"h5 text-primary\">R{product.avgPrice.toFixed(2)}</span>\n                        <small className=\"text-muted d-block\">Average Price</small>\n                      </div>\n                      <div className=\"price-range-info mt-2\">\n                        <small className=\"text-muted\">\n                          Range: R{product.minPrice.toFixed(2)} - R{product.maxPrice.toFixed(2)}\n                        </small>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-5\">\n        <div className=\"container\">\n          <div className=\"row mb-4\">\n            <div className=\"col-12 text-center\">\n              <h2 className=\"section-title\">Why Choose BasketCase?</h2>\n              <p className=\"text-muted\">Save time and money with our smart comparison tools</p>\n            </div>\n          </div>\n          \n          <div className=\"row\">\n            <div className=\"col-lg-4 col-md-6 mb-4\">\n              <div className=\"feature-card text-center\">\n                <div className=\"feature-icon\">\n                  <i className=\"fas fa-sync-alt text-primary\"></i>\n                </div>\n                <h4>Real-time Updates</h4>\n                <p className=\"text-muted\">\n                  Prices updated every 30 minutes to ensure you get the latest deals.\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"col-lg-4 col-md-6 mb-4\">\n              <div className=\"feature-card text-center\">\n                <div className=\"feature-icon\">\n                  <i className=\"fas fa-map-marker-alt text-primary\"></i>\n                </div>\n                <h4>Store Locator</h4>\n                <p className=\"text-muted\">\n                  Find the nearest stores with the best prices in your area.\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"col-lg-4 col-md-6 mb-4\">\n              <div className=\"feature-card text-center\">\n                <div className=\"feature-icon\">\n                  <i className=\"fas fa-chart-line text-primary\"></i>\n                </div>\n                <h4>Price History</h4>\n                <p className=\"text-muted\">\n                  Track price trends and know when to buy for maximum savings.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG5C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGT,QAAQ,CAAgB,EAAE,CAAC;EACvE,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAoB,EAAE,CAAC;EAC/E,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAME,eAAe,GAAG,MAAMZ,SAAS,CAACa,iBAAiB,CAAC,CAAC,CAAC;QAC5D,IAAID,eAAe,CAACE,OAAO,EAAE;UAC3BR,iBAAiB,CAACM,eAAe,CAACG,IAAI,IAAI,EAAE,CAAC;QAC/C;;QAEA;QACA,MAAMC,gBAAgB,GAAG,MAAMhB,SAAS,CAACiB,WAAW,CAAC,CAAC;QACtD,IAAID,gBAAgB,CAACF,OAAO,EAAE;UAC5BN,mBAAmB,CAACQ,gBAAgB,CAACD,IAAI,IAAI,EAAE,CAAC;QAClD;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKkB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCnB,OAAA;QAAKkB,SAAS,EAAC,6CAA6C;QAACE,IAAI,EAAC,QAAQ;QAAAD,QAAA,eACxEnB,OAAA;UAAMkB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExB,OAAA;IAAKkB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnB,OAAA;MAASkB,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC/BnB,OAAA;QAAKkB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBnB,OAAA;UAAKkB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCnB,OAAA;YAAKkB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBnB,OAAA;cAAIkB,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,wBAEzB,eAAAnB,OAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxB,OAAA;gBAAMkB,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACLxB,OAAA;cAAGkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAG7B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJxB,OAAA;cAAKkB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnB,OAAA,CAACH,IAAI;gBAAC4B,EAAE,EAAC,WAAW;gBAACP,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxDnB,OAAA;kBAAGkB,SAAS,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mBAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPxB,OAAA,CAACH,IAAI;gBAAC4B,EAAE,EAAC,SAAS;gBAACP,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBACzDnB,OAAA;kBAAGkB,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxB,OAAA;YAAKkB,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBnB,OAAA;cAAKkB,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBnB,OAAA;gBAAKkB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BnB,OAAA;kBAAKkB,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpBnB,OAAA;oBAAKkB,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBnB,OAAA;sBAAIkB,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtCxB,OAAA;sBAAGkB,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxB,OAAA;kBAAKkB,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpBnB,OAAA;oBAAKkB,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBnB,OAAA;sBAAIkB,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpCxB,OAAA;sBAAGkB,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxB,OAAA;kBAAKkB,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpBnB,OAAA;oBAAKkB,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBnB,OAAA;sBAAIkB,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtCxB,OAAA;sBAAGkB,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxB,OAAA;MAASkB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBnB,OAAA;QAAKkB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBnB,OAAA;UAAKkB,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnB,OAAA;YAAKkB,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBnB,OAAA;cAAIkB,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC3BnB,OAAA;gBAAGkB,SAAS,EAAC;cAA8B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6BAElD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxB,OAAA;cAAGkB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAA+B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxB,OAAA;UAAKkB,SAAS,EAAC,KAAK;UAAAC,QAAA,EACjBhB,cAAc,CAACuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC1C7B,OAAA;YAAiBkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACjDnB,OAAA;cAAKkB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACtCnB,OAAA;gBAAKkB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnB,OAAA;kBAAKkB,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,gBACpEnB,OAAA;oBAAAmB,QAAA,gBACEnB,OAAA;sBAAIkB,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAES,IAAI,CAACE,OAAO,CAACC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACnDxB,OAAA;sBAAGkB,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAES,IAAI,CAACE,OAAO,CAACE;oBAAK;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvDxB,OAAA;sBAAOkB,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBAC3BnB,OAAA;wBAAGkB,SAAS,EAAC;sBAAmB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACpCI,IAAI,CAACK,KAAK,CAACF,IAAI;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNxB,OAAA;oBAAMkB,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,GAAC,QAC1B,EAACS,IAAI,CAACM,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENxB,OAAA;kBAAKkB,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBnB,OAAA;oBAAKkB,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BnB,OAAA;sBAAMkB,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,GAAC,GAAC,EAACS,IAAI,CAACS,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzExB,OAAA;sBAAMkB,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,GAAC,GAAC,EAACS,IAAI,CAACS,KAAK,CAACE,QAAQ,CAACH,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eACNxB,OAAA;oBAAKkB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,eACjCnB,OAAA;sBAAOkB,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC7BnB,OAAA;wBAAGkB,SAAS,EAAC;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACzCI,IAAI,CAACM,OAAO,CAACM,UAAU,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAC,OACtC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA9BEK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxB,OAAA;UAAKkB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BnB,OAAA,CAACH,IAAI;YAAC4B,EAAE,EAAC,WAAW;YAACP,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrDnB,OAAA;cAAGkB,SAAS,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,kBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxB,OAAA;MAASkB,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCnB,OAAA;QAAKkB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBnB,OAAA;UAAKkB,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnB,OAAA;YAAKkB,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBnB,OAAA;cAAIkB,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC3BnB,OAAA;gBAAGkB,SAAS,EAAC;cAAsC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAE1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxB,OAAA;cAAGkB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxB,OAAA;UAAKkB,SAAS,EAAC,KAAK;UAAAC,QAAA,EACjBd,gBAAgB,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACG,OAAO,EAAED,KAAK,kBAC/C7B,OAAA;YAAuBkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACvDnB,OAAA;cAAKkB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCnB,OAAA;gBAAKkB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCnB,OAAA;kBAAKkB,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BnB,OAAA;oBAAMkB,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,GAAC,GAAC,EAACU,KAAK,GAAG,CAAC;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNxB,OAAA;kBAAIkB,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEW,OAAO,CAACW;gBAAW;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1DxB,OAAA;kBAAKkB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BnB,OAAA;oBAAKkB,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBnB,OAAA;sBAAMkB,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,GAAC,GAAC,EAACW,OAAO,CAACY,QAAQ,CAACN,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACvExB,OAAA;sBAAOkB,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACNxB,OAAA;oBAAKkB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eACpCnB,OAAA;sBAAOkB,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,UACpB,EAACW,OAAO,CAACa,QAAQ,CAACP,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI,EAACN,OAAO,CAACc,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAnBEM,OAAO,CAACe,GAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBhB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxB,OAAA;MAASkB,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBnB,OAAA;QAAKkB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBnB,OAAA;UAAKkB,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnB,OAAA;YAAKkB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCnB,OAAA;cAAIkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDxB,OAAA;cAAGkB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxB,OAAA;UAAKkB,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnB,OAAA;YAAKkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACrCnB,OAAA;cAAKkB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCnB,OAAA;gBAAKkB,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BnB,OAAA;kBAAGkB,SAAS,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNxB,OAAA;gBAAAmB,QAAA,EAAI;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BxB,OAAA;gBAAGkB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE1B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAKkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACrCnB,OAAA;cAAKkB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCnB,OAAA;gBAAKkB,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BnB,OAAA;kBAAGkB,SAAS,EAAC;gBAAoC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNxB,OAAA;gBAAAmB,QAAA,EAAI;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBxB,OAAA;gBAAGkB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE1B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAKkB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACrCnB,OAAA;cAAKkB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCnB,OAAA;gBAAKkB,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BnB,OAAA;kBAAGkB,SAAS,EAAC;gBAAgC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNxB,OAAA;gBAAAmB,QAAA,EAAI;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBxB,OAAA;gBAAGkB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE1B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACtB,EAAA,CAvPID,QAAkB;AAAA6C,EAAA,GAAlB7C,QAAkB;AAyPxB,eAAeA,QAAQ;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}