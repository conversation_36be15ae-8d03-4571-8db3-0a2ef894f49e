@echo off
title BasketCase Development
cls
echo.
echo 🚀 BasketCase Development Environment
echo.

REM Quick dependency check and install if needed
cd backend
if not exist node_modules (
    echo Installing backend dependencies...
    npm install --legacy-peer-deps --silent
)

cd ..\frontend  
if not exist node_modules (
    echo Installing frontend dependencies...
    npm install --legacy-peer-deps --silent
)

REM Setup environment
cd ..\backend
if not exist .env copy .env.example .env > nul

echo ✅ Backend API: http://localhost:5000
echo ✅ Frontend App: http://localhost:3000
echo ✅ API Docs: http://localhost:5000/api/docs
echo.
echo Starting servers... (Press Ctrl+C to stop)
echo.

REM Start backend in background
start /B cmd /c "cd backend && npm run start:dev"

REM Wait a moment for backend to start
timeout /t 3 /nobreak > nul

REM Start frontend (this will keep the window open)
cd frontend
npm start

REM If we get here, frontend stopped
echo.
echo 👋 Servers stopped.
pause
