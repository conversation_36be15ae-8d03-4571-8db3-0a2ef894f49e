{"name": "typed-query-selector", "description": "Better typed `querySelector` and `querySelectorAll`.", "author": "<PERSON> <<EMAIL>>", "repository": "g-plane/typed-query-selector", "version": "2.12.0", "license": "MIT", "files": ["*.d.ts"], "main": "shim.d.ts", "types": "shim.d.ts", "sideEffects": false, "scripts": {"test": "tsc -p .", "fmt": "prettier --write *.ts", "fmt:check": "prettier --check *.ts"}, "devDependencies": {"@gplane/tsconfig": "^6.1.0", "@type-challenges/utils": "^0.1.1", "prettier": "^3.0.3", "typescript": "^5.5.3"}}