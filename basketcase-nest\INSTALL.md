# 🚀 BasketCase Installation Guide

## Quick Fix for Dependency Issues

If you're getting dependency version errors, follow these steps:

### Option 1: Use the Fixed Batch Script
```cmd
cd c:\laragon\www\basketcase-nest
start-dev.bat
```

### Option 2: Manual Installation (Recommended)

#### Step 1: Backend Setup
```cmd
cd c:\laragon\www\basketcase-nest\backend
npm install --legacy-peer-deps
```

If that fails, try:
```cmd
npm install --force
```

#### Step 2: Frontend Setup
```cmd
cd c:\laragon\www\basketcase-nest\frontend
npm install --legacy-peer-deps
```

If that fails, try:
```cmd
npm install --force
```

#### Step 3: Start Development Servers

**Terminal 1 - Backend:**
```cmd
cd c:\laragon\www\basketcase-nest\backend
npm run start:dev
```

**Terminal 2 - Frontend:**
```cmd
cd c:\laragon\www\basketcase-nest\frontend
npm start
```

## Alternative: Use Simplified Versions

If you're still having issues, I can create a simplified version without some of the problematic dependencies.

### Quick Test Setup

1. **Test Backend Only:**
```cmd
cd c:\laragon\www\basketcase-nest\backend
npm install express cors dotenv
node -e "
const express = require('express');
const cors = require('cors');
const app = express();
app.use(cors());
app.get('/api/health', (req, res) => res.json({status: 'OK'}));
app.listen(5000, () => console.log('✅ Backend running on http://localhost:5000'));
"
```

2. **Test Frontend Only:**
```cmd
cd c:\laragon\www\basketcase-nest\frontend
npx create-react-app . --template typescript
npm start
```

## Troubleshooting

### Common Issues:

1. **Node Version**: Make sure you're using Node.js 16+ 
```cmd
node --version
```

2. **NPM Cache**: Clear npm cache
```cmd
npm cache clean --force
```

3. **Delete node_modules**: Start fresh
```cmd
rmdir /s node_modules
del package-lock.json
npm install --legacy-peer-deps
```

4. **Use Yarn Instead**: If npm fails
```cmd
npm install -g yarn
yarn install
```

## Access Points Once Running:

- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000/api
- **API Docs:** http://localhost:5000/api/docs
- **Health Check:** http://localhost:5000/api/health

## Need Help?

If you're still having issues, let me know and I can:
1. Create a simplified version without complex dependencies
2. Provide a Docker setup
3. Create a version that works with your current Node.js version

The main goal is to get the demo working quickly!
