.scraping-panel {
  margin-bottom: 2rem;
}

.scraping-panel .card {
  border: 1px solid #e3f2fd;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.scraping-panel .card-header {
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
  color: white;
  border-bottom: none;
}

.scraping-status {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.375rem;
  border-left: 4px solid #2196f3;
}

.scraping-controls .btn {
  transition: all 0.3s ease;
}

.scraping-controls .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.scraping-results .alert {
  border-left: 4px solid #28a745;
  background: #f8fff9;
}

.scraping-info {
  background: #fff3cd;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border-left: 4px solid #ffc107;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

@media (max-width: 768px) {
  .scraping-panel .row > div {
    margin-bottom: 0.5rem;
  }
  
  .scraping-controls .btn {
    font-size: 0.875rem;
  }
}
