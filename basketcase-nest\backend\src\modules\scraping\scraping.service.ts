import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { Product, ProductDocument } from '../../schemas/product.schema';
import { Store, StoreDocument } from '../../schemas/store.schema';
import { Price, PriceDocument } from '../../schemas/price.schema';
import { SparScraperService } from './scrapers/spar-scraper.service';
import { CheckersScraperService } from './scrapers/checkers-scraper.service';
import { IScraper, ScrapedProduct } from './interfaces/scraper.interface';

@Injectable()
export class ScrapingService {
  private readonly logger = new Logger(ScrapingService.name);
  private isRunning = false;
  private lastRunTime: Date | null = null;
  private totalProductsScraped = 0;

  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectModel(Price.name) private priceModel: Model<PriceDocument>,
    private sparScraper: SparScraperService,
    private checkersScraper: CheckersScraperService,
  ) {}

  private get scrapers(): IScraper[] {
    return [this.sparScraper, this.checkersScraper];
  }

  // Automated scraping every 30 minutes for demo (in production, this would be daily)
  @Cron('0 */30 * * * *', {
    name: 'automated-scraping',
    timeZone: 'Africa/Johannesburg',
  })
  async handleAutomatedScraping() {
    this.logger.log('🔄 Starting automated scraping...');
    await this.runFullScraping();
  }

  async runFullScraping() {
    if (this.isRunning) {
      this.logger.warn('⚠️ Scraping already in progress, skipping...');
      return {
        success: false,
        message: 'Scraping already in progress',
        isRunning: true,
      };
    }

    try {
      this.isRunning = true;
      this.logger.log('🚀 Starting full scraping process...');

      const results = {
        totalProducts: 0,
        newProducts: 0,
        updatedPrices: 0,
        errors: 0,
        stores: [],
        startTime: new Date(),
      };

      // Scrape from each store
      for (const scraper of this.scrapers) {
        try {
          this.logger.log(`🔍 Scraping ${scraper.storeName}...`);
          
          const scrapingResult = await scraper.scrapeProducts('all', 25);
          
          if (scrapingResult.success) {
            for (const productData of scrapingResult.products) {
              try {
                await this.processScrapedProduct(productData);
                results.totalProducts++;
              } catch (error) {
                this.logger.error(`❌ Error processing product: ${error.message}`);
                results.errors++;
              }
            }

            results.stores.push({
              name: scraper.storeName,
              productsScraped: scrapingResult.products.length,
              success: true,
              duration: scrapingResult.metadata.scrapingDuration,
            });
          } else {
            results.stores.push({
              name: scraper.storeName,
              productsScraped: 0,
              success: false,
              errors: scrapingResult.errors,
            });
            results.errors += scrapingResult.errors.length;
          }

        } catch (error) {
          this.logger.error(`❌ Error scraping ${scraper.storeName}: ${error.message}`);
          results.stores.push({
            name: scraper.storeName,
            productsScraped: 0,
            success: false,
            error: error.message,
          });
          results.errors++;
        }
      }

      this.lastRunTime = new Date();
      this.totalProductsScraped = results.totalProducts;

      const duration = Date.now() - results.startTime.getTime();
      this.logger.log(`✅ Scraping completed in ${duration}ms: ${results.totalProducts} products processed`);

      return {
        success: true,
        results: {
          ...results,
          duration,
          endTime: new Date(),
        },
      };

    } catch (error) {
      this.logger.error('❌ Scraping service error:', error);
      return {
        success: false,
        error: error.message,
      };
    } finally {
      this.isRunning = false;
    }
  }

  async processScrapedProduct(productData: ScrapedProduct) {
    try {
      // Find or create store
      let store = await this.storeModel.findOne({
        name: productData.store.name,
        branch: productData.store.branch,
      });

      if (!store) {
        const locationParts = productData.store.location?.split(',') || ['Unknown', 'Unknown'];
        store = new this.storeModel({
          name: productData.store.name,
          branch: productData.store.branch,
          address: {
            street: productData.store.branch,
            city: locationParts[0]?.trim() || 'Unknown',
            province: locationParts[1]?.trim() || 'Unknown',
            country: 'South Africa',
          },
          contact: {
            phone: '+27 11 000 0000',
            email: `${productData.store.name.toLowerCase()}@store.co.za`,
          },
          isActive: true,
        });
        await store.save();
        this.logger.log(`✅ Created new store: ${store.name} - ${store.branch}`);
      }

      // Find or create product
      let product = await this.productModel.findOne({
        name: productData.name,
        brand: productData.brand,
      });

      if (!product) {
        product = new this.productModel({
          name: productData.name,
          brand: productData.brand,
          category: productData.category,
          description: productData.description,
          images: productData.images?.map(url => ({ url, isPrimary: false })) || [],
          barcode: productData.barcode,
          isActive: true,
          lastScraped: new Date(),
          isRecentlyUpdated: true,
        });
        await product.save();
        this.logger.log(`✅ Created new product: ${product.name}`);
      } else {
        // Update existing product
        product.lastScraped = new Date();
        product.isRecentlyUpdated = true;
        await product.save();
      }

      // Create or update price
      let price = await this.priceModel.findOne({
        product: product._id,
        store: store._id,
      });

      if (price) {
        // Update existing price
        const oldPrice = price.current;
        price.current = productData.price.current;
        price.original = productData.price.original || productData.price.current;
        price.currency = productData.price.currency;
        price.availability = productData.availability;
        price.promotion = productData.promotion;
        price.scrapingInfo = {
          lastScraped: productData.scrapingInfo.scrapedAt,
          sourceUrl: productData.scrapingInfo.sourceUrl,
          scrapingMethod: 'automated',
          confidence: productData.scrapingInfo.confidence,
        };

        // Add to price history if price changed
        if (oldPrice !== productData.price.current) {
          if (!price.priceHistory) {
            price.priceHistory = [];
          }
          price.priceHistory.push({
            price: oldPrice,
            date: new Date(),
            source: 'scraping',
          });
        }

        await price.save();
        this.logger.log(`📝 Updated price for ${product.name} at ${store.name}`);
      } else {
        // Create new price
        price = new this.priceModel({
          product: product._id,
          store: store._id,
          current: productData.price.current,
          original: productData.price.original || productData.price.current,
          currency: productData.price.currency,
          availability: productData.availability,
          promotion: productData.promotion,
          scrapingInfo: {
            lastScraped: productData.scrapingInfo.scrapedAt,
            sourceUrl: productData.scrapingInfo.sourceUrl,
            scrapingMethod: 'automated',
            confidence: productData.scrapingInfo.confidence,
          },
          isActive: true,
        });
        await price.save();
        this.logger.log(`✅ Created new price for ${product.name} at ${store.name}`);
      }

      return { product, store, price };

    } catch (error) {
      this.logger.error('❌ Error processing scraped product:', error);
      throw error;
    }
  }

  getScrapingStatus() {
    return {
      isRunning: this.isRunning,
      lastRun: this.lastRunTime,
      totalProductsScraped: this.totalProductsScraped,
      availableScrapers: this.scrapers.map(s => s.storeName),
      nextScheduledRun: this.getNextScheduledRun(),
    };
  }

  private getNextScheduledRun(): Date {
    const now = new Date();
    const next = new Date(now);
    next.setMinutes(Math.ceil(now.getMinutes() / 30) * 30, 0, 0);
    if (next <= now) {
      next.setMinutes(next.getMinutes() + 30);
    }
    return next;
  }

  async getScraperHealth() {
    const healthChecks = await Promise.all(
      this.scrapers.map(async (scraper) => ({
        name: scraper.storeName,
        healthy: await scraper.isHealthy(),
      }))
    );

    return {
      overall: healthChecks.every(check => check.healthy),
      scrapers: healthChecks,
      timestamp: new Date(),
    };
  }
}
