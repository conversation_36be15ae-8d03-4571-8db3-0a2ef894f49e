{"version": 3, "file": "TextContent.js", "sourceRoot": "", "sources": ["../../../../src/injected/TextContent.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAMH,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AAE1E;;;;GAIG;AACH,MAAM,qBAAqB,GAAG,CAAC,IAAU,EAA+B,EAAE;IACxE,IAAI,IAAI,YAAY,iBAAiB,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,IAAI,YAAY,mBAAmB,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IACE,IAAI,YAAY,gBAAgB;QAChC,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EACzC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAE3D;;;;GAIG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG,CAAC,IAAU,EAAW,EAAE;IACnE,OAAO,CACL,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAC5E,CAAC;AACJ,CAAC,CAAC;AAYF;;GAEG;AACH,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAqB,CAAC;AAC1D,MAAM,cAAc,GAAG,CAAC,IAAiB,EAAE,EAAE;IAC3C,OAAO,IAAI,EAAE,CAAC;QACZ,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,IAAI,YAAY,UAAU,EAAE,CAAC;YAC/B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;AAC1C,MAAM,kBAAkB,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;IAC1D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,IAAU,EAAe,EAAE;IAC3D,IAAI,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,KAAK,EAAE,CAAC;QACV,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,GAAG,EAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAC,CAAC;IAClC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;QACzC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,gBAAgB,GAAG,EAAE,CAAC;IAC1B,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEjC,IAAI,CAAC,gBAAgB,CACnB,OAAO,EACP,KAAK,CAAC,EAAE;YACN,cAAc,CAAC,KAAK,CAAC,MAA0B,CAAC,CAAC;QACnD,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAC5B,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YACnE,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;gBACtC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC;gBACpC,gBAAgB,IAAI,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC;gBAC1C,SAAS;YACX,CAAC;YACD,IAAI,gBAAgB,EAAE,CAAC;gBACrB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzC,CAAC;YACD,gBAAgB,GAAG,EAAE,CAAC;YACtB,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;gBACzC,KAAK,CAAC,IAAI,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;YAC9C,CAAC;QACH,CAAC;QACD,IAAI,gBAAgB,EAAE,CAAC;YACrB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/C,KAAK,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE;gBAC/B,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YACH,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAClC,OAAO,KAAK,CAAC;AACf,CAAC,CAAC"}