{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\basketcase-nest\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const location = useLocation();\n  const isActive = path => {\n    return location.pathname === path ? 'nav-link active' : 'nav-link';\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar navbar-expand-lg navbar-dark bg-primary fixed-top\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        className: \"navbar-brand\",\n        to: \"/\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-shopping-basket me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), \"BasketCase\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"navbar-toggler\",\n        type: \"button\",\n        \"data-bs-toggle\": \"collapse\",\n        \"data-bs-target\": \"#navbarNav\",\n        \"aria-controls\": \"navbarNav\",\n        \"aria-expanded\": \"false\",\n        \"aria-label\": \"Toggle navigation\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"navbar-toggler-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collapse navbar-collapse\",\n        id: \"navbarNav\",\n        children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"navbar-nav me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: isActive('/'),\n              to: \"/\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-home me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this), \"Home\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: isActive('/products'),\n              to: \"/products\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-box me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this), \"Products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: isActive('/stores'),\n              to: \"/stores\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-store me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), \"Stores\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: isActive('/compare'),\n              to: \"/compare\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-balance-scale me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), \"Compare\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: isActive('/about'),\n              to: \"/about\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-info-circle me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), \"About\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"navbar-nav\",\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"navbar-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), \"Live Price Updates\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "location", "isActive", "path", "pathname", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "id", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/basketcase-nest/frontend/src/components/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Navbar: React.FC = () => {\n  const location = useLocation();\n\n  const isActive = (path: string) => {\n    return location.pathname === path ? 'nav-link active' : 'nav-link';\n  };\n\n  return (\n    <nav className=\"navbar navbar-expand-lg navbar-dark bg-primary fixed-top\">\n      <div className=\"container\">\n        <Link className=\"navbar-brand\" to=\"/\">\n          <i className=\"fas fa-shopping-basket me-2\"></i>\n          BasketCase\n        </Link>\n\n        <button\n          className=\"navbar-toggler\"\n          type=\"button\"\n          data-bs-toggle=\"collapse\"\n          data-bs-target=\"#navbarNav\"\n          aria-controls=\"navbarNav\"\n          aria-expanded=\"false\"\n          aria-label=\"Toggle navigation\"\n        >\n          <span className=\"navbar-toggler-icon\"></span>\n        </button>\n\n        <div className=\"collapse navbar-collapse\" id=\"navbarNav\">\n          <ul className=\"navbar-nav me-auto\">\n            <li className=\"nav-item\">\n              <Link className={isActive('/')} to=\"/\">\n                <i className=\"fas fa-home me-1\"></i>\n                Home\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link className={isActive('/products')} to=\"/products\">\n                <i className=\"fas fa-box me-1\"></i>\n                Products\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link className={isActive('/stores')} to=\"/stores\">\n                <i className=\"fas fa-store me-1\"></i>\n                Stores\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link className={isActive('/compare')} to=\"/compare\">\n                <i className=\"fas fa-balance-scale me-1\"></i>\n                Compare\n              </Link>\n            </li>\n            <li className=\"nav-item\">\n              <Link className={isActive('/about')} to=\"/about\">\n                <i className=\"fas fa-info-circle me-1\"></i>\n                About\n              </Link>\n            </li>\n          </ul>\n\n          <ul className=\"navbar-nav\">\n            <li className=\"nav-item\">\n              <span className=\"navbar-text\">\n                <i className=\"fas fa-clock me-1\"></i>\n                Live Price Updates\n              </span>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,QAAQ,GAAIC,IAAY,IAAK;IACjC,OAAOF,QAAQ,CAACG,QAAQ,KAAKD,IAAI,GAAG,iBAAiB,GAAG,UAAU;EACpE,CAAC;EAED,oBACEL,OAAA;IAAKO,SAAS,EAAC,0DAA0D;IAAAC,QAAA,eACvER,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA,CAACH,IAAI;QAACU,SAAS,EAAC,cAAc;QAACE,EAAE,EAAC,GAAG;QAAAD,QAAA,gBACnCR,OAAA;UAAGO,SAAS,EAAC;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,cAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPb,OAAA;QACEO,SAAS,EAAC,gBAAgB;QAC1BO,IAAI,EAAC,QAAQ;QACb,kBAAe,UAAU;QACzB,kBAAe,YAAY;QAC3B,iBAAc,WAAW;QACzB,iBAAc,OAAO;QACrB,cAAW,mBAAmB;QAAAN,QAAA,eAE9BR,OAAA;UAAMO,SAAS,EAAC;QAAqB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAETb,OAAA;QAAKO,SAAS,EAAC,0BAA0B;QAACQ,EAAE,EAAC,WAAW;QAAAP,QAAA,gBACtDR,OAAA;UAAIO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAChCR,OAAA;YAAIO,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBR,OAAA,CAACH,IAAI;cAACU,SAAS,EAAEH,QAAQ,CAAC,GAAG,CAAE;cAACK,EAAE,EAAC,GAAG;cAAAD,QAAA,gBACpCR,OAAA;gBAAGO,SAAS,EAAC;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,QAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLb,OAAA;YAAIO,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBR,OAAA,CAACH,IAAI;cAACU,SAAS,EAAEH,QAAQ,CAAC,WAAW,CAAE;cAACK,EAAE,EAAC,WAAW;cAAAD,QAAA,gBACpDR,OAAA;gBAAGO,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,YAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLb,OAAA;YAAIO,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBR,OAAA,CAACH,IAAI;cAACU,SAAS,EAAEH,QAAQ,CAAC,SAAS,CAAE;cAACK,EAAE,EAAC,SAAS;cAAAD,QAAA,gBAChDR,OAAA;gBAAGO,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLb,OAAA;YAAIO,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBR,OAAA,CAACH,IAAI;cAACU,SAAS,EAAEH,QAAQ,CAAC,UAAU,CAAE;cAACK,EAAE,EAAC,UAAU;cAAAD,QAAA,gBAClDR,OAAA;gBAAGO,SAAS,EAAC;cAA2B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLb,OAAA;YAAIO,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBR,OAAA,CAACH,IAAI;cAACU,SAAS,EAAEH,QAAQ,CAAC,QAAQ,CAAE;cAACK,EAAE,EAAC,QAAQ;cAAAD,QAAA,gBAC9CR,OAAA;gBAAGO,SAAS,EAAC;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAELb,OAAA;UAAIO,SAAS,EAAC,YAAY;UAAAC,QAAA,eACxBR,OAAA;YAAIO,SAAS,EAAC,UAAU;YAAAC,QAAA,eACtBR,OAAA;cAAMO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC3BR,OAAA;gBAAGO,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CAzEID,MAAgB;EAAA,QACHH,WAAW;AAAA;AAAAkB,EAAA,GADxBf,MAAgB;AA2EtB,eAAeA,MAAM;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}