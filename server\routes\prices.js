const express = require('express');
const router = express.Router();
const { Price, Product, Store } = require('../models');

// GET /api/prices/compare - Compare prices for a product across stores
router.get('/compare', async (req, res) => {
  try {
    const { 
      productId, 
      lat, 
      lng, 
      maxDistance = 10000,
      includeOutOfStock = false 
    } = req.query;

    if (!productId) {
      return res.status(400).json({ message: 'Product ID is required' });
    }

    const location = lat && lng ? {
      coordinates: [parseFloat(lng), parseFloat(lat)]
    } : null;

    const priceComparison = await Price.compareProduct(productId, {
      location,
      maxDistance: parseInt(maxDistance),
      includeOutOfStock: includeOutOfStock === 'true'
    });

    // Calculate savings information
    if (priceComparison.length > 1) {
      const prices = priceComparison.map(p => p.price.current);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      
      priceComparison.forEach(item => {
        item.savings = {
          amount: item.price.current - minPrice,
          percentage: minPrice > 0 ? ((item.price.current - minPrice) / minPrice) * 100 : 0,
          isCheapest: item.price.current === minPrice,
          isMostExpensive: item.price.current === maxPrice
        };
      });
    }

    res.json({ 
      productId,
      comparison: priceComparison,
      summary: {
        storeCount: priceComparison.length,
        lowestPrice: priceComparison.length > 0 ? Math.min(...priceComparison.map(p => p.price.current)) : null,
        highestPrice: priceComparison.length > 0 ? Math.max(...priceComparison.map(p => p.price.current)) : null,
        averagePrice: priceComparison.length > 0 ? 
          priceComparison.reduce((sum, p) => sum + p.price.current, 0) / priceComparison.length : null
      }
    });
  } catch (error) {
    console.error('Error comparing prices:', error);
    res.status(500).json({ message: 'Error comparing prices', error: error.message });
  }
});

// GET /api/prices/trending - Get trending price data
router.get('/trending', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    
    const trendingPrices = await Price.getTrendingPrices(parseInt(days));
    
    res.json({ 
      trending: trendingPrices,
      period: `${days} days`
    });
  } catch (error) {
    console.error('Error fetching trending prices:', error);
    res.status(500).json({ message: 'Error fetching trending prices', error: error.message });
  }
});

// GET /api/prices/promotions - Get current promotions
router.get('/promotions', async (req, res) => {
  try {
    const { 
      storeId, 
      category,
      page = 1, 
      limit = 20 
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    let query = {
      'promotion.isOnPromotion': true,
      'promotion.promotionEndDate': { $gte: new Date() },
      isActive: true
    };

    if (storeId) {
      query.store = storeId;
    }

    const promotions = await Price.find(query)
      .populate('product', 'name brand category images')
      .populate('store', 'name branch location address')
      .sort({ 'promotion.promotionEndDate': 1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Filter by category if specified
    let filteredPromotions = promotions;
    if (category) {
      filteredPromotions = promotions.filter(p => p.product.category === category);
    }

    const total = await Price.countDocuments(query);

    res.json({
      promotions: filteredPromotions,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: filteredPromotions.length,
        totalItems: total
      }
    });
  } catch (error) {
    console.error('Error fetching promotions:', error);
    res.status(500).json({ message: 'Error fetching promotions', error: error.message });
  }
});

// GET /api/prices/history/:productId/:storeId - Get price history for specific product at specific store
router.get('/history/:productId/:storeId', async (req, res) => {
  try {
    const { productId, storeId } = req.params;
    const { days = 30 } = req.query;

    const price = await Price.findOne({
      product: productId,
      store: storeId,
      isActive: true
    }).populate('product', 'name brand').populate('store', 'name branch');

    if (!price) {
      return res.status(404).json({ message: 'Price record not found' });
    }

    // Filter price history by date range
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

    const filteredHistory = price.priceHistory.filter(
      entry => entry.date >= cutoffDate
    ).sort((a, b) => a.date - b.date);

    res.json({
      product: price.product,
      store: price.store,
      currentPrice: price.price.current,
      history: filteredHistory,
      period: `${days} days`
    });
  } catch (error) {
    console.error('Error fetching price history:', error);
    res.status(500).json({ message: 'Error fetching price history', error: error.message });
  }
});

// GET /api/prices/analytics - Get price analytics
router.get('/analytics', async (req, res) => {
  try {
    const { category, days = 30 } = req.query;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    let matchQuery = {
      'scrapingInfo.lastScraped': { $gte: startDate },
      isActive: true
    };

    const pipeline = [
      { $match: matchQuery },
      {
        $lookup: {
          from: 'products',
          localField: 'product',
          foreignField: '_id',
          as: 'productInfo'
        }
      },
      { $unwind: '$productInfo' }
    ];

    // Add category filter if specified
    if (category) {
      pipeline.push({
        $match: { 'productInfo.category': category }
      });
    }

    pipeline.push(
      {
        $group: {
          _id: {
            category: '$productInfo.category',
            store: '$store'
          },
          avgPrice: { $avg: '$price.current' },
          minPrice: { $min: '$price.current' },
          maxPrice: { $max: '$price.current' },
          productCount: { $sum: 1 },
          promotionCount: {
            $sum: { $cond: ['$promotion.isOnPromotion', 1, 0] }
          }
        }
      },
      {
        $lookup: {
          from: 'stores',
          localField: '_id.store',
          foreignField: '_id',
          as: 'storeInfo'
        }
      },
      { $unwind: '$storeInfo' },
      {
        $group: {
          _id: '$_id.category',
          stores: {
            $push: {
              store: '$storeInfo.name',
              avgPrice: '$avgPrice',
              minPrice: '$minPrice',
              maxPrice: '$maxPrice',
              productCount: '$productCount',
              promotionCount: '$promotionCount'
            }
          },
          overallAvgPrice: { $avg: '$avgPrice' },
          overallMinPrice: { $min: '$minPrice' },
          overallMaxPrice: { $max: '$maxPrice' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    );

    const analytics = await Price.aggregate(pipeline);

    res.json({ 
      analytics,
      period: `${days} days`,
      category: category || 'all'
    });
  } catch (error) {
    console.error('Error fetching price analytics:', error);
    res.status(500).json({ message: 'Error fetching price analytics', error: error.message });
  }
});

// POST /api/prices - Create/update price (for scraper use)
router.post('/', async (req, res) => {
  try {
    const { productId, storeId, ...priceData } = req.body;

    // Check if price record already exists
    let price = await Price.findOne({
      product: productId,
      store: storeId
    });

    if (price) {
      // Update existing price
      Object.assign(price, priceData);
      price.scrapingInfo.lastScraped = new Date();
      await price.save();
    } else {
      // Create new price record
      price = new Price({
        product: productId,
        store: storeId,
        ...priceData
      });
      await price.save();
    }

    res.status(201).json({ price });
  } catch (error) {
    console.error('Error creating/updating price:', error);
    res.status(400).json({ message: 'Error creating/updating price', error: error.message });
  }
});

// GET /api/prices/biggest-savings - Get products with biggest savings
router.get('/biggest-savings', async (req, res) => {
  try {
    const { limit = 6 } = req.query;

    // Mock data for biggest savings since we don't have real price data yet
    const mockSavings = [
      {
        product: {
          _id: '1',
          name: 'Coca-Cola 2L Bottle',
          brand: 'Coca-Cola',
          category: 'Beverages'
        },
        store: {
          _id: '1',
          name: 'SPAR',
          branch: 'Sandton City'
        },
        price: {
          current: 18.99,
          original: 24.99
        },
        savings: {
          amount: 6.00,
          percentage: 24.0
        }
      },
      {
        product: {
          _id: '2',
          name: 'White Bread 700g',
          brand: 'Albany',
          category: 'Bakery'
        },
        store: {
          _id: '2',
          name: 'Checkers',
          branch: 'Canal Walk'
        },
        price: {
          current: 12.99,
          original: 16.99
        },
        savings: {
          amount: 4.00,
          percentage: 23.5
        }
      },
      {
        product: {
          _id: '3',
          name: 'Full Cream Milk 2L',
          brand: 'Clover',
          category: 'Dairy & Eggs'
        },
        store: {
          _id: '3',
          name: 'Pick n Pay',
          branch: 'Gateway'
        },
        price: {
          current: 22.99,
          original: 28.99
        },
        savings: {
          amount: 6.00,
          percentage: 20.7
        }
      },
      {
        product: {
          _id: '4',
          name: 'Chicken Breast 1kg',
          brand: 'Rainbow',
          category: 'Meat & Poultry'
        },
        store: {
          _id: '4',
          name: 'Woolworths',
          branch: 'V&A Waterfront'
        },
        price: {
          current: 89.99,
          original: 109.99
        },
        savings: {
          amount: 20.00,
          percentage: 18.2
        }
      },
      {
        product: {
          _id: '5',
          name: 'Bananas 1kg',
          brand: 'Fresh',
          category: 'Fruits & Vegetables'
        },
        store: {
          _id: '1',
          name: 'SPAR',
          branch: 'Menlyn Park'
        },
        price: {
          current: 19.99,
          original: 24.99
        },
        savings: {
          amount: 5.00,
          percentage: 20.0
        }
      },
      {
        product: {
          _id: '6',
          name: 'Pasta 500g',
          brand: 'Barilla',
          category: 'Pantry Staples'
        },
        store: {
          _id: '2',
          name: 'Checkers',
          branch: 'Eastgate'
        },
        price: {
          current: 15.99,
          original: 19.99
        },
        savings: {
          amount: 4.00,
          percentage: 20.0
        }
      }
    ];

    res.json({
      success: true,
      biggestSavings: mockSavings.slice(0, parseInt(limit)),
      totalSavings: mockSavings.length
    });

  } catch (error) {
    console.error('Error getting biggest savings:', error);
    res.status(500).json({ message: 'Error getting biggest savings', error: error.message });
  }
});

module.exports = router;
