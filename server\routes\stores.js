const express = require('express');
const router = express.Router();
const { Store } = require('../models');

// GET /api/stores - List all stores with optional filtering
router.get('/', async (req, res) => {
  try {
    const {
      name,
      city,
      province,
      lat,
      lng,
      maxDistance = 10000,
      page = 1,
      limit = 50
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    let query = { isActive: true };

    // Add filters
    if (name) {
      query.name = name;
    }
    if (city) {
      query['address.city'] = new RegExp(city, 'i');
    }
    if (province) {
      query['address.province'] = province;
    }

    let stores;
    let total;

    // If location provided, use geospatial query
    if (lat && lng) {
      stores = await Store.findNearby(
        parseFloat(lng), 
        parseFloat(lat), 
        parseInt(maxDistance)
      )
      .where(query)
      .skip(skip)
      .limit(parseInt(limit));

      // Count total for pagination (approximate for geospatial queries)
      total = await Store.findNearby(
        parseFloat(lng), 
        parseFloat(lat), 
        parseInt(maxDistance)
      )
      .where(query)
      .countDocuments();
    } else {
      stores = await Store.find(query)
        .skip(skip)
        .limit(parseInt(limit))
        .sort({ name: 1, branch: 1 });

      total = await Store.countDocuments(query);
    }

    // Add distance calculation if location provided
    if (lat && lng) {
      stores = stores.map(store => {
        const storeObj = store.toObject();
        storeObj.distance = store.distanceFrom(parseFloat(lng), parseFloat(lat));
        return storeObj;
      });
    }

    res.json({
      stores,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: stores.length,
        totalItems: total
      }
    });
  } catch (error) {
    console.error('Error fetching stores:', error);
    res.status(500).json({ message: 'Error fetching stores', error: error.message });
  }
});

// GET /api/stores/:id - Get single store details
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const store = await Store.findById(id);
    
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }

    res.json({ store });
  } catch (error) {
    console.error('Error fetching store:', error);
    res.status(500).json({ message: 'Error fetching store', error: error.message });
  }
});

// GET /api/stores/nearby - Find stores near a location
router.get('/location/nearby', async (req, res) => {
  try {
    const { lat, lng, maxDistance = 10000, name } = req.query;

    if (!lat || !lng) {
      return res.status(400).json({ message: 'Latitude and longitude are required' });
    }

    let query = { isActive: true };
    if (name) {
      query.name = name;
    }

    const stores = await Store.findNearby(
      parseFloat(lng), 
      parseFloat(lat), 
      parseInt(maxDistance)
    ).where(query);

    // Add distance to each store
    const storesWithDistance = stores.map(store => {
      const storeObj = store.toObject();
      storeObj.distance = store.distanceFrom(parseFloat(lng), parseFloat(lat));
      return storeObj;
    }).sort((a, b) => a.distance - b.distance);

    res.json({ stores: storesWithDistance });
  } catch (error) {
    console.error('Error finding nearby stores:', error);
    res.status(500).json({ message: 'Error finding nearby stores', error: error.message });
  }
});

// GET /api/stores/chains - Get all store chains
router.get('/meta/chains', async (req, res) => {
  try {
    const chains = await Store.distinct('name', { isActive: true });
    res.json({ chains });
  } catch (error) {
    console.error('Error fetching store chains:', error);
    res.status(500).json({ message: 'Error fetching store chains', error: error.message });
  }
});

// GET /api/stores/provinces - Get all provinces with stores
router.get('/meta/provinces', async (req, res) => {
  try {
    const provinces = await Store.distinct('address.province', { isActive: true });
    res.json({ provinces });
  } catch (error) {
    console.error('Error fetching provinces:', error);
    res.status(500).json({ message: 'Error fetching provinces', error: error.message });
  }
});

// GET /api/stores/cities - Get all cities with stores
router.get('/meta/cities', async (req, res) => {
  try {
    const { province } = req.query;
    
    let query = { isActive: true };
    if (province) {
      query['address.province'] = province;
    }

    const cities = await Store.distinct('address.city', query);
    res.json({ cities: cities.sort() });
  } catch (error) {
    console.error('Error fetching cities:', error);
    res.status(500).json({ message: 'Error fetching cities', error: error.message });
  }
});

// POST /api/stores - Create new store (for admin use)
router.post('/', async (req, res) => {
  try {
    const store = new Store(req.body);
    await store.save();
    res.status(201).json({ store });
  } catch (error) {
    console.error('Error creating store:', error);
    res.status(400).json({ message: 'Error creating store', error: error.message });
  }
});

// PUT /api/stores/:id - Update store (for admin use)
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const store = await Store.findByIdAndUpdate(id, req.body, { 
      new: true, 
      runValidators: true 
    });
    
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }
    
    res.json({ store });
  } catch (error) {
    console.error('Error updating store:', error);
    res.status(400).json({ message: 'Error updating store', error: error.message });
  }
});

// DELETE /api/stores/:id - Soft delete store (for admin use)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const store = await Store.findByIdAndUpdate(id, { isActive: false }, { new: true });
    
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }
    
    res.json({ message: 'Store deactivated successfully' });
  } catch (error) {
    console.error('Error deactivating store:', error);
    res.status(500).json({ message: 'Error deactivating store', error: error.message });
  }
});

module.exports = router;
