const mongoose = require('mongoose');

const priceSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
    index: true
  },
  store: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Store',
    required: true,
    index: true
  },
  price: {
    current: {
      type: Number,
      required: true,
      min: 0
    },
    original: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'ZAR',
      enum: ['ZAR']
    }
  },
  availability: {
    inStock: {
      type: Boolean,
      default: true
    },
    stockLevel: {
      type: String,
      enum: ['high', 'medium', 'low', 'out_of_stock'],
      default: 'high'
    },
    lastStockUpdate: {
      type: Date,
      default: Date.now
    }
  },
  promotion: {
    isOnPromotion: {
      type: Boolean,
      default: false
    },
    promotionType: {
      type: String,
      enum: ['percentage', 'fixed_amount', 'buy_one_get_one', 'bulk_discount', 'other']
    },
    promotionDescription: String,
    promotionStartDate: Date,
    promotionEndDate: Date,
    originalPrice: Number
  },
  unitPrice: {
    value: Number,
    unit: {
      type: String,
      enum: ['per_kg', 'per_100g', 'per_litre', 'per_100ml', 'per_item', 'per_pack']
    }
  },
  priceHistory: [{
    price: Number,
    date: { type: Date, default: Date.now },
    changeType: {
      type: String,
      enum: ['increase', 'decrease', 'no_change']
    },
    changePercentage: Number
  }],
  scrapingInfo: {
    lastScraped: {
      type: Date,
      default: Date.now,
      index: true
    },
    sourceUrl: String,
    scrapeSuccess: {
      type: Boolean,
      default: true
    },
    scrapeErrors: [String]
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound indexes for efficient queries
priceSchema.index({ product: 1, store: 1 }, { unique: true });
priceSchema.index({ product: 1, 'price.current': 1 });
priceSchema.index({ store: 1, 'scrapingInfo.lastScraped': 1 });
priceSchema.index({ 'promotion.isOnPromotion': 1, 'promotion.promotionEndDate': 1 });

// Pre-save middleware to update price history
priceSchema.pre('save', function(next) {
  if (this.isModified('price.current') && !this.isNew) {
    const lastHistory = this.priceHistory[this.priceHistory.length - 1];
    const currentPrice = this.price.current;
    
    if (!lastHistory || lastHistory.price !== currentPrice) {
      let changeType = 'no_change';
      let changePercentage = 0;
      
      if (lastHistory) {
        if (currentPrice > lastHistory.price) {
          changeType = 'increase';
          changePercentage = ((currentPrice - lastHistory.price) / lastHistory.price) * 100;
        } else if (currentPrice < lastHistory.price) {
          changeType = 'decrease';
          changePercentage = ((lastHistory.price - currentPrice) / lastHistory.price) * 100;
        }
      }
      
      this.priceHistory.push({
        price: currentPrice,
        changeType,
        changePercentage: Math.round(changePercentage * 100) / 100
      });
      
      // Keep only last 50 price history entries
      if (this.priceHistory.length > 50) {
        this.priceHistory = this.priceHistory.slice(-50);
      }
    }
  }
  next();
});

// Static method to get price comparison for a product
priceSchema.statics.compareProduct = function(productId, options = {}) {
  const { 
    location, 
    maxDistance = 10000,
    includeOutOfStock = false 
  } = options;
  
  let pipeline = [
    { $match: { product: mongoose.Types.ObjectId(productId), isActive: true } }
  ];
  
  if (!includeOutOfStock) {
    pipeline.push({ $match: { 'availability.inStock': true } });
  }
  
  pipeline.push(
    {
      $lookup: {
        from: 'stores',
        localField: 'store',
        foreignField: '_id',
        as: 'storeInfo'
      }
    },
    { $unwind: '$storeInfo' },
    {
      $match: { 'storeInfo.isActive': true }
    }
  );
  
  // Add geospatial filtering if location provided
  if (location && location.coordinates) {
    pipeline.push({
      $match: {
        'storeInfo.location': {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: location.coordinates
            },
            $maxDistance: maxDistance
          }
        }
      }
    });
  }
  
  pipeline.push(
    {
      $sort: { 'price.current': 1 }
    },
    {
      $project: {
        price: 1,
        availability: 1,
        promotion: 1,
        unitPrice: 1,
        scrapingInfo: 1,
        store: {
          _id: '$storeInfo._id',
          name: '$storeInfo.name',
          branch: '$storeInfo.branch',
          location: '$storeInfo.location',
          address: '$storeInfo.address'
        }
      }
    }
  );
  
  return this.aggregate(pipeline);
};

// Static method to get trending prices
priceSchema.statics.getTrendingPrices = function(days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        'scrapingInfo.lastScraped': { $gte: startDate },
        isActive: true
      }
    },
    {
      $lookup: {
        from: 'products',
        localField: 'product',
        foreignField: '_id',
        as: 'productInfo'
      }
    },
    { $unwind: '$productInfo' },
    {
      $group: {
        _id: '$product',
        productName: { $first: '$productInfo.name' },
        category: { $first: '$productInfo.category' },
        avgPrice: { $avg: '$price.current' },
        minPrice: { $min: '$price.current' },
        maxPrice: { $max: '$price.current' },
        priceCount: { $sum: 1 }
      }
    },
    {
      $sort: { priceCount: -1 }
    },
    {
      $limit: 20
    }
  ]);
};

module.exports = mongoose.model('Price', priceSchema);
