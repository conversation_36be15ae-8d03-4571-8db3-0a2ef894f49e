{"ast": null, "code": "var _jsxFileName = \"c:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { productsAPI, pricesAPI } from '../services/api';\nimport ProductGrid from '../components/ProductGrid';\nimport FilterPanel from '../components/FilterPanel';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport './HomePage.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState(null);\n  const [trendingProducts, setTrendingProducts] = useState([]);\n  const [promotions, setPromotions] = useState([]);\n  const [biggestSavings, setBiggestSavings] = useState([]);\n\n  // Filter states\n  const [filters, setFilters] = useState({\n    query: searchParams.get('q') || '',\n    category: searchParams.get('category') || '',\n    brand: searchParams.get('brand') || '',\n    sortBy: searchParams.get('sortBy') || 'relevance',\n    page: parseInt(searchParams.get('page')) || 1\n  });\n\n  // Load products based on current filters\n  const loadProducts = async (currentFilters = filters) => {\n    setLoading(true);\n    setError(null);\n    try {\n      const searchFilters = {\n        ...currentFilters,\n        limit: 20\n      };\n\n      // Remove empty filters\n      Object.keys(searchFilters).forEach(key => {\n        if (!searchFilters[key]) {\n          delete searchFilters[key];\n        }\n      });\n      const response = await productsAPI.search(searchFilters);\n      setProducts(response.products || []);\n      setPagination(response.pagination);\n    } catch (err) {\n      setError('Failed to load products. Please try again.');\n      console.error('Error loading products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load trending products\n  const loadTrendingProducts = async () => {\n    try {\n      const response = await pricesAPI.getTrending(7);\n      setTrendingProducts(response.trending || []);\n    } catch (err) {\n      console.error('Error loading trending products:', err);\n    }\n  };\n\n  // Load current promotions\n  const loadPromotions = async () => {\n    try {\n      const response = await pricesAPI.getPromotions({\n        limit: 10\n      });\n      setPromotions(response.promotions || []);\n    } catch (err) {\n      console.error('Error loading promotions:', err);\n    }\n  };\n\n  // Load biggest savings\n  const loadBiggestSavings = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/biggest-savings?limit=6');\n      const data = await response.json();\n      setBiggestSavings(data.biggestSavings || []);\n    } catch (err) {\n      console.error('Error loading biggest savings:', err);\n    }\n  };\n\n  // Update URL when filters change\n  useEffect(() => {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value && value !== 'relevance' && value !== 1) {\n        params.set(key, value.toString());\n      }\n    });\n    setSearchParams(params);\n  }, [filters, setSearchParams]);\n\n  // Load products when filters change\n  useEffect(() => {\n    loadProducts();\n  }, [filters]);\n\n  // Load initial data\n  useEffect(() => {\n    loadTrendingProducts();\n    loadPromotions();\n    loadBiggestSavings();\n  }, []);\n\n  // Handle filter changes\n  const handleFilterChange = newFilters => {\n    setFilters(prev => ({\n      ...prev,\n      ...newFilters,\n      page: 1 // Reset to first page when filters change\n    }));\n  };\n\n  // Handle page change\n  const handlePageChange = page => {\n    setFilters(prev => ({\n      ...prev,\n      page\n    }));\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  // Handle product click\n  const handleProductClick = product => {\n    navigate(`/product/${product._id}`);\n  };\n\n  // Check if we have active search/filters\n  const hasActiveFilters = filters.query || filters.category || filters.brand;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-container\",\n      children: [!hasActiveFilters && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: \"Compare Grocery Prices Across South Africa\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: \"Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n          className: \"filters-sidebar\",\n          children: /*#__PURE__*/_jsxDEV(FilterPanel, {\n            filters: filters,\n            onFilterChange: handleFilterChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"products-section\",\n          children: [hasActiveFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-results-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"results-title\",\n              children: filters.query ? `Search results for \"${filters.query}\"` : 'Products'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), pagination && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"results-info\",\n              children: [\"Showing \", (pagination.current - 1) * 20 + 1, \" - \", Math.min(pagination.current * 20, pagination.totalItems), \" of \", pagination.totalItems, \" products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n            message: error,\n            onRetry: () => loadProducts()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 23\n          }, this), !loading && !error && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [products.length > 0 ? /*#__PURE__*/_jsxDEV(ProductGrid, {\n              products: products,\n              onProductClick: handleProductClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 19\n            }, this) : hasActiveFilters ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-results\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No products found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Try adjusting your search criteria or filters.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this) : null, pagination && pagination.total > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pagination\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pagination-button\",\n                disabled: pagination.current <= 1,\n                onClick: () => handlePageChange(pagination.current - 1),\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"pagination-info\",\n                children: [\"Page \", pagination.current, \" of \", pagination.total]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pagination-button\",\n                disabled: pagination.current >= pagination.total,\n                onClick: () => handlePageChange(pagination.current + 1),\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), !hasActiveFilters && !loading && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"featured-sections\",\n        children: [biggestSavings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"featured-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"\\uD83D\\uDD25 Biggest Savings Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"savings-grid\",\n            children: biggestSavings.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"savings-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"savings-badge\",\n                children: [\"Save R\", item.savings.amount.toFixed(2), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"savings-percentage\",\n                  children: [\"(\", item.savings.percentage.toFixed(0), \"% off)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"savings-product\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"savings-product-name\",\n                  children: item.product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"savings-brand\",\n                  children: item.product.brand\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"savings-store\",\n                  children: [\"at \", item.store.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"savings-prices\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"savings-current-price\",\n                  children: [\"R\", item.price.current.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"savings-original-price\",\n                  children: [\"was R\", item.price.original.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"view-deal-button\",\n                onClick: () => handleProductClick(item.product),\n                children: \"View Deal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 23\n              }, this)]\n            }, `${item.product._id}-${item.store._id}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bottom-sections\",\n          children: [trendingProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"featured-section trending-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section-title\",\n              children: \"\\uD83D\\uDCC8 Trending Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trending-grid\",\n              children: trendingProducts.slice(0, 6).map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trending-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"trending-product-name\",\n                  children: item.productName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"trending-stats\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"avg-price\",\n                    children: [\"Avg: R\", item.avgPrice.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"price-range\",\n                    children: [\"R\", item.minPrice.toFixed(2), \" - R\", item.maxPrice.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 25\n                }, this)]\n              }, item._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 17\n          }, this), promotions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"featured-section promotions-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"section-title\",\n              children: \"\\uD83C\\uDFF7\\uFE0F Current Promotions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotions-grid\",\n              children: promotions.slice(0, 8).map(promo => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"promotion-product\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: promo.product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"promotion-store\",\n                    children: promo.store.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"promotion-price\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"current-price\",\n                    children: [\"R\", promo.price.current.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 27\n                  }, this), promo.price.original > promo.price.current && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    children: [\"R\", promo.price.original.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 25\n                }, this), promo.promotion.promotionDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"promotion-description\",\n                  children: promo.promotion.promotionDescription\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 27\n                }, this)]\n              }, `${promo.product._id}-${promo.store._id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"7f50XSA1s/ADvsOM98n5SNUBYoA=\", false, function () {\n  return [useSearchParams, useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "useNavigate", "productsAPI", "pricesAPI", "ProductGrid", "FilterPanel", "LoadingSpinner", "ErrorMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HomePage", "_s", "searchParams", "setSearchParams", "navigate", "products", "setProducts", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "trendingProducts", "setTrendingProducts", "promotions", "setPromotions", "biggestSavings", "setBiggestSavings", "filters", "setFilters", "query", "get", "category", "brand", "sortBy", "page", "parseInt", "loadProducts", "currentFilters", "searchFilters", "limit", "Object", "keys", "for<PERSON>ach", "key", "response", "search", "err", "console", "loadTrendingProducts", "getTrending", "trending", "loadPromotions", "getPromotions", "loadBiggestSavings", "fetch", "data", "json", "params", "URLSearchParams", "entries", "value", "set", "toString", "handleFilterChange", "newFilters", "prev", "handlePageChange", "window", "scrollTo", "top", "behavior", "handleProductClick", "product", "_id", "hasActiveFilters", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onFilterChange", "current", "Math", "min", "totalItems", "message", "onRetry", "length", "onProductClick", "total", "disabled", "onClick", "map", "item", "savings", "amount", "toFixed", "percentage", "name", "store", "price", "original", "slice", "productName", "avgPrice", "minPrice", "maxPrice", "promo", "promotion", "promotionDescription", "_c", "$RefreshReg$"], "sources": ["c:/laragon/www/basketcase/client/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { productsAPI, pricesAPI } from '../services/api';\nimport ProductGrid from '../components/ProductGrid';\nimport FilterPanel from '../components/FilterPanel';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport './HomePage.css';\n\nconst HomePage = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const navigate = useNavigate();\n  \n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState(null);\n  const [trendingProducts, setTrendingProducts] = useState([]);\n  const [promotions, setPromotions] = useState([]);\n  const [biggestSavings, setBiggestSavings] = useState([]);\n\n  // Filter states\n  const [filters, setFilters] = useState({\n    query: searchParams.get('q') || '',\n    category: searchParams.get('category') || '',\n    brand: searchParams.get('brand') || '',\n    sortBy: searchParams.get('sortBy') || 'relevance',\n    page: parseInt(searchParams.get('page')) || 1\n  });\n\n  // Load products based on current filters\n  const loadProducts = async (currentFilters = filters) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const searchFilters = {\n        ...currentFilters,\n        limit: 20\n      };\n      \n      // Remove empty filters\n      Object.keys(searchFilters).forEach(key => {\n        if (!searchFilters[key]) {\n          delete searchFilters[key];\n        }\n      });\n      \n      const response = await productsAPI.search(searchFilters);\n      setProducts(response.products || []);\n      setPagination(response.pagination);\n      \n    } catch (err) {\n      setError('Failed to load products. Please try again.');\n      console.error('Error loading products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load trending products\n  const loadTrendingProducts = async () => {\n    try {\n      const response = await pricesAPI.getTrending(7);\n      setTrendingProducts(response.trending || []);\n    } catch (err) {\n      console.error('Error loading trending products:', err);\n    }\n  };\n\n  // Load current promotions\n  const loadPromotions = async () => {\n    try {\n      const response = await pricesAPI.getPromotions({ limit: 10 });\n      setPromotions(response.promotions || []);\n    } catch (err) {\n      console.error('Error loading promotions:', err);\n    }\n  };\n\n  // Load biggest savings\n  const loadBiggestSavings = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/biggest-savings?limit=6');\n      const data = await response.json();\n      setBiggestSavings(data.biggestSavings || []);\n    } catch (err) {\n      console.error('Error loading biggest savings:', err);\n    }\n  };\n\n  // Update URL when filters change\n  useEffect(() => {\n    const params = new URLSearchParams();\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value && value !== 'relevance' && value !== 1) {\n        params.set(key, value.toString());\n      }\n    });\n    \n    setSearchParams(params);\n  }, [filters, setSearchParams]);\n\n  // Load products when filters change\n  useEffect(() => {\n    loadProducts();\n  }, [filters]);\n\n  // Load initial data\n  useEffect(() => {\n    loadTrendingProducts();\n    loadPromotions();\n    loadBiggestSavings();\n  }, []);\n\n  // Handle filter changes\n  const handleFilterChange = (newFilters) => {\n    setFilters(prev => ({\n      ...prev,\n      ...newFilters,\n      page: 1 // Reset to first page when filters change\n    }));\n  };\n\n  // Handle page change\n  const handlePageChange = (page) => {\n    setFilters(prev => ({ ...prev, page }));\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  // Handle product click\n  const handleProductClick = (product) => {\n    navigate(`/product/${product._id}`);\n  };\n\n  // Check if we have active search/filters\n  const hasActiveFilters = filters.query || filters.category || filters.brand;\n\n  return (\n    <div className=\"home-page\">\n      <div className=\"home-container\">\n        \n        {/* Hero Section - only show when no active search */}\n        {!hasActiveFilters && (\n          <section className=\"hero-section\">\n            <div className=\"hero-content\">\n              <h1 className=\"hero-title\">\n                Compare Grocery Prices Across South Africa\n              </h1>\n              <p className=\"hero-subtitle\">\n                Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths\n              </p>\n            </div>\n          </section>\n        )}\n\n        <div className=\"main-content\">\n          {/* Filter Panel */}\n          <aside className=\"filters-sidebar\">\n            <FilterPanel\n              filters={filters}\n              onFilterChange={handleFilterChange}\n            />\n          </aside>\n\n          {/* Products Section */}\n          <main className=\"products-section\">\n            \n            {/* Search Results Header */}\n            {hasActiveFilters && (\n              <div className=\"search-results-header\">\n                <h2 className=\"results-title\">\n                  {filters.query ? `Search results for \"${filters.query}\"` : 'Products'}\n                </h2>\n                \n                {pagination && (\n                  <div className=\"results-info\">\n                    Showing {((pagination.current - 1) * 20) + 1} - {Math.min(pagination.current * 20, pagination.totalItems)} of {pagination.totalItems} products\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* Loading State */}\n            {loading && <LoadingSpinner />}\n\n            {/* Error State */}\n            {error && <ErrorMessage message={error} onRetry={() => loadProducts()} />}\n\n            {/* Products Grid */}\n            {!loading && !error && (\n              <>\n                {products.length > 0 ? (\n                  <ProductGrid\n                    products={products}\n                    onProductClick={handleProductClick}\n                  />\n                ) : hasActiveFilters ? (\n                  <div className=\"no-results\">\n                    <h3>No products found</h3>\n                    <p>Try adjusting your search criteria or filters.</p>\n                  </div>\n                ) : null}\n\n                {/* Pagination */}\n                {pagination && pagination.total > 1 && (\n                  <div className=\"pagination\">\n                    <button\n                      className=\"pagination-button\"\n                      disabled={pagination.current <= 1}\n                      onClick={() => handlePageChange(pagination.current - 1)}\n                    >\n                      Previous\n                    </button>\n                    \n                    <span className=\"pagination-info\">\n                      Page {pagination.current} of {pagination.total}\n                    </span>\n                    \n                    <button\n                      className=\"pagination-button\"\n                      disabled={pagination.current >= pagination.total}\n                      onClick={() => handlePageChange(pagination.current + 1)}\n                    >\n                      Next\n                    </button>\n                  </div>\n                )}\n              </>\n            )}\n          </main>\n        </div>\n\n        {/* Featured Sections - only show when no active search */}\n        {!hasActiveFilters && !loading && (\n          <section className=\"featured-sections\">\n\n            {/* Biggest Savings */}\n            {biggestSavings.length > 0 && (\n              <div className=\"featured-section\">\n                <h2 className=\"section-title\">🔥 Biggest Savings Today</h2>\n                <div className=\"savings-grid\">\n                  {biggestSavings.map(item => (\n                    <div key={`${item.product._id}-${item.store._id}`} className=\"savings-item\">\n                      <div className=\"savings-badge\">\n                        Save R{item.savings.amount.toFixed(2)}\n                        <span className=\"savings-percentage\">({item.savings.percentage.toFixed(0)}% off)</span>\n                      </div>\n                      <div className=\"savings-product\">\n                        <h4 className=\"savings-product-name\">{item.product.name}</h4>\n                        <div className=\"savings-brand\">{item.product.brand}</div>\n                        <div className=\"savings-store\">at {item.store.name}</div>\n                      </div>\n                      <div className=\"savings-prices\">\n                        <div className=\"savings-current-price\">R{item.price.current.toFixed(2)}</div>\n                        <div className=\"savings-original-price\">was R{item.price.original.toFixed(2)}</div>\n                      </div>\n                      <button\n                        className=\"view-deal-button\"\n                        onClick={() => handleProductClick(item.product)}\n                      >\n                        View Deal\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Bottom Section - Trending and Promotions Side by Side */}\n            <div className=\"bottom-sections\">\n              {/* Trending Products - Left */}\n              {trendingProducts.length > 0 && (\n                <div className=\"featured-section trending-section\">\n                  <h2 className=\"section-title\">📈 Trending Products</h2>\n                  <div className=\"trending-grid\">\n                    {trendingProducts.slice(0, 6).map(item => (\n                      <div key={item._id} className=\"trending-item\">\n                        <h3 className=\"trending-product-name\">{item.productName}</h3>\n                        <div className=\"trending-stats\">\n                          <span className=\"avg-price\">Avg: R{item.avgPrice.toFixed(2)}</span>\n                          <span className=\"price-range\">\n                            R{item.minPrice.toFixed(2)} - R{item.maxPrice.toFixed(2)}\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Current Promotions - Right */}\n              {promotions.length > 0 && (\n                <div className=\"featured-section promotions-section\">\n                  <h2 className=\"section-title\">🏷️ Current Promotions</h2>\n                  <div className=\"promotions-grid\">\n                    {promotions.slice(0, 8).map(promo => (\n                      <div key={`${promo.product._id}-${promo.store._id}`} className=\"promotion-item\">\n                        <div className=\"promotion-product\">\n                          <h4>{promo.product.name}</h4>\n                          <span className=\"promotion-store\">{promo.store.name}</span>\n                        </div>\n                        <div className=\"promotion-price\">\n                          <span className=\"current-price\">R{promo.price.current.toFixed(2)}</span>\n                          {promo.price.original > promo.price.current && (\n                            <span className=\"original-price\">R{promo.price.original.toFixed(2)}</span>\n                          )}\n                        </div>\n                        {promo.promotion.promotionDescription && (\n                          <div className=\"promotion-description\">\n                            {promo.promotion.promotionDescription}\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </section>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,WAAW,EAAEC,SAAS,QAAQ,iBAAiB;AACxD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGf,eAAe,CAAC,CAAC;EACzD,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC;IACrCmC,KAAK,EAAEnB,YAAY,CAACoB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE;IAClCC,QAAQ,EAAErB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;IAC5CE,KAAK,EAAEtB,YAAY,CAACoB,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;IACtCG,MAAM,EAAEvB,YAAY,CAACoB,GAAG,CAAC,QAAQ,CAAC,IAAI,WAAW;IACjDI,IAAI,EAAEC,QAAQ,CAACzB,YAAY,CAACoB,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI;EAC9C,CAAC,CAAC;;EAEF;EACA,MAAMM,YAAY,GAAG,MAAAA,CAAOC,cAAc,GAAGV,OAAO,KAAK;IACvDX,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMoB,aAAa,GAAG;QACpB,GAAGD,cAAc;QACjBE,KAAK,EAAE;MACT,CAAC;;MAED;MACAC,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;QACxC,IAAI,CAACL,aAAa,CAACK,GAAG,CAAC,EAAE;UACvB,OAAOL,aAAa,CAACK,GAAG,CAAC;QAC3B;MACF,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,MAAM9C,WAAW,CAAC+C,MAAM,CAACP,aAAa,CAAC;MACxDxB,WAAW,CAAC8B,QAAQ,CAAC/B,QAAQ,IAAI,EAAE,CAAC;MACpCO,aAAa,CAACwB,QAAQ,CAACzB,UAAU,CAAC;IAEpC,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ5B,QAAQ,CAAC,4CAA4C,CAAC;MACtD6B,OAAO,CAAC9B,KAAK,CAAC,yBAAyB,EAAE6B,GAAG,CAAC;IAC/C,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAM7C,SAAS,CAACkD,WAAW,CAAC,CAAC,CAAC;MAC/C3B,mBAAmB,CAACsB,QAAQ,CAACM,QAAQ,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,kCAAkC,EAAE6B,GAAG,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAM7C,SAAS,CAACqD,aAAa,CAAC;QAAEb,KAAK,EAAE;MAAG,CAAC,CAAC;MAC7Df,aAAa,CAACoB,QAAQ,CAACrB,UAAU,IAAI,EAAE,CAAC;IAC1C,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,2BAA2B,EAAE6B,GAAG,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMU,KAAK,CAAC,0DAA0D,CAAC;MACxF,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAClC9B,iBAAiB,CAAC6B,IAAI,CAAC9B,cAAc,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,gCAAgC,EAAE6B,GAAG,CAAC;IACtD;EACF,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACd,MAAM8D,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpClB,MAAM,CAACmB,OAAO,CAAChC,OAAO,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEiB,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,IAAIA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,CAAC,EAAE;QACjDH,MAAM,CAACI,GAAG,CAAClB,GAAG,EAAEiB,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;IAEFnD,eAAe,CAAC8C,MAAM,CAAC;EACzB,CAAC,EAAE,CAAC9B,OAAO,EAAEhB,eAAe,CAAC,CAAC;;EAE9B;EACAhB,SAAS,CAAC,MAAM;IACdyC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACT,OAAO,CAAC,CAAC;;EAEb;EACAhC,SAAS,CAAC,MAAM;IACdqD,oBAAoB,CAAC,CAAC;IACtBG,cAAc,CAAC,CAAC;IAChBE,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,kBAAkB,GAAIC,UAAU,IAAK;IACzCpC,UAAU,CAACqC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,GAAGD,UAAU;MACb9B,IAAI,EAAE,CAAC,CAAC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMgC,gBAAgB,GAAIhC,IAAI,IAAK;IACjCN,UAAU,CAACqC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE/B;IAAK,CAAC,CAAC,CAAC;IACvCiC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;IACtC5D,QAAQ,CAAC,YAAY4D,OAAO,CAACC,GAAG,EAAE,CAAC;EACrC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG/C,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACI,QAAQ,IAAIJ,OAAO,CAACK,KAAK;EAE3E,oBACE3B,OAAA;IAAKsE,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBvE,OAAA;MAAKsE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAG5B,CAACF,gBAAgB,iBAChBrE,OAAA;QAASsE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC/BvE,OAAA;UAAKsE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvE,OAAA;YAAIsE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3E,OAAA;YAAGsE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV,eAED3E,OAAA;QAAKsE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3BvE,OAAA;UAAOsE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAChCvE,OAAA,CAACJ,WAAW;YACV0B,OAAO,EAAEA,OAAQ;YACjBsD,cAAc,EAAElB;UAAmB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGR3E,OAAA;UAAMsE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAG/BF,gBAAgB,iBACfrE,OAAA;YAAKsE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCvE,OAAA;cAAIsE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1BjD,OAAO,CAACE,KAAK,GAAG,uBAAuBF,OAAO,CAACE,KAAK,GAAG,GAAG;YAAU;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,EAEJ7D,UAAU,iBACTd,OAAA;cAAKsE,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAC,UACpB,EAAE,CAACzD,UAAU,CAAC+D,OAAO,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,EAAC,KAAG,EAACC,IAAI,CAACC,GAAG,CAACjE,UAAU,CAAC+D,OAAO,GAAG,EAAE,EAAE/D,UAAU,CAACkE,UAAU,CAAC,EAAC,MAAI,EAAClE,UAAU,CAACkE,UAAU,EAAC,WACvI;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGAjE,OAAO,iBAAIV,OAAA,CAACH,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAG7B/D,KAAK,iBAAIZ,OAAA,CAACF,YAAY;YAACmF,OAAO,EAAErE,KAAM;YAACsE,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAAC;UAAE;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAGxE,CAACjE,OAAO,IAAI,CAACE,KAAK,iBACjBZ,OAAA,CAAAE,SAAA;YAAAqE,QAAA,GACG/D,QAAQ,CAAC2E,MAAM,GAAG,CAAC,gBAClBnF,OAAA,CAACL,WAAW;cACVa,QAAQ,EAAEA,QAAS;cACnB4E,cAAc,EAAElB;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,GACAN,gBAAgB,gBAClBrE,OAAA;cAAKsE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvE,OAAA;gBAAAuE,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B3E,OAAA;gBAAAuE,QAAA,EAAG;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,GACJ,IAAI,EAGP7D,UAAU,IAAIA,UAAU,CAACuE,KAAK,GAAG,CAAC,iBACjCrF,OAAA;cAAKsE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvE,OAAA;gBACEsE,SAAS,EAAC,mBAAmB;gBAC7BgB,QAAQ,EAAExE,UAAU,CAAC+D,OAAO,IAAI,CAAE;gBAClCU,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC/C,UAAU,CAAC+D,OAAO,GAAG,CAAC,CAAE;gBAAAN,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET3E,OAAA;gBAAMsE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,OAC3B,EAACzD,UAAU,CAAC+D,OAAO,EAAC,MAAI,EAAC/D,UAAU,CAACuE,KAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAEP3E,OAAA;gBACEsE,SAAS,EAAC,mBAAmB;gBAC7BgB,QAAQ,EAAExE,UAAU,CAAC+D,OAAO,IAAI/D,UAAU,CAACuE,KAAM;gBACjDE,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC/C,UAAU,CAAC+D,OAAO,GAAG,CAAC,CAAE;gBAAAN,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGL,CAACN,gBAAgB,IAAI,CAAC3D,OAAO,iBAC5BV,OAAA;QAASsE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAGnCnD,cAAc,CAAC+D,MAAM,GAAG,CAAC,iBACxBnF,OAAA;UAAKsE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvE,OAAA;YAAIsE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D3E,OAAA;YAAKsE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BnD,cAAc,CAACoE,GAAG,CAACC,IAAI,iBACtBzF,OAAA;cAAmDsE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzEvE,OAAA;gBAAKsE,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,QACvB,EAACkB,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,eACrC5F,OAAA;kBAAMsE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,GAAC,EAACkB,IAAI,CAACC,OAAO,CAACG,UAAU,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACN3E,OAAA;gBAAKsE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BvE,OAAA;kBAAIsE,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAEkB,IAAI,CAACtB,OAAO,CAAC2B;gBAAI;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7D3E,OAAA;kBAAKsE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEkB,IAAI,CAACtB,OAAO,CAACxC;gBAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzD3E,OAAA;kBAAKsE,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,KAAG,EAACkB,IAAI,CAACM,KAAK,CAACD,IAAI;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACN3E,OAAA;gBAAKsE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvE,OAAA;kBAAKsE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,GAAC,EAACkB,IAAI,CAACO,KAAK,CAACnB,OAAO,CAACe,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7E3E,OAAA;kBAAKsE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,GAAC,OAAK,EAACkB,IAAI,CAACO,KAAK,CAACC,QAAQ,CAACL,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACN3E,OAAA;gBACEsE,SAAS,EAAC,kBAAkB;gBAC5BiB,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACuB,IAAI,CAACtB,OAAO,CAAE;gBAAAI,QAAA,EACjD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAnBD,GAAGc,IAAI,CAACtB,OAAO,CAACC,GAAG,IAAIqB,IAAI,CAACM,KAAK,CAAC3B,GAAG,EAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoB5C,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD3E,OAAA;UAAKsE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAE7BvD,gBAAgB,CAACmE,MAAM,GAAG,CAAC,iBAC1BnF,OAAA;YAAKsE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDvE,OAAA;cAAIsE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD3E,OAAA;cAAKsE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BvD,gBAAgB,CAACkF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAACC,IAAI,iBACpCzF,OAAA;gBAAoBsE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC3CvE,OAAA;kBAAIsE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEkB,IAAI,CAACU;gBAAW;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7D3E,OAAA;kBAAKsE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BvE,OAAA;oBAAMsE,SAAS,EAAC,WAAW;oBAAAC,QAAA,GAAC,QAAM,EAACkB,IAAI,CAACW,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnE3E,OAAA;oBAAMsE,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,GAC3B,EAACkB,IAAI,CAACY,QAAQ,CAACT,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI,EAACH,IAAI,CAACa,QAAQ,CAACV,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAPEc,IAAI,CAACrB,GAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAzD,UAAU,CAACiE,MAAM,GAAG,CAAC,iBACpBnF,OAAA;YAAKsE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAClDvE,OAAA;cAAIsE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzD3E,OAAA;cAAKsE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7BrD,UAAU,CAACgF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAACe,KAAK,iBAC/BvG,OAAA;gBAAqDsE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7EvE,OAAA;kBAAKsE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCvE,OAAA;oBAAAuE,QAAA,EAAKgC,KAAK,CAACpC,OAAO,CAAC2B;kBAAI;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7B3E,OAAA;oBAAMsE,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAEgC,KAAK,CAACR,KAAK,CAACD;kBAAI;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACN3E,OAAA;kBAAKsE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BvE,OAAA;oBAAMsE,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAC,GAAC,EAACgC,KAAK,CAACP,KAAK,CAACnB,OAAO,CAACe,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACvE4B,KAAK,CAACP,KAAK,CAACC,QAAQ,GAAGM,KAAK,CAACP,KAAK,CAACnB,OAAO,iBACzC7E,OAAA;oBAAMsE,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAAC,GAAC,EAACgC,KAAK,CAACP,KAAK,CAACC,QAAQ,CAACL,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAC1E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACL4B,KAAK,CAACC,SAAS,CAACC,oBAAoB,iBACnCzG,OAAA;kBAAKsE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnCgC,KAAK,CAACC,SAAS,CAACC;gBAAoB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CACN;cAAA,GAfO,GAAG4B,KAAK,CAACpC,OAAO,CAACC,GAAG,IAAImC,KAAK,CAACR,KAAK,CAAC3B,GAAG,EAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgB9C,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvE,EAAA,CA5TID,QAAQ;EAAA,QAC4BZ,eAAe,EACtCC,WAAW;AAAA;AAAAkH,EAAA,GAFxBvG,QAAQ;AA8Td,eAAeA,QAAQ;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}