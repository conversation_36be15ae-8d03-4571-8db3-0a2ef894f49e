const { chromium } = require('playwright');
const { Product, Store, Price } = require('../models');

class BaseScraper {
  constructor(storeName, config = {}) {
    this.storeName = storeName;
    this.config = {
      headless: true,
      timeout: 30000,
      maxRetries: 3,
      delay: 1000,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      ...config
    };
    this.browser = null;
    this.context = null;
    this.page = null;
    this.errors = [];
    this.scrapedCount = 0;
  }

  async initialize() {
    try {
      console.log(`Initializing ${this.storeName} scraper...`);
      
      this.browser = await chromium.launch({ 
        headless: this.config.headless,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      this.context = await this.browser.newContext({
        userAgent: this.config.userAgent,
        viewport: { width: 1920, height: 1080 }
      });
      
      this.page = await this.context.newPage();
      
      // Set default timeout
      this.page.setDefaultTimeout(this.config.timeout);
      
      // Block unnecessary resources to speed up scraping
      await this.page.route('**/*', (route) => {
        const resourceType = route.request().resourceType();
        if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
          route.abort();
        } else {
          route.continue();
        }
      });
      
      console.log(`${this.storeName} scraper initialized successfully`);
    } catch (error) {
      console.error(`Error initializing ${this.storeName} scraper:`, error);
      throw error;
    }
  }

  async cleanup() {
    try {
      if (this.page) await this.page.close();
      if (this.context) await this.context.close();
      if (this.browser) await this.browser.close();
      console.log(`${this.storeName} scraper cleaned up`);
    } catch (error) {
      console.error(`Error cleaning up ${this.storeName} scraper:`, error);
    }
  }

  async delay(ms = null) {
    const delayTime = ms || this.config.delay;
    await new Promise(resolve => setTimeout(resolve, delayTime));
  }

  async retryOperation(operation, maxRetries = null) {
    const retries = maxRetries || this.config.maxRetries;
    
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        console.log(`Attempt ${attempt} failed for ${this.storeName}:`, error.message);
        
        if (attempt === retries) {
          throw error;
        }
        
        await this.delay(attempt * 1000); // Exponential backoff
      }
    }
  }

  async navigateToPage(url) {
    return await this.retryOperation(async () => {
      console.log(`Navigating to: ${url}`);
      await this.page.goto(url, { waitUntil: 'networkidle' });
      await this.delay();
    });
  }

  async findOrCreateProduct(productData) {
    try {
      // Try to find existing product by normalized name and brand
      let product = await Product.findOne({
        normalizedName: productData.name.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim(),
        brand: productData.brand || null
      });

      if (!product) {
        // Create new product
        product = new Product({
          name: productData.name,
          brand: productData.brand,
          category: productData.category || 'Other',
          description: productData.description,
          images: productData.images || [],
          barcode: productData.barcode,
          tags: productData.tags || []
        });
        
        await product.save();
        console.log(`Created new product: ${product.name}`);
      }

      return product;
    } catch (error) {
      console.error('Error finding/creating product:', error);
      throw error;
    }
  }

  async updatePrice(product, store, priceData) {
    try {
      let price = await Price.findOne({
        product: product._id,
        store: store._id
      });

      const priceInfo = {
        price: {
          current: priceData.current,
          original: priceData.original || priceData.current,
          currency: 'ZAR'
        },
        availability: {
          inStock: priceData.inStock !== false,
          stockLevel: priceData.stockLevel || 'high'
        },
        promotion: {
          isOnPromotion: priceData.isOnPromotion || false,
          promotionType: priceData.promotionType,
          promotionDescription: priceData.promotionDescription,
          promotionStartDate: priceData.promotionStartDate,
          promotionEndDate: priceData.promotionEndDate
        },
        unitPrice: priceData.unitPrice,
        scrapingInfo: {
          lastScraped: new Date(),
          sourceUrl: priceData.sourceUrl,
          scrapeSuccess: true,
          scrapeErrors: []
        }
      };

      if (price) {
        Object.assign(price, priceInfo);
        await price.save();
      } else {
        price = new Price({
          product: product._id,
          store: store._id,
          ...priceInfo
        });
        await price.save();
      }

      this.scrapedCount++;
      return price;
    } catch (error) {
      console.error('Error updating price:', error);
      this.errors.push({
        product: product.name,
        store: store.name,
        error: error.message
      });
      throw error;
    }
  }

  async getStore() {
    const store = await Store.findOne({ 
      name: this.storeName,
      isActive: true 
    });
    
    if (!store) {
      throw new Error(`Store ${this.storeName} not found in database`);
    }
    
    return store;
  }

  // Abstract methods to be implemented by specific scrapers
  async scrapeProducts() {
    throw new Error('scrapeProducts method must be implemented by subclass');
  }

  async scrapeProductDetails(productUrl) {
    throw new Error('scrapeProductDetails method must be implemented by subclass');
  }

  // Main scraping method
  async scrape() {
    const startTime = Date.now();
    console.log(`Starting ${this.storeName} scraping process...`);

    try {
      await this.initialize();
      const store = await this.getStore();
      
      await this.scrapeProducts();
      
      // Update store's last scraped timestamp
      store.lastScraped = new Date();
      await store.save();
      
      const duration = (Date.now() - startTime) / 1000;
      console.log(`${this.storeName} scraping completed in ${duration}s`);
      console.log(`Scraped ${this.scrapedCount} products`);
      
      if (this.errors.length > 0) {
        console.log(`Encountered ${this.errors.length} errors during scraping`);
      }
      
      return {
        success: true,
        scrapedCount: this.scrapedCount,
        errors: this.errors,
        duration
      };
      
    } catch (error) {
      console.error(`Error during ${this.storeName} scraping:`, error);
      return {
        success: false,
        error: error.message,
        scrapedCount: this.scrapedCount,
        errors: this.errors
      };
    } finally {
      await this.cleanup();
    }
  }
}

module.exports = BaseScraper;
