import { Document } from 'mongoose';
export type StoreDocument = Store & Document;
export declare class Store {
    name: string;
    branch: string;
    location?: {
        type: string;
        coordinates: [number, number];
    };
    address: {
        street: string;
        city: string;
        province: string;
        postalCode?: string;
        country: string;
    };
    contact?: {
        phone?: string;
        email?: string;
        website?: string;
    };
    operatingHours?: {
        monday?: {
            open: string;
            close: string;
        };
        tuesday?: {
            open: string;
            close: string;
        };
        wednesday?: {
            open: string;
            close: string;
        };
        thursday?: {
            open: string;
            close: string;
        };
        friday?: {
            open: string;
            close: string;
        };
        saturday?: {
            open: string;
            close: string;
        };
        sunday?: {
            open: string;
            close: string;
        };
    };
    isActive: boolean;
}
export declare const StoreSchema: import("mongoose").Schema<Store, import("mongoose").Model<Store, any, any, any, Document<unknown, any, Store> & Store & {
    _id: import("mongoose").Types.ObjectId;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Store, Document<unknown, {}, import("mongoose").FlatRecord<Store>> & import("mongoose").FlatRecord<Store> & {
    _id: import("mongoose").Types.ObjectId;
}>;
