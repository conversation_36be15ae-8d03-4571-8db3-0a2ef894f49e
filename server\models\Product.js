const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  normalizedName: {
    type: String,
    required: true,
    index: true
  },
  brand: {
    type: String,
    trim: true,
    index: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Dairy & Eggs',
      'Meat & Poultry',
      'Seafood',
      'Fruits & Vegetables',
      'Bakery',
      'Pantry Staples',
      'Snacks & Confectionery',
      'Beverages',
      'Frozen Foods',
      'Health & Beauty',
      'Household & Cleaning',
      'Baby & Kids',
      'Pet Care',
      'Other'
    ],
    index: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  specifications: {
    weight: String,
    volume: String,
    dimensions: String,
    ingredients: [String],
    allergens: [String],
    nutritionalInfo: {
      energy: String,
      protein: String,
      carbohydrates: String,
      fat: String,
      fiber: String,
      sodium: String
    }
  },
  images: [{
    url: String,
    alt: String,
    isPrimary: { type: Boolean, default: false }
  }],
  barcode: {
    type: String,
    sparse: true,
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Text index for search functionality
productSchema.index({
  name: 'text',
  normalizedName: 'text',
  brand: 'text',
  description: 'text',
  tags: 'text'
}, {
  weights: {
    name: 10,
    normalizedName: 8,
    brand: 5,
    description: 2,
    tags: 3
  }
});

// Compound indexes for efficient queries
productSchema.index({ category: 1, brand: 1 });
productSchema.index({ normalizedName: 1, brand: 1 });

// Pre-save middleware to create normalized name
productSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.normalizedName = this.name
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ')    // Replace multiple spaces with single space
      .trim();
  }
  next();
});

// Static method to search products
productSchema.statics.search = function(query, options = {}) {
  const {
    category,
    brand,
    limit = 20,
    skip = 0,
    sortBy = 'relevance'
  } = options;

  let searchQuery = { isActive: true };
  
  // Add text search if query provided
  if (query) {
    searchQuery.$text = { $search: query };
  }
  
  // Add filters
  if (category) searchQuery.category = category;
  if (brand) searchQuery.brand = new RegExp(brand, 'i');
  
  let mongoQuery = this.find(searchQuery);
  
  // Add text score for relevance sorting
  if (query) {
    mongoQuery = mongoQuery.select({ score: { $meta: 'textScore' } });
  }
  
  // Apply sorting
  if (sortBy === 'relevance' && query) {
    mongoQuery = mongoQuery.sort({ score: { $meta: 'textScore' } });
  } else if (sortBy === 'name') {
    mongoQuery = mongoQuery.sort({ name: 1 });
  } else if (sortBy === 'brand') {
    mongoQuery = mongoQuery.sort({ brand: 1, name: 1 });
  }
  
  return mongoQuery.skip(skip).limit(limit);
};

// Static method to find similar products
productSchema.statics.findSimilar = function(productId, limit = 5) {
  return this.findById(productId)
    .then(product => {
      if (!product) return [];
      
      return this.find({
        _id: { $ne: productId },
        $or: [
          { normalizedName: new RegExp(product.normalizedName, 'i') },
          { brand: product.brand, category: product.category },
          { tags: { $in: product.tags } }
        ],
        isActive: true
      }).limit(limit);
    });
};

module.exports = mongoose.model('Product', productSchema);
