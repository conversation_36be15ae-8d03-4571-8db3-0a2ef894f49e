{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-brand\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-logo\",\n              children: \"\\uD83D\\uDED2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"footer-brand-name\",\n              children: \"BasketCase\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"footer-description\",\n            children: \"Compare grocery prices across South Africa's major retailers. Find the best deals and save money on your shopping.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"footer-section-title\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"footer-link\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/compare\",\n                className: \"footer-link\",\n                children: \"Compare Prices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/stores\",\n                className: \"footer-link\",\n                children: \"Store Locations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"footer-section-title\",\n            children: \"Supported Stores\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"SPAR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"Checkers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"Pick n Pay\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"Woolworths\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"footer-section-title\",\n            children: \"Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"How It Works\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"footer-link\",\n              children: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-bottom\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-copyright\",\n            children: [\"\\xA9 \", currentYear, \" BasketCase. All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-disclaimer\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Prices are updated daily and may vary. Please verify prices at the store. BasketCase is not affiliated with any of the retailers mentioned.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/client/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './Footer.css';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"footer\">\n      <div className=\"footer-container\">\n        \n        {/* Footer Content */}\n        <div className=\"footer-content\">\n          \n          {/* Brand Section */}\n          <div className=\"footer-section\">\n            <div className=\"footer-brand\">\n              <div className=\"footer-logo\">🛒</div>\n              <h3 className=\"footer-brand-name\">BasketCase</h3>\n            </div>\n            <p className=\"footer-description\">\n              Compare grocery prices across South Africa's major retailers. \n              Find the best deals and save money on your shopping.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"footer-section\">\n            <h4 className=\"footer-section-title\">Quick Links</h4>\n            <ul className=\"footer-links\">\n              <li><Link to=\"/\" className=\"footer-link\">Home</Link></li>\n              <li><Link to=\"/compare\" className=\"footer-link\">Compare Prices</Link></li>\n              <li><Link to=\"/stores\" className=\"footer-link\">Store Locations</Link></li>\n            </ul>\n          </div>\n\n          {/* Supported Stores */}\n          <div className=\"footer-section\">\n            <h4 className=\"footer-section-title\">Supported Stores</h4>\n            <ul className=\"footer-links\">\n              <li className=\"footer-link\">SPAR</li>\n              <li className=\"footer-link\">Checkers</li>\n              <li className=\"footer-link\">Pick n Pay</li>\n              <li className=\"footer-link\">Woolworths</li>\n            </ul>\n          </div>\n\n          {/* Information */}\n          <div className=\"footer-section\">\n            <h4 className=\"footer-section-title\">Information</h4>\n            <ul className=\"footer-links\">\n              <li className=\"footer-link\">About Us</li>\n              <li className=\"footer-link\">How It Works</li>\n              <li className=\"footer-link\">Privacy Policy</li>\n              <li className=\"footer-link\">Terms of Service</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Footer Bottom */}\n        <div className=\"footer-bottom\">\n          <div className=\"footer-bottom-content\">\n            <div className=\"footer-copyright\">\n              © {currentYear} BasketCase. All rights reserved.\n            </div>\n            \n            <div className=\"footer-disclaimer\">\n              <small>\n                Prices are updated daily and may vary. Please verify prices at the store. \n                BasketCase is not affiliated with any of the retailers mentioned.\n              </small>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBN,OAAA;MAAKK,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAG/BN,OAAA;QAAKK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAG7BN,OAAA;UAAKK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BN,OAAA;YAAKK,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BN,OAAA;cAAKK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCV,OAAA;cAAIK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNV,OAAA;YAAGK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAGlC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BN,OAAA;YAAIK,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDV,OAAA;YAAIK,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1BN,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACa,EAAE,EAAC,GAAG;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDV,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACa,EAAE,EAAC,UAAU;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EV,OAAA;cAAAM,QAAA,eAAIN,OAAA,CAACF,IAAI;gBAACa,EAAE,EAAC,SAAS;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BN,OAAA;YAAIK,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DV,OAAA;YAAIK,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1BN,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrCV,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCV,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CV,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BN,OAAA;YAAIK,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDV,OAAA;YAAIK,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1BN,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCV,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CV,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CV,OAAA;cAAIK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNV,OAAA;QAAKK,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BN,OAAA;UAAKK,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCN,OAAA;YAAKK,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAAC,OAC9B,EAACJ,WAAW,EAAC,mCACjB;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENV,OAAA;YAAKK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCN,OAAA;cAAAM,QAAA,EAAO;YAGP;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACE,EAAA,GAzEIX,MAAM;AA2EZ,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}