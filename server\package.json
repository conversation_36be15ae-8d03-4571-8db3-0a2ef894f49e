{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index-working.js", "dev": "nodemon index-working.js", "dev-old": "nodemon index.js", "scrape": "node scripts/scraper.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "cheerio": "^1.1.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "mongoose": "^8.16.4", "morgan": "^1.10.0", "node-cron": "^4.2.1", "playwright": "^1.54.1"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}