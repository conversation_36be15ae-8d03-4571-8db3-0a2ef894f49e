@echo off
setlocal enabledelayedexpansion

echo.
echo ==========================================
echo    BasketCase - Grocery Price Comparison
echo ==========================================
echo    One-Click Startup Script
echo ==========================================
echo.

REM Color codes for better output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Ask user for startup preference
echo Choose your startup method:
echo 1. Docker (Recommended - Includes MongoDB)
echo 2. Local Development (Requires local MongoDB)
echo 3. Docker with MongoDB only (Hybrid)
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto docker_full
if "%choice%"=="2" goto local_dev
if "%choice%"=="3" goto docker_mongo
echo Invalid choice. Defaulting to Docker setup...
goto docker_full

:docker_full
echo.
echo %BLUE%Setting up with Docker (Full Stack)...%NC%
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: Docker is not installed or not in PATH%NC%
    echo Please install Docker Desktop from https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: Docker Compose is not installed%NC%
    pause
    exit /b 1
)

echo %GREEN%Docker is installed ✓%NC%

REM Stop any existing containers
echo Stopping any existing containers...
docker-compose -f docker-compose.dev.yml down >nul 2>&1

REM Build and start containers
echo Building and starting Docker containers...
echo This may take a few minutes on first run...
docker-compose -f docker-compose.dev.yml up --build -d

if %errorlevel% neq 0 (
    echo %RED%ERROR: Failed to start Docker containers%NC%
    echo Trying to start without build...
    docker-compose -f docker-compose.dev.yml up -d
)

REM Wait for services to be ready
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose -f docker-compose.dev.yml ps

echo.
echo %GREEN%✓ Docker containers are running!%NC%
echo.
echo Services available at:
echo - Frontend: http://localhost:3000
echo - Backend API: http://localhost:5000
echo - MongoDB: localhost:27017
echo.
echo %YELLOW%Setting up initial data...%NC%
timeout /t 5 /nobreak >nul

REM Create sample stores (run inside container)
docker-compose -f docker-compose.dev.yml exec -T server node scripts/create-sample-stores.js 2>nul

REM Run initial scraping (optional)
set /p scrape="Run initial product scraping? (y/N): "
if /i "%scrape%"=="y" (
    echo Running initial scraping...
    docker-compose -f docker-compose.dev.yml exec -T server npm run scrape
)

echo.
echo %GREEN%🎉 BasketCase is ready!%NC%
echo.
echo Open your browser and visit: http://localhost:3000
echo.
echo To stop the application, run: docker-compose -f docker-compose.dev.yml down
echo To view logs, run: docker-compose -f docker-compose.dev.yml logs -f
echo.
start http://localhost:3000
pause
exit /b 0

:docker_mongo
echo.
echo %BLUE%Setting up with Docker MongoDB + Local Development...%NC%
echo.

REM Check Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: Docker is not installed%NC%
    pause
    exit /b 1
)

REM Start only MongoDB in Docker
echo Starting MongoDB in Docker...
docker run -d --name basketcase-mongo -p 27017:27017 -e MONGO_INITDB_ROOT_USERNAME=admin -e MONGO_INITDB_ROOT_PASSWORD=password123 mongo:6.0

if %errorlevel% neq 0 (
    echo MongoDB container might already exist, trying to start it...
    docker start basketcase-mongo
)

echo %GREEN%MongoDB is running in Docker ✓%NC%
timeout /t 3 /nobreak >nul

goto local_setup

:local_dev
echo.
echo %BLUE%Setting up Local Development Environment...%NC%
echo.

REM Check if MongoDB is running locally
echo Checking for local MongoDB...
netstat -an | findstr :27017 >nul
if %errorlevel% neq 0 (
    echo %YELLOW%Warning: MongoDB doesn't appear to be running on port 27017%NC%
    echo Please make sure MongoDB is installed and running, or choose Docker option.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" exit /b 1
)

:local_setup
REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed
    pause
    exit /b 1
)

echo Node.js and npm are installed ✓

REM Install dependencies
echo.
echo Installing dependencies...

if not exist "node_modules" (
    echo Installing root dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install root dependencies
        pause
        exit /b 1
    )
)

if not exist "server\node_modules" (
    echo Installing server dependencies...
    cd server
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install server dependencies
        pause
        exit /b 1
    )
    cd ..
)

if not exist "client\node_modules" (
    echo Installing client dependencies...
    cd client
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install client dependencies
        pause
        exit /b 1
    )
    cd ..
)

echo ✓ Dependencies installed

REM Setup environment
if not exist "server\.env" (
    echo Setting up environment configuration...
    echo.
    echo Creating default environment files...

    REM Create server .env
    echo PORT=5000> server\.env
    echo NODE_ENV=development>> server\.env
    echo CLIENT_URL=http://localhost:3000>> server\.env
    echo MONGODB_URI=mongodb://localhost:27017/basketcase>> server\.env
    echo JWT_SECRET=basketcase_dev_secret>> server\.env
    echo LOG_LEVEL=info>> server\.env

    REM Create client .env
    echo REACT_APP_API_URL=http://localhost:5000/api> client\.env
    echo REACT_APP_NAME=BasketCase>> client\.env
    echo REACT_APP_VERSION=1.0.0>> client\.env

    echo ✓ Environment files created
)

REM Create sample data
echo Setting up sample data...
timeout /t 2 /nobreak >nul
node scripts\create-sample-stores.js >nul 2>&1

echo.
echo Starting BasketCase application...
echo.
echo Services will be available at:
echo - Frontend: http://localhost:3000
echo - Backend API: http://localhost:5000
echo - MongoDB: localhost:27017
echo.
echo Press Ctrl+C in this window to stop the application
echo.

REM Start the application and keep window open
call npm run dev

echo.
echo Application stopped.
pause
