@echo off
echo.
echo ==========================================
echo    BasketCase - Grocery Price Comparison
echo ==========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed or not in PATH
    pause
    exit /b 1
)

echo Node.js and npm are installed ✓
echo.

REM Check if MongoDB is running
echo Checking MongoDB connection...
timeout /t 2 /nobreak >nul

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing root dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install root dependencies
        pause
        exit /b 1
    )
)

if not exist "server\node_modules" (
    echo Installing server dependencies...
    cd server
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install server dependencies
        pause
        exit /b 1
    )
    cd ..
)

if not exist "client\node_modules" (
    echo Installing client dependencies...
    cd client
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install client dependencies
        pause
        exit /b 1
    )
    cd ..
)

REM Check if environment files exist
if not exist "server\.env" (
    echo Setting up environment configuration...
    node scripts\setup-env.js
)

echo.
echo Starting BasketCase application...
echo.
echo Server will start on: http://localhost:5000
echo Client will start on: http://localhost:3000
echo.
echo Press Ctrl+C to stop the application
echo.

REM Start both server and client
npm run dev

pause
