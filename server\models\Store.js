const mongoose = require('mongoose');

const storeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    enum: ['SPAR', 'Checkers', 'Pick n Pay', 'Woolworths'],
    index: true
  },
  branch: {
    type: String,
    required: true,
    trim: true
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true,
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates format'
      }
    }
  },
  address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    province: { 
      type: String, 
      required: true,
      enum: ['Western Cape', 'Eastern Cape', 'Northern Cape', 'Free State', 
             'KwaZulu-Natal', 'North West', 'Gauteng', 'Mpumalanga', 'Limpopo']
    },
    postalCode: { type: String, required: true }
  },
  contact: {
    phone: String,
    email: String
  },
  operatingHours: {
    monday: { open: String, close: String },
    tuesday: { open: String, close: String },
    wednesday: { open: String, close: String },
    thursday: { open: String, close: String },
    friday: { open: String, close: String },
    saturday: { open: String, close: String },
    sunday: { open: String, close: String }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastScraped: {
    type: Date,
    default: null
  },
  scrapeUrl: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

// Create geospatial index for location-based queries
storeSchema.index({ location: '2dsphere' });

// Compound index for efficient store queries
storeSchema.index({ name: 1, branch: 1 }, { unique: true });
storeSchema.index({ 'address.city': 1, name: 1 });
storeSchema.index({ 'address.province': 1, name: 1 });

// Instance method to calculate distance from a point
storeSchema.methods.distanceFrom = function(longitude, latitude) {
  const [storeLng, storeLat] = this.location.coordinates;
  const R = 6371; // Earth's radius in kilometers
  
  const dLat = (latitude - storeLat) * Math.PI / 180;
  const dLng = (longitude - storeLng) * Math.PI / 180;
  
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(storeLat * Math.PI / 180) * Math.cos(latitude * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in kilometers
};

// Static method to find stores within radius
storeSchema.statics.findNearby = function(longitude, latitude, maxDistance = 10000) {
  return this.find({
    location: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance // in meters
      }
    },
    isActive: true
  });
};

module.exports = mongoose.model('Store', storeSchema);
