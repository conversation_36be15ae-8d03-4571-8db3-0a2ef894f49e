const cron = require('node-cron');
const ScraperOrchestrator = require('../scripts/scraper');
const mongoose = require('mongoose');
require('dotenv').config();

class SchedulerService {
  constructor() {
    this.jobs = new Map();
    this.isRunning = false;
    this.currentJob = null;
    this.lastRunResults = null;
  }

  async initialize() {
    try {
      // Connect to MongoDB if not already connected
      if (mongoose.connection.readyState !== 1) {
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/basketcase', {
          useNewUrlParser: true,
          useUnifiedTopology: true,
        });
        console.log('Scheduler: Connected to MongoDB');
      }
      
      this.setupScheduledJobs();
      console.log('Scheduler service initialized successfully');
    } catch (error) {
      console.error('Error initializing scheduler service:', error);
      throw error;
    }
  }

  setupScheduledJobs() {
    // Daily scraping job - runs at 2 AM every day
    const dailyScrapingJob = cron.schedule('0 2 * * *', async () => {
      console.log('Starting scheduled daily scraping job...');
      await this.runScrapingJob('daily');
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    // Weekly deep scraping job - runs at 1 AM every Sunday
    const weeklyScrapingJob = cron.schedule('0 1 * * 0', async () => {
      console.log('Starting scheduled weekly deep scraping job...');
      await this.runScrapingJob('weekly');
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    // Health check job - runs every hour
    const healthCheckJob = cron.schedule('0 * * * *', async () => {
      await this.performHealthCheck();
    }, {
      scheduled: false,
      timezone: 'Africa/Johannesburg'
    });

    this.jobs.set('daily', dailyScrapingJob);
    this.jobs.set('weekly', weeklyScrapingJob);
    this.jobs.set('health', healthCheckJob);

    console.log('Scheduled jobs configured:');
    console.log('- Daily scraping: 2:00 AM daily');
    console.log('- Weekly deep scraping: 1:00 AM Sundays');
    console.log('- Health check: Every hour');
  }

  async runScrapingJob(jobType = 'manual') {
    if (this.isRunning) {
      console.log('Scraping job already running, skipping...');
      return { success: false, message: 'Job already running' };
    }

    this.isRunning = true;
    this.currentJob = {
      type: jobType,
      startTime: new Date(),
      status: 'running'
    };

    try {
      console.log(`Starting ${jobType} scraping job at ${this.currentJob.startTime}`);
      
      const orchestrator = new ScraperOrchestrator();
      await orchestrator.initialize();
      
      const results = await orchestrator.runAllScrapers();
      
      this.lastRunResults = {
        ...results,
        jobType,
        completedAt: new Date(),
        duration: Date.now() - this.currentJob.startTime.getTime()
      };

      this.currentJob.status = 'completed';
      this.currentJob.endTime = new Date();

      console.log(`${jobType} scraping job completed successfully`);
      
      // Send notification if configured
      await this.sendJobNotification(this.lastRunResults);
      
      await orchestrator.cleanup();
      
      return this.lastRunResults;
      
    } catch (error) {
      console.error(`Error in ${jobType} scraping job:`, error);
      
      this.lastRunResults = {
        success: false,
        error: error.message,
        jobType,
        completedAt: new Date(),
        duration: Date.now() - this.currentJob.startTime.getTime()
      };

      this.currentJob.status = 'failed';
      this.currentJob.endTime = new Date();
      this.currentJob.error = error.message;

      await this.sendJobNotification(this.lastRunResults);
      
      return this.lastRunResults;
      
    } finally {
      this.isRunning = false;
      this.currentJob = null;
    }
  }

  async performHealthCheck() {
    try {
      // Check MongoDB connection
      const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
      
      // Check last scraping job results
      const lastJobStatus = this.lastRunResults ? {
        success: this.lastRunResults.success,
        completedAt: this.lastRunResults.completedAt,
        totalScraped: this.lastRunResults.results ? 
          this.lastRunResults.results.reduce((sum, r) => sum + r.scrapedCount, 0) : 0
      } : null;

      const healthStatus = {
        timestamp: new Date(),
        database: dbStatus,
        scheduler: 'running',
        lastJob: lastJobStatus,
        currentJob: this.currentJob,
        uptime: process.uptime()
      };

      console.log('Health check completed:', JSON.stringify(healthStatus, null, 2));
      
      return healthStatus;
      
    } catch (error) {
      console.error('Error during health check:', error);
      return {
        timestamp: new Date(),
        status: 'error',
        error: error.message
      };
    }
  }

  async sendJobNotification(results) {
    try {
      // This is a placeholder for notification functionality
      // You can integrate with email services, Slack, Discord, etc.
      
      const message = results.success ? 
        `✅ Scraping job (${results.jobType}) completed successfully!\n` +
        `Total products scraped: ${results.results ? results.results.reduce((sum, r) => sum + r.scrapedCount, 0) : 0}\n` +
        `Duration: ${Math.round(results.duration / 1000)}s` :
        `❌ Scraping job (${results.jobType}) failed!\n` +
        `Error: ${results.error}`;

      console.log('Job notification:', message);
      
      // TODO: Implement actual notification sending
      // - Email notifications
      // - Slack/Discord webhooks
      // - SMS notifications
      // - Push notifications
      
    } catch (error) {
      console.error('Error sending job notification:', error);
    }
  }

  startScheduler() {
    try {
      this.jobs.forEach((job, name) => {
        job.start();
        console.log(`Started scheduled job: ${name}`);
      });
      
      console.log('Scheduler service started successfully');
      return true;
    } catch (error) {
      console.error('Error starting scheduler:', error);
      return false;
    }
  }

  stopScheduler() {
    try {
      this.jobs.forEach((job, name) => {
        job.stop();
        console.log(`Stopped scheduled job: ${name}`);
      });
      
      console.log('Scheduler service stopped');
      return true;
    } catch (error) {
      console.error('Error stopping scheduler:', error);
      return false;
    }
  }

  getJobStatus(jobName) {
    const job = this.jobs.get(jobName);
    if (!job) {
      return { error: 'Job not found' };
    }

    return {
      name: jobName,
      running: job.running,
      lastRun: this.lastRunResults,
      currentJob: this.currentJob
    };
  }

  getAllJobsStatus() {
    const status = {};
    
    this.jobs.forEach((job, name) => {
      status[name] = {
        running: job.running,
        scheduled: true
      };
    });

    return {
      jobs: status,
      isRunning: this.isRunning,
      currentJob: this.currentJob,
      lastRun: this.lastRunResults,
      uptime: process.uptime()
    };
  }

  async runJobManually(jobType = 'manual') {
    console.log(`Manually triggering ${jobType} scraping job...`);
    return await this.runScrapingJob(jobType);
  }
}

module.exports = SchedulerService;
