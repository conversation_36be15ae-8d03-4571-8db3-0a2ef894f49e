{"name": "basketcase-frontend", "version": "1.0.0", "description": "BasketCase Price Comparison Frontend - React TypeScript", "private": true, "dependencies": {"@types/node": "^18.16.12", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "axios": "^1.4.0", "bootstrap": "^5.2.3", "react-bootstrap": "^2.7.4", "@fortawesome/fontawesome-free": "^6.4.0", "react-query": "^3.39.3", "react-hook-form": "^7.44.3", "@hookform/resolvers": "^3.1.0", "yup": "^1.2.0", "react-toastify": "^9.1.3", "lodash": "^4.17.21", "@types/lodash": "^4.14.194"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0"}, "proxy": "http://localhost:5000"}