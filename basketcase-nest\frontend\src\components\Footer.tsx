import React from 'react';

const Footer: React.FC = () => {
  return (
    <footer className="footer bg-dark text-light py-4 mt-5">
      <div className="container">
        <div className="row">
          <div className="col-md-4">
            <h5 className="footer-title">
              <i className="fas fa-shopping-basket me-2"></i>
              BasketCase
            </h5>
            <p className="text-muted">
              Compare grocery prices across South African stores and save money on your shopping.
            </p>
          </div>
          
          <div className="col-md-2">
            <h6 className="footer-title">Quick Links</h6>
            <ul className="list-unstyled">
              <li><a href="/" className="footer-link">Home</a></li>
              <li><a href="/products" className="footer-link">Products</a></li>
              <li><a href="/stores" className="footer-link">Stores</a></li>
              <li><a href="/compare" className="footer-link">Compare</a></li>
            </ul>
          </div>
          
          <div className="col-md-2">
            <h6 className="footer-title">Stores</h6>
            <ul className="list-unstyled">
              <li><span className="text-muted">SPAR</span></li>
              <li><span className="text-muted">Checkers</span></li>
              <li><span className="text-muted">Pick n Pay</span></li>
              <li><span className="text-muted">Woolworths</span></li>
            </ul>
          </div>
          
          <div className="col-md-2">
            <h6 className="footer-title">Categories</h6>
            <ul className="list-unstyled">
              <li><span className="text-muted">Beverages</span></li>
              <li><span className="text-muted">Bakery</span></li>
              <li><span className="text-muted">Dairy & Eggs</span></li>
              <li><span className="text-muted">Fresh Produce</span></li>
            </ul>
          </div>
          
          <div className="col-md-2">
            <h6 className="footer-title">Connect</h6>
            <div className="d-flex">
              <a href="#" className="footer-link me-3">
                <i className="fab fa-facebook-f"></i>
              </a>
              <a href="#" className="footer-link me-3">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="footer-link me-3">
                <i className="fab fa-instagram"></i>
              </a>
            </div>
          </div>
        </div>
        
        <hr className="my-4" />
        
        <div className="row align-items-center">
          <div className="col-md-6">
            <p className="mb-0 text-muted">
              © 2025 BasketCase. All rights reserved.
            </p>
          </div>
          <div className="col-md-6 text-md-end">
            <p className="mb-0 text-muted">
              <i className="fas fa-sync-alt me-1"></i>
              Prices updated every 30 minutes
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
