import e from"postcss-selector-parser";import s from"fs";import t from"path";import n from"postcss";var o=(s,t)=>{const n={};return s.nodes.slice().forEach((s=>{var o,c,r;if("atrule"!==s.type||"custom-selector"!==s.name)return;if(!s.params||!s.params.includes(":--"))return;const a=s.params.trim(),i=e().astSync(a),l=null==i||null==(o=i.nodes)||null==(c=o[0])||null==(r=c.nodes)?void 0:r[0];if(!l||"pseudo"!==l.type||!l.value.startsWith(":--"))return;const u=l.toString();n[u]=e().astSync(a.slice(u.length).trim()),Object(t).preserve||s.remove()})),n};function c(e,s){let t=e.nodes.length-1;for(;t>=0;){const n=r(e.nodes[t],s);n.length&&e.nodes.splice(t,1,...n),--t}return e}function r(e,s){const t=[];for(const n in e.nodes){const{value:o,nodes:a}=e.nodes[n];if(o in s){for(const c of s[o].nodes){const o=e.clone(),a=c.clone().nodes;o.nodes.splice(n,1,...a.map(((s,t)=>("selector"===s.type&&(s.spaces={...e.nodes[t].spaces}),0===t&&s.spaces&&(s.spaces.before=""),t===a.length-1&&s.spaces&&(s.spaces.after=""),s))));const i=r(o,s);u(o.nodes,Number(n)),i.length?t.push(...i):t.push(o)}return t}a&&a.length&&c(e.nodes[n],s)}return t}const a=/^(tag|universal)$/,i=/^(class|id|pseudo|tag|universal)$/,l=e=>i.test(Object(e).type),u=(e,s)=>{if(s&&(t=e[s],a.test(Object(t).type))&&l(e[s-1])){let t=s-1;for(;t&&l(e[t]);)--t;if(t<s){const n=e.splice(s,1)[0];e.splice(t,0,n),e[t].spaces.before=e[t+1].spaces.before,e[t+1].spaces.before="",e[s]&&(e[s].spaces.after=e[t].spaces.after,e[t].spaces.after="")}}var t};function p(e){return o(e)}function f(s){const t=Object.assign({},Object(s).customSelectors||Object(s)["custom-selectors"]);for(const s in t)t[s]=e().astSync(t[s]);return t}function m(e){return e.map((e=>{if(e instanceof Promise)return e;if(e instanceof Function)return e();const s=e===Object(e)?e:{from:String(e)};if(Object(s).customSelectors||Object(s)["custom-selectors"])return s;const n=String(s.from||"");return{type:(s.type||t.extname(n).slice(1)).toLowerCase(),from:n}})).reduce((async(e,s)=>{const o=await e,{type:c,from:r}=await s;return"ast"===c?Object.assign(o,p(r)):"css"===c?Object.assign(o,await async function(e){const s=await j(t.resolve(e));return p(n.parse(s,{from:t.resolve(e)}))}(r)):"js"===c?Object.assign(o,await async function(e){return f(await import(t.resolve(e)))}(r)):"json"===c?Object.assign(o,await async function(e){return f(await y(t.resolve(e)))}(r)):Object.assign(o,f(await s))}),Promise.resolve({}))}const j=e=>new Promise(((t,n)=>{s.readFile(e,"utf8",((e,s)=>{e?n(e):t(s)}))})),y=async e=>JSON.parse(await j(e));function b(e,s){return Promise.all(s.map((async s=>{if(s instanceof Function)await s(d(e));else{const n=s===Object(s)?s:{to:String(s)},o=n.toJSON||d;if("customSelectors"in n)n.customSelectors=o(e);else if("custom-selectors"in n)n["custom-selectors"]=o(e);else{const s=String(n.to||""),c=(n.type||t.extname(n.to).slice(1)).toLowerCase(),r=o(e);"css"===c&&await async function(e,s){const t=`${Object.keys(s).reduce(((e,t)=>(e.push(`@custom-selector ${t} ${s[t]};`),e)),[]).join("\n")}\n`;await O(e,t)}(s,r),"js"===c&&await async function(e,s){const t=`module.exports = {\n\tcustomSelectors: {\n${Object.keys(s).reduce(((e,t)=>(e.push(`\t\t'${g(t)}': '${g(s[t])}'`),e)),[]).join(",\n")}\n\t}\n};\n`;await O(e,t)}(s,r),"json"===c&&await async function(e,s){const t=`${JSON.stringify({"custom-selectors":s},null,"\t")}\n`;await O(e,t)}(s,r),"mjs"===c&&await async function(e,s){const t=`export const customSelectors = {\n${Object.keys(s).reduce(((e,t)=>(e.push(`\t'${g(t)}': '${g(s[t])}'`),e)),[]).join(",\n")}\n};\n`;await O(e,t)}(s,r)}}})))}const d=e=>Object.keys(e).reduce(((s,t)=>(s[t]=String(e[t]),s)),{}),O=(e,t)=>new Promise(((n,o)=>{s.writeFile(e,t,(e=>{e?o(e):n()}))})),g=e=>e.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r"),w=s=>{const t=Boolean(Object(s).preserve),n=[].concat(Object(s).importFrom||[]),r=[].concat(Object(s).exportTo||[]),a=m(n),i=Symbol("customSelectorHelper");return{postcssPlugin:"postcss-custom-selectors",Once:async(e,s)=>{s[i]=Object.assign(await a,o(e,{preserve:t})),await b(s[i],r)},Rule:(s,n)=>{s.selector.includes(":--")&&((s,t,n)=>{const o=e((e=>{c(e,t)})).processSync(s.selector);o!==s.selector&&(s.cloneBefore({selector:o}),n.preserve||s.remove())})(s,n[i],{preserve:t})}}};w.postcss=!0;export{w as default};
