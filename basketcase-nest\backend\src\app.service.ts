import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHealth() {
    return {
      status: 'OK',
      timestamp: new Date().toISOString(),
      message: 'BasketCase API is running successfully',
      version: '1.0.0',
    };
  }

  getInfo() {
    return {
      name: 'BasketCase API',
      description: 'Price comparison platform for South African grocery stores',
      version: '1.0.0',
      documentation: '/api/docs',
      features: [
        'Product price comparison',
        'Store management',
        'Automated web scraping',
        'Real-time price tracking',
        'Savings calculation',
      ],
    };
  }
}
