/* Footer Styles */
.footer {
  background-color: var(--dark-color);
  color: white;
  margin-top: auto;
  padding: 2rem 0 1rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.footer-logo {
  font-size: 1.5rem;
}

.footer-brand-name {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 0;
}

.footer-description {
  color: #adb5bd;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.footer-section-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: white;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-link {
  color: #adb5bd;
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition);
}

.footer-link:hover {
  color: white;
  text-decoration: none;
}

.footer-bottom {
  border-top: 1px solid #495057;
  padding-top: 1rem;
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: center;
}

.footer-copyright {
  color: #adb5bd;
  font-size: 0.9rem;
}

.footer-disclaimer {
  color: #6c757d;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .footer {
    padding: 1.5rem 0 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .footer-section {
    gap: 0.75rem;
  }
  
  .footer-bottom-content {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 0 0.5rem;
  }
  
  .footer-content {
    gap: 1rem;
    margin-bottom: 1rem;
  }
}
