# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/basketcase

# Scraping Configuration
SCRAPE_INTERVAL_HOURS=24
MAX_CONCURRENT_SCRAPERS=3

# API Keys (for future use)
# SPAR_API_KEY=your_spar_api_key_here
# CHECKERS_API_KEY=your_checkers_api_key_here
# PICKNPAY_API_KEY=your_picknpay_api_key_here
# WOOLWORTHS_API_KEY=your_woolworths_api_key_here

# Security
JWT_SECRET=your_jwt_secret_here_change_in_production

# Logging
LOG_LEVEL=info
