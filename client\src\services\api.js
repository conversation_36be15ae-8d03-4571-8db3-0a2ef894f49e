import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Products API
export const productsAPI = {
  // Search products
  search: async (params = {}) => {
    const response = await api.get('/products', { params });
    return response.data;
  },

  // Get single product with price comparison
  getById: async (id, location = null) => {
    const params = location ? { lat: location.lat, lng: location.lng } : {};
    const response = await api.get(`/products/${id}`, { params });
    return response.data;
  },

  // Get price history for a product
  getPriceHistory: async (id, params = {}) => {
    const response = await api.get(`/products/${id}/prices`, { params });
    return response.data;
  },

  // Get product categories
  getCategories: async () => {
    const response = await api.get('/products/meta/categories');
    return response.data;
  },

  // Get brands
  getBrands: async (category = null) => {
    const params = category ? { category } : {};
    const response = await api.get('/products/meta/brands', { params });
    return response.data;
  },
};

// Stores API
export const storesAPI = {
  // Get all stores
  getAll: async (params = {}) => {
    const response = await api.get('/stores', { params });
    return response.data;
  },

  // Get single store
  getById: async (id) => {
    const response = await api.get(`/stores/${id}`);
    return response.data;
  },

  // Find nearby stores
  findNearby: async (lat, lng, maxDistance = 10000, name = null) => {
    const params = { lat, lng, maxDistance };
    if (name) params.name = name;
    const response = await api.get('/stores/location/nearby', { params });
    return response.data;
  },

  // Get store chains
  getChains: async () => {
    const response = await api.get('/stores/meta/chains');
    return response.data;
  },

  // Get provinces
  getProvinces: async () => {
    const response = await api.get('/stores/meta/provinces');
    return response.data;
  },

  // Get cities
  getCities: async (province = null) => {
    const params = province ? { province } : {};
    const response = await api.get('/stores/meta/cities', { params });
    return response.data;
  },
};

// Prices API
export const pricesAPI = {
  // Compare prices for a product
  compare: async (productId, location = null, options = {}) => {
    const params = { productId, ...options };
    if (location) {
      params.lat = location.lat;
      params.lng = location.lng;
    }
    const response = await api.get('/prices/compare', { params });
    return response.data;
  },

  // Get trending prices
  getTrending: async (days = 7) => {
    const response = await api.get('/prices/trending', { params: { days } });
    return response.data;
  },

  // Get current promotions
  getPromotions: async (params = {}) => {
    const response = await api.get('/prices/promotions', { params });
    return response.data;
  },

  // Get price history for specific product at specific store
  getHistory: async (productId, storeId, days = 30) => {
    const response = await api.get(`/prices/history/${productId}/${storeId}`, { 
      params: { days } 
    });
    return response.data;
  },

  // Get price analytics
  getAnalytics: async (params = {}) => {
    const response = await api.get('/prices/analytics', { params });
    return response.data;
  },
};

// Scheduler API
export const schedulerAPI = {
  // Get scheduler status
  getStatus: async () => {
    const response = await api.get('/scheduler/status');
    return response.data;
  },

  // Get health check
  getHealth: async () => {
    const response = await api.get('/scheduler/health');
    return response.data;
  },

  // Start scheduler
  start: async () => {
    const response = await api.post('/scheduler/start');
    return response.data;
  },

  // Stop scheduler
  stop: async () => {
    const response = await api.post('/scheduler/stop');
    return response.data;
  },

  // Run manual scraping job
  runJob: async (jobType = 'manual') => {
    const response = await api.post('/scheduler/run', { jobType });
    return response.data;
  },

  // Get latest results
  getLatestResults: async () => {
    const response = await api.get('/scheduler/results/latest');
    return response.data;
  },
};

// Utility functions
export const apiUtils = {
  // Handle API errors
  handleError: (error) => {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data.message || 'Server error',
        status: error.response.status,
        data: error.response.data,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'Network error - please check your connection',
        status: 0,
      };
    } else {
      // Something else happened
      return {
        message: error.message || 'Unknown error',
        status: -1,
      };
    }
  },

  // Format price for display
  formatPrice: (price, currency = 'ZAR') => {
    if (typeof price !== 'number') return 'N/A';
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency,
    }).format(price);
  },

  // Calculate savings
  calculateSavings: (currentPrice, originalPrice) => {
    if (!originalPrice || originalPrice <= currentPrice) return null;
    
    const savings = originalPrice - currentPrice;
    const percentage = (savings / originalPrice) * 100;
    
    return {
      amount: savings,
      percentage: Math.round(percentage * 100) / 100,
    };
  },

  // Format distance
  formatDistance: (distance) => {
    if (typeof distance !== 'number') return 'N/A';
    
    if (distance < 1) {
      return `${Math.round(distance * 1000)}m`;
    } else {
      return `${Math.round(distance * 10) / 10}km`;
    }
  },

  // Debounce function for search
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
};

export default api;
