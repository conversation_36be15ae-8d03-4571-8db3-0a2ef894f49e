import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import SearchBar from './SearchBar';
import LocationSelector from './LocationSelector';
import './Header.css';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const handleSearch = (query, filters) => {
    const searchParams = new URLSearchParams();
    if (query) searchParams.set('q', query);
    if (filters.category) searchParams.set('category', filters.category);
    if (filters.brand) searchParams.set('brand', filters.brand);
    
    navigate(`/?${searchParams.toString()}`);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="header">
      <div className="header-container">
        {/* Logo and Brand */}
        <div className="header-brand">
          <Link to="/" className="brand-link">
            <div className="logo">
              🛒
            </div>
            <span className="brand-name">BasketCase</span>
          </Link>
        </div>

        {/* Search Bar */}
        <div className="header-search">
          <SearchBar onSearch={handleSearch} />
        </div>

        {/* Location Selector */}
        <div className="header-location">
          <LocationSelector />
        </div>

        {/* Navigation Menu */}
        <nav className={`header-nav ${isMenuOpen ? 'nav-open' : ''}`}>
          <ul className="nav-list">
            <li className="nav-item">
              <Link to="/" className="nav-link">
                Home
              </Link>
            </li>
            <li className="nav-item">
              <Link to="/compare" className="nav-link">
                Compare
              </Link>
            </li>
            <li className="nav-item">
              <Link to="/stores" className="nav-link">
                Stores
              </Link>
            </li>
          </ul>
        </nav>

        {/* Mobile Menu Toggle */}
        <button 
          className="menu-toggle"
          onClick={toggleMenu}
          aria-label="Toggle menu"
        >
          <span className="hamburger"></span>
          <span className="hamburger"></span>
          <span className="hamburger"></span>
        </button>
      </div>
    </header>
  );
};

export default Header;
