{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport ProductGrid from '../components/ProductGrid';\nimport FilterPanel from '../components/FilterPanel';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport ScrapingPanel from '../components/ScrapingPanel';\nimport './HomePage.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [products, setProducts] = useState([]);\n  const [biggestSavings, setBiggestSavings] = useState([]);\n  const [trendingProducts, setTrendingProducts] = useState([]);\n  const [promotions, setPromotions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState(null);\n  const [filters, setFilters] = useState({\n    query: '',\n    category: '',\n    brand: '',\n    province: '',\n    city: '',\n    sortBy: 'relevance'\n  });\n\n  // Load products\n  const loadProducts = async (page = 1) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: '20',\n        ...filters\n      });\n      const response = await fetch(`http://localhost:5000/api/products?${params}`);\n      const data = await response.json();\n      if (data.success) {\n        setProducts(data.products || []);\n        setPagination(data.pagination);\n      } else {\n        setError('Failed to load products');\n      }\n    } catch (err) {\n      setError('Failed to load products. Please try again.');\n      console.error('Error loading products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load biggest savings\n  const loadBiggestSavings = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/biggest-savings?limit=6');\n      const data = await response.json();\n      setBiggestSavings(data.biggestSavings || []);\n    } catch (err) {\n      console.error('Error loading biggest savings:', err);\n    }\n  };\n\n  // Load trending products\n  const loadTrendingProducts = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/trending?days=7');\n      const data = await response.json();\n      setTrendingProducts(data.trending || []);\n    } catch (err) {\n      console.error('Error loading trending products:', err);\n    }\n  };\n\n  // Load promotions\n  const loadPromotions = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/promotions');\n      const data = await response.json();\n      setPromotions(data.promotions || []);\n    } catch (err) {\n      console.error('Error loading promotions:', err);\n    }\n  };\n  useEffect(() => {\n    loadProducts();\n    loadBiggestSavings();\n    loadTrendingProducts();\n    loadPromotions();\n  }, []);\n  useEffect(() => {\n    loadProducts(1);\n  }, [filters]);\n  const handleFilterChange = newFilters => {\n    setFilters(prev => ({\n      ...prev,\n      ...newFilters\n    }));\n  };\n  const handlePageChange = page => {\n    loadProducts(page);\n  };\n  const handleProductClick = product => {\n    navigate(`/product/${product._id}`);\n  };\n  const hasActiveFilters = filters.query || filters.category || filters.brand;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-container\",\n      children: [!hasActiveFilters && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: \"Compare Grocery Prices Across South Africa\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: \"Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this), !hasActiveFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid mb-4\",\n        children: [biggestSavings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-center mb-4\",\n              children: \"\\uD83D\\uDD25 Biggest Savings Today\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: biggestSavings.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-lg-2 col-md-4 col-sm-6 mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card h-100 border-danger\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-body text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"badge bg-danger mb-2\",\n                      children: [\"Save R\", item.savings.amount.toFixed(2), \" (\", item.savings.percentage.toFixed(0), \"% off)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"card-title\",\n                      children: item.product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"card-text small text-muted\",\n                      children: [item.product.brand, \" at \", item.store.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"h5 text-success\",\n                        children: [\"R\", item.price.current.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 152,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 153,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-decoration-line-through text-muted\",\n                        children: [\"was R\", item.price.original.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 154,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-primary mt-2\",\n                      onClick: () => handleProductClick(item.product),\n                      children: \"View Deal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 25\n                }, this)\n              }, `${item.product._id}-${item.store._id}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"card-title\",\n                  children: \"\\uD83D\\uDCC8 Trending Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: trendingProducts.slice(0, 3).map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-12 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center p-2 border rounded\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-1\",\n                          children: item.productName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 182,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: [\"Avg: R\", item.avgPrice.toFixed(2)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 183,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-end\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: [\"R\", item.minPrice.toFixed(2), \" - R\", item.maxPrice.toFixed(2)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 186,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 185,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 27\n                    }, this)\n                  }, item._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"card-title\",\n                  children: \"\\uD83C\\uDFF7\\uFE0F Current Promotions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"row\",\n                  children: promotions.slice(0, 3).map((promo, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-12 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center p-2 border rounded\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-1\",\n                          children: promo.product.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 205,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: promo.store.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 206,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-end\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-success\",\n                          children: [\"R\", promo.price.current.toFixed(2)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 209,\n                          columnNumber: 31\n                        }, this), promo.price.original > promo.price.current && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 212,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-decoration-line-through text-muted\",\n                            children: [\"R\", promo.price.original.toFixed(2)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 213,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4 col-lg-3\",\n            children: /*#__PURE__*/_jsxDEV(FilterPanel, {\n              filters: filters,\n              onFilterChange: handleFilterChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-8 col-lg-9\",\n            children: [hasActiveFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-results-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"results-title\",\n                children: filters.query ? `Search results for \"${filters.query}\"` : 'Products'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), pagination && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"results-info\",\n                children: [\"Showing \", (pagination.current - 1) * 20 + 1, \" - \", Math.min(pagination.current * 20, pagination.totalItems), \" of \", pagination.totalItems, \" products\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 27\n            }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              message: error,\n              onRetry: () => loadProducts()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 25\n            }, this), !loading && !error && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [products.length > 0 ? /*#__PURE__*/_jsxDEV(ProductGrid, {\n                products: products,\n                onProductClick: handleProductClick\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 21\n              }, this) : hasActiveFilters ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-results\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"No products found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Try adjusting your search criteria or filters.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this) : null, pagination && pagination.total > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pagination\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"pagination-button\",\n                  disabled: pagination.current <= 1,\n                  onClick: () => handlePageChange(pagination.current - 1),\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"pagination-info\",\n                  children: [\"Page \", pagination.current, \" of \", pagination.total]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"pagination-button\",\n                  disabled: pagination.current >= pagination.total,\n                  onClick: () => handlePageChange(pagination.current + 1),\n                  children: \"Next\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"t4qZVSuOXqOHYN48fCjoXJvKFlA=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "ProductGrid", "FilterPanel", "LoadingSpinner", "ErrorMessage", "ScrapingPanel", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HomePage", "_s", "navigate", "products", "setProducts", "biggestSavings", "setBiggestSavings", "trendingProducts", "setTrendingProducts", "promotions", "setPromotions", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "filters", "setFilters", "query", "category", "brand", "province", "city", "sortBy", "loadProducts", "page", "params", "URLSearchParams", "toString", "limit", "response", "fetch", "data", "json", "success", "err", "console", "loadBiggestSavings", "loadTrendingProducts", "trending", "loadPromotions", "handleFilterChange", "newFilters", "prev", "handlePageChange", "handleProductClick", "product", "_id", "hasActiveFilters", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "item", "savings", "amount", "toFixed", "percentage", "name", "store", "price", "current", "original", "onClick", "slice", "productName", "avgPrice", "minPrice", "maxPrice", "promo", "index", "onFilterChange", "Math", "min", "totalItems", "message", "onRetry", "onProductClick", "total", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/client/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport ProductGrid from '../components/ProductGrid';\nimport FilterPanel from '../components/FilterPanel';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport ScrapingPanel from '../components/ScrapingPanel';\nimport './HomePage.css';\n\nconst HomePage = () => {\n  const navigate = useNavigate();\n  const [products, setProducts] = useState([]);\n  const [biggestSavings, setBiggestSavings] = useState([]);\n  const [trendingProducts, setTrendingProducts] = useState([]);\n  const [promotions, setPromotions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState(null);\n  const [filters, setFilters] = useState({\n    query: '',\n    category: '',\n    brand: '',\n    province: '',\n    city: '',\n    sortBy: 'relevance'\n  });\n\n  // Load products\n  const loadProducts = async (page = 1) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: '20',\n        ...filters\n      });\n\n      const response = await fetch(`http://localhost:5000/api/products?${params}`);\n      const data = await response.json();\n      \n      if (data.success) {\n        setProducts(data.products || []);\n        setPagination(data.pagination);\n      } else {\n        setError('Failed to load products');\n      }\n    } catch (err) {\n      setError('Failed to load products. Please try again.');\n      console.error('Error loading products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load biggest savings\n  const loadBiggestSavings = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/biggest-savings?limit=6');\n      const data = await response.json();\n      setBiggestSavings(data.biggestSavings || []);\n    } catch (err) {\n      console.error('Error loading biggest savings:', err);\n    }\n  };\n\n  // Load trending products\n  const loadTrendingProducts = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/trending?days=7');\n      const data = await response.json();\n      setTrendingProducts(data.trending || []);\n    } catch (err) {\n      console.error('Error loading trending products:', err);\n    }\n  };\n\n  // Load promotions\n  const loadPromotions = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/prices/promotions');\n      const data = await response.json();\n      setPromotions(data.promotions || []);\n    } catch (err) {\n      console.error('Error loading promotions:', err);\n    }\n  };\n\n  useEffect(() => {\n    loadProducts();\n    loadBiggestSavings();\n    loadTrendingProducts();\n    loadPromotions();\n  }, []);\n\n  useEffect(() => {\n    loadProducts(1);\n  }, [filters]);\n\n  const handleFilterChange = (newFilters) => {\n    setFilters(prev => ({ ...prev, ...newFilters }));\n  };\n\n  const handlePageChange = (page) => {\n    loadProducts(page);\n  };\n\n  const handleProductClick = (product) => {\n    navigate(`/product/${product._id}`);\n  };\n\n  const hasActiveFilters = filters.query || filters.category || filters.brand;\n\n  return (\n    <div className=\"home-page\">\n      <div className=\"home-container\">\n        \n        {/* Hero Section */}\n        {!hasActiveFilters && (\n          <section className=\"hero-section\">\n            <div className=\"hero-content\">\n              <h1 className=\"hero-title\">\n                Compare Grocery Prices Across South Africa\n              </h1>\n              <p className=\"hero-subtitle\">\n                Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths\n              </p>\n            </div>\n          </section>\n        )}\n\n        {/* Featured Sections */}\n        {!hasActiveFilters && (\n          <div className=\"container-fluid mb-4\">\n            {/* Biggest Savings */}\n            {biggestSavings.length > 0 && (\n              <div className=\"row mb-4\">\n                <div className=\"col-12\">\n                  <h2 className=\"text-center mb-4\">🔥 Biggest Savings Today</h2>\n                  <div className=\"row\">\n                    {biggestSavings.map(item => (\n                      <div key={`${item.product._id}-${item.store._id}`} className=\"col-lg-2 col-md-4 col-sm-6 mb-3\">\n                        <div className=\"card h-100 border-danger\">\n                          <div className=\"card-body text-center\">\n                            <div className=\"badge bg-danger mb-2\">\n                              Save R{item.savings.amount.toFixed(2)} ({item.savings.percentage.toFixed(0)}% off)\n                            </div>\n                            <h6 className=\"card-title\">{item.product.name}</h6>\n                            <p className=\"card-text small text-muted\">{item.product.brand} at {item.store.name}</p>\n                            <div className=\"price-info\">\n                              <span className=\"h5 text-success\">R{item.price.current.toFixed(2)}</span>\n                              <br />\n                              <small className=\"text-decoration-line-through text-muted\">was R{item.price.original.toFixed(2)}</small>\n                            </div>\n                            <button \n                              className=\"btn btn-sm btn-outline-primary mt-2\"\n                              onClick={() => handleProductClick(item.product)}\n                            >\n                              View Deal\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Trending and Promotions */}\n            <div className=\"row mb-4\">\n              <div className=\"col-md-6\">\n                <div className=\"card\">\n                  <div className=\"card-body\">\n                    <h3 className=\"card-title\">📈 Trending Products</h3>\n                    <div className=\"row\">\n                      {trendingProducts.slice(0, 3).map(item => (\n                        <div key={item._id} className=\"col-12 mb-2\">\n                          <div className=\"d-flex justify-content-between align-items-center p-2 border rounded\">\n                            <div>\n                              <h6 className=\"mb-1\">{item.productName}</h6>\n                              <small className=\"text-muted\">Avg: R{item.avgPrice.toFixed(2)}</small>\n                            </div>\n                            <div className=\"text-end\">\n                              <small className=\"text-muted\">R{item.minPrice.toFixed(2)} - R{item.maxPrice.toFixed(2)}</small>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"col-md-6\">\n                <div className=\"card\">\n                  <div className=\"card-body\">\n                    <h3 className=\"card-title\">🏷️ Current Promotions</h3>\n                    <div className=\"row\">\n                      {promotions.slice(0, 3).map((promo, index) => (\n                        <div key={index} className=\"col-12 mb-2\">\n                          <div className=\"d-flex justify-content-between align-items-center p-2 border rounded\">\n                            <div>\n                              <h6 className=\"mb-1\">{promo.product.name}</h6>\n                              <small className=\"text-muted\">{promo.store.name}</small>\n                            </div>\n                            <div className=\"text-end\">\n                              <span className=\"text-success\">R{promo.price.current.toFixed(2)}</span>\n                              {promo.price.original > promo.price.current && (\n                                <>\n                                  <br />\n                                  <small className=\"text-decoration-line-through text-muted\">R{promo.price.original.toFixed(2)}</small>\n                                </>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Main Content */}\n        <div className=\"container-fluid\">\n          <div className=\"row\">\n            <div className=\"col-md-4 col-lg-3\">\n              <FilterPanel\n                filters={filters}\n                onFilterChange={handleFilterChange}\n              />\n            </div>\n\n            <div className=\"col-md-8 col-lg-9\">\n              {hasActiveFilters && (\n                <div className=\"search-results-header\">\n                  <h2 className=\"results-title\">\n                    {filters.query ? `Search results for \"${filters.query}\"` : 'Products'}\n                  </h2>\n                  {pagination && (\n                    <div className=\"results-info\">\n                      Showing {((pagination.current - 1) * 20) + 1} - {Math.min(pagination.current * 20, pagination.totalItems)} of {pagination.totalItems} products\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {loading && <LoadingSpinner />}\n              {error && <ErrorMessage message={error} onRetry={() => loadProducts()} />}\n\n              {!loading && !error && (\n                <>\n                  {products.length > 0 ? (\n                    <ProductGrid\n                      products={products}\n                      onProductClick={handleProductClick}\n                    />\n                  ) : hasActiveFilters ? (\n                    <div className=\"no-results\">\n                      <h3>No products found</h3>\n                      <p>Try adjusting your search criteria or filters.</p>\n                    </div>\n                  ) : null}\n\n                  {pagination && pagination.total > 1 && (\n                    <div className=\"pagination\">\n                      <button\n                        className=\"pagination-button\"\n                        disabled={pagination.current <= 1}\n                        onClick={() => handlePageChange(pagination.current - 1)}\n                      >\n                        Previous\n                      </button>\n                      \n                      <span className=\"pagination-info\">\n                        Page {pagination.current} of {pagination.total}\n                      </span>\n                      \n                      <button\n                        className=\"pagination-button\"\n                        disabled={pagination.current >= pagination.total}\n                        onClick={() => handlePageChange(pagination.current + 1)}\n                      >\n                        Next\n                      </button>\n                    </div>\n                  )}\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC;IACrC+B,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IACvC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMa,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCF,IAAI,EAAEA,IAAI,CAACG,QAAQ,CAAC,CAAC;QACrBC,KAAK,EAAE,IAAI;QACX,GAAGb;MACL,CAAC,CAAC;MAEF,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsCL,MAAM,EAAE,CAAC;MAC5E,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB/B,WAAW,CAAC6B,IAAI,CAAC9B,QAAQ,IAAI,EAAE,CAAC;QAChCa,aAAa,CAACiB,IAAI,CAAClB,UAAU,CAAC;MAChC,CAAC,MAAM;QACLD,QAAQ,CAAC,yBAAyB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOsB,GAAG,EAAE;MACZtB,QAAQ,CAAC,4CAA4C,CAAC;MACtDuB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEuB,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMC,KAAK,CAAC,0DAA0D,CAAC;MACxF,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC5B,iBAAiB,CAAC2B,IAAI,CAAC5B,cAAc,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAO+B,GAAG,EAAE;MACZC,OAAO,CAACxB,KAAK,CAAC,gCAAgC,EAAEuB,GAAG,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,kDAAkD,CAAC;MAChF,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC1B,mBAAmB,CAACyB,IAAI,CAACO,QAAQ,IAAI,EAAE,CAAC;IAC1C,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZC,OAAO,CAACxB,KAAK,CAAC,kCAAkC,EAAEuB,GAAG,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6C,CAAC;MAC3E,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCxB,aAAa,CAACuB,IAAI,CAACxB,UAAU,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZC,OAAO,CAACxB,KAAK,CAAC,2BAA2B,EAAEuB,GAAG,CAAC;IACjD;EACF,CAAC;EAED/C,SAAS,CAAC,MAAM;IACdoC,YAAY,CAAC,CAAC;IACda,kBAAkB,CAAC,CAAC;IACpBC,oBAAoB,CAAC,CAAC;IACtBE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENpD,SAAS,CAAC,MAAM;IACdoC,YAAY,CAAC,CAAC,CAAC;EACjB,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC;EAEb,MAAMyB,kBAAkB,GAAIC,UAAU,IAAK;IACzCzB,UAAU,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD;IAAW,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAME,gBAAgB,GAAInB,IAAI,IAAK;IACjCD,YAAY,CAACC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoB,kBAAkB,GAAIC,OAAO,IAAK;IACtC7C,QAAQ,CAAC,YAAY6C,OAAO,CAACC,GAAG,EAAE,CAAC;EACrC,CAAC;EAED,MAAMC,gBAAgB,GAAGhC,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACI,KAAK;EAE3E,oBACExB,OAAA;IAAKqD,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBtD,OAAA;MAAKqD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAG5B,CAACF,gBAAgB,iBAChBpD,OAAA;QAASqD,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC/BtD,OAAA;UAAKqD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtD,OAAA;YAAIqD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1D,OAAA;YAAGqD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV,EAGA,CAACN,gBAAgB,iBAChBpD,OAAA;QAAKqD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,GAElC9C,cAAc,CAACmD,MAAM,GAAG,CAAC,iBACxB3D,OAAA;UAAKqD,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBtD,OAAA;YAAKqD,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBtD,OAAA;cAAIqD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9D1D,OAAA;cAAKqD,SAAS,EAAC,KAAK;cAAAC,QAAA,EACjB9C,cAAc,CAACoD,GAAG,CAACC,IAAI,iBACtB7D,OAAA;gBAAmDqD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,eAC5FtD,OAAA;kBAAKqD,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,eACvCtD,OAAA;oBAAKqD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCtD,OAAA;sBAAKqD,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,GAAC,QAC9B,EAACO,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACH,IAAI,CAACC,OAAO,CAACG,UAAU,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,QAC9E;oBAAA;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN1D,OAAA;sBAAIqD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEO,IAAI,CAACX,OAAO,CAACgB;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACnD1D,OAAA;sBAAGqD,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GAAEO,IAAI,CAACX,OAAO,CAAC1B,KAAK,EAAC,MAAI,EAACqC,IAAI,CAACM,KAAK,CAACD,IAAI;oBAAA;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvF1D,OAAA;sBAAKqD,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBtD,OAAA;wBAAMqD,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,GAAC,GAAC,EAACO,IAAI,CAACO,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzE1D,OAAA;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN1D,OAAA;wBAAOqD,SAAS,EAAC,yCAAyC;wBAAAC,QAAA,GAAC,OAAK,EAACO,IAAI,CAACO,KAAK,CAACE,QAAQ,CAACN,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACN1D,OAAA;sBACEqD,SAAS,EAAC,qCAAqC;sBAC/CkB,OAAO,EAAEA,CAAA,KAAMtB,kBAAkB,CAACY,IAAI,CAACX,OAAO,CAAE;sBAAAI,QAAA,EACjD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GApBE,GAAGG,IAAI,CAACX,OAAO,CAACC,GAAG,IAAIU,IAAI,CAACM,KAAK,CAAChB,GAAG,EAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqB5C,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD1D,OAAA;UAAKqD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBtD,OAAA;YAAKqD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBtD,OAAA;cAAKqD,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtD,OAAA;gBAAKqD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBtD,OAAA;kBAAIqD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpD1D,OAAA;kBAAKqD,SAAS,EAAC,KAAK;kBAAAC,QAAA,EACjB5C,gBAAgB,CAAC8D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACZ,GAAG,CAACC,IAAI,iBACpC7D,OAAA;oBAAoBqD,SAAS,EAAC,aAAa;oBAAAC,QAAA,eACzCtD,OAAA;sBAAKqD,SAAS,EAAC,sEAAsE;sBAAAC,QAAA,gBACnFtD,OAAA;wBAAAsD,QAAA,gBACEtD,OAAA;0BAAIqD,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAEO,IAAI,CAACY;wBAAW;0BAAAlB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC5C1D,OAAA;0BAAOqD,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAC,QAAM,EAACO,IAAI,CAACa,QAAQ,CAACV,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE,CAAC,eACN1D,OAAA;wBAAKqD,SAAS,EAAC,UAAU;wBAAAC,QAAA,eACvBtD,OAAA;0BAAOqD,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAC,GAAC,EAACO,IAAI,CAACc,QAAQ,CAACX,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI,EAACH,IAAI,CAACe,QAAQ,CAACZ,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GATEG,IAAI,CAACV,GAAG;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1D,OAAA;YAAKqD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBtD,OAAA;cAAKqD,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtD,OAAA;gBAAKqD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBtD,OAAA;kBAAIqD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtD1D,OAAA;kBAAKqD,SAAS,EAAC,KAAK;kBAAAC,QAAA,EACjB1C,UAAU,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACZ,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBACvC9E,OAAA;oBAAiBqD,SAAS,EAAC,aAAa;oBAAAC,QAAA,eACtCtD,OAAA;sBAAKqD,SAAS,EAAC,sEAAsE;sBAAAC,QAAA,gBACnFtD,OAAA;wBAAAsD,QAAA,gBACEtD,OAAA;0BAAIqD,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAEuB,KAAK,CAAC3B,OAAO,CAACgB;wBAAI;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC9C1D,OAAA;0BAAOqD,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEuB,KAAK,CAACV,KAAK,CAACD;wBAAI;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACN1D,OAAA;wBAAKqD,SAAS,EAAC,UAAU;wBAAAC,QAAA,gBACvBtD,OAAA;0BAAMqD,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,GAAC,EAACuB,KAAK,CAACT,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,CAAC,CAAC;wBAAA;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACtEmB,KAAK,CAACT,KAAK,CAACE,QAAQ,GAAGO,KAAK,CAACT,KAAK,CAACC,OAAO,iBACzCrE,OAAA,CAAAE,SAAA;0BAAAoD,QAAA,gBACEtD,OAAA;4BAAAuD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACN1D,OAAA;4BAAOqD,SAAS,EAAC,yCAAyC;4BAAAC,QAAA,GAAC,GAAC,EAACuB,KAAK,CAACT,KAAK,CAACE,QAAQ,CAACN,OAAO,CAAC,CAAC,CAAC;0BAAA;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA,eACrG,CACH;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAfEoB,KAAK;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgBV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD1D,OAAA;QAAKqD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BtD,OAAA;UAAKqD,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBtD,OAAA;YAAKqD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCtD,OAAA,CAACL,WAAW;cACVyB,OAAO,EAAEA,OAAQ;cACjB2D,cAAc,EAAElC;YAAmB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1D,OAAA;YAAKqD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/BF,gBAAgB,iBACfpD,OAAA;cAAKqD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCtD,OAAA;gBAAIqD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC1BlC,OAAO,CAACE,KAAK,GAAG,uBAAuBF,OAAO,CAACE,KAAK,GAAG,GAAG;cAAU;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,EACJxC,UAAU,iBACTlB,OAAA;gBAAKqD,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAC,UACpB,EAAE,CAACpC,UAAU,CAACmD,OAAO,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,EAAC,KAAG,EAACW,IAAI,CAACC,GAAG,CAAC/D,UAAU,CAACmD,OAAO,GAAG,EAAE,EAAEnD,UAAU,CAACgE,UAAU,CAAC,EAAC,MAAI,EAAChE,UAAU,CAACgE,UAAU,EAAC,WACvI;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EAEA5C,OAAO,iBAAId,OAAA,CAACJ,cAAc;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7B1C,KAAK,iBAAIhB,OAAA,CAACH,YAAY;cAACsF,OAAO,EAAEnE,KAAM;cAACoE,OAAO,EAAEA,CAAA,KAAMxD,YAAY,CAAC;YAAE;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAExE,CAAC5C,OAAO,IAAI,CAACE,KAAK,iBACjBhB,OAAA,CAAAE,SAAA;cAAAoD,QAAA,GACGhD,QAAQ,CAACqD,MAAM,GAAG,CAAC,gBAClB3D,OAAA,CAACN,WAAW;gBACVY,QAAQ,EAAEA,QAAS;gBACnB+E,cAAc,EAAEpC;cAAmB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,GACAN,gBAAgB,gBAClBpD,OAAA;gBAAKqD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtD,OAAA;kBAAAsD,QAAA,EAAI;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1B1D,OAAA;kBAAAsD,QAAA,EAAG;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,GACJ,IAAI,EAEPxC,UAAU,IAAIA,UAAU,CAACoE,KAAK,GAAG,CAAC,iBACjCtF,OAAA;gBAAKqD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtD,OAAA;kBACEqD,SAAS,EAAC,mBAAmB;kBAC7BkC,QAAQ,EAAErE,UAAU,CAACmD,OAAO,IAAI,CAAE;kBAClCE,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAAC9B,UAAU,CAACmD,OAAO,GAAG,CAAC,CAAE;kBAAAf,QAAA,EACzD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAET1D,OAAA;kBAAMqD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAAC,OAC3B,EAACpC,UAAU,CAACmD,OAAO,EAAC,MAAI,EAACnD,UAAU,CAACoE,KAAK;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eAEP1D,OAAA;kBACEqD,SAAS,EAAC,mBAAmB;kBAC7BkC,QAAQ,EAAErE,UAAU,CAACmD,OAAO,IAAInD,UAAU,CAACoE,KAAM;kBACjDf,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAAC9B,UAAU,CAACmD,OAAO,GAAG,CAAC,CAAE;kBAAAf,QAAA,EACzD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA,eACD,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAlSID,QAAQ;EAAA,QACKV,WAAW;AAAA;AAAA+F,EAAA,GADxBrF,QAAQ;AAoSd,eAAeA,QAAQ;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}