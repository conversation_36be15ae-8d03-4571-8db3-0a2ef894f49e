import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type ProductDocument = Product & Document;

@Schema({ timestamps: true })
export class Product {
  @ApiProperty({ description: 'Product name' })
  @Prop({ required: true, index: true })
  name: string;

  @ApiProperty({ description: 'Product brand' })
  @Prop({ required: true, index: true })
  brand: string;

  @ApiProperty({ description: 'Product category' })
  @Prop({ required: true, index: true })
  category: string;

  @ApiProperty({ description: 'Product description' })
  @Prop()
  description?: string;

  @ApiProperty({ description: 'Product images' })
  @Prop([{
    url: { type: String, required: true },
    isPrimary: { type: Boolean, default: false },
    alt: String,
  }])
  images: Array<{
    url: string;
    isPrimary: boolean;
    alt?: string;
  }>;

  @ApiProperty({ description: 'Product barcode' })
  @Prop({ index: true })
  barcode?: string;

  @ApiProperty({ description: 'Product specifications' })
  @Prop({
    weight: String,
    volume: String,
    dimensions: String,
    ingredients: [String],
    nutritionalInfo: Object,
  })
  specifications?: {
    weight?: string;
    volume?: string;
    dimensions?: string;
    ingredients?: string[];
    nutritionalInfo?: Record<string, any>;
  };

  @ApiProperty({ description: 'Whether product is active' })
  @Prop({ default: true, index: true })
  isActive: boolean;

  @ApiProperty({ description: 'Last scraped timestamp' })
  @Prop()
  lastScraped?: Date;

  @ApiProperty({ description: 'Recently updated flag' })
  @Prop({ default: false })
  isRecentlyUpdated?: boolean;
}

export const ProductSchema = SchemaFactory.createForClass(Product);
