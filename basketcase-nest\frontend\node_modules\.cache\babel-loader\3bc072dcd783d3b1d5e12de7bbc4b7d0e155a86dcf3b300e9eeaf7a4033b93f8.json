{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\basketcase-nest\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ToastContainer } from 'react-toastify';\n\n// Bootstrap CSS\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport '@fortawesome/fontawesome-free/css/all.min.css';\nimport 'react-toastify/dist/ReactToastify.css';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\n\n// Pages\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport ProductDetailPage from './pages/ProductDetailPage';\nimport StoresPage from './pages/StoresPage';\nimport ComparePage from './pages/ComparePage';\nimport AboutPage from './pages/AboutPage';\n\n// Styles\nimport './App.css';\n\n// Create a client\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutes\n    }\n  }\n});\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"main-content\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/products\",\n              element: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/products/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProductDetailPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/stores\",\n              element: /*#__PURE__*/_jsxDEV(StoresPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/compare\",\n              element: /*#__PURE__*/_jsxDEV(ComparePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/about\",\n              element: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n          position: \"top-right\",\n          autoClose: 5000,\n          hideProgressBar: false,\n          newestOnTop: false,\n          closeOnClick: true,\n          rtl: false,\n          pauseOnFocusLoss: true,\n          draggable: true,\n          pauseOnHover: true,\n          theme: \"light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "QueryClient", "QueryClientProvider", "ToastContainer", "<PERSON><PERSON><PERSON>", "Footer", "HomePage", "ProductsPage", "ProductDetailPage", "StoresPage", "ComparePage", "AboutPage", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "App", "client", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/basketcase-nest/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ToastContainer } from 'react-toastify';\n\n// Bootstrap CSS\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport '@fortawesome/fontawesome-free/css/all.min.css';\nimport 'react-toastify/dist/ReactToastify.css';\n\n// Components\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\n\n// Pages\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport ProductDetailPage from './pages/ProductDetailPage';\nimport StoresPage from './pages/StoresPage';\nimport ComparePage from './pages/ComparePage';\nimport AboutPage from './pages/AboutPage';\n\n// Styles\nimport './App.css';\n\n// Create a client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    },\n  },\n});\n\nconst App: React.FC = () => {\n  return (\n    <QueryClientProvider client={queryClient}>\n      <Router>\n        <div className=\"App\">\n          <Navbar />\n          \n          <main className=\"main-content\">\n            <Routes>\n              <Route path=\"/\" element={<HomePage />} />\n              <Route path=\"/products\" element={<ProductsPage />} />\n              <Route path=\"/products/:id\" element={<ProductDetailPage />} />\n              <Route path=\"/stores\" element={<StoresPage />} />\n              <Route path=\"/compare\" element={<ComparePage />} />\n              <Route path=\"/about\" element={<AboutPage />} />\n            </Routes>\n          </main>\n\n          <Footer />\n\n          <ToastContainer\n            position=\"top-right\"\n            autoClose={5000}\n            hideProgressBar={false}\n            newestOnTop={false}\n            closeOnClick\n            rtl={false}\n            pauseOnFocusLoss\n            draggable\n            pauseOnHover\n            theme=\"light\"\n          />\n        </div>\n      </Router>\n    </QueryClientProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,cAAc,QAAQ,gBAAgB;;AAE/C;AACA,OAAO,sCAAsC;AAC7C,OAAO,+CAA+C;AACtD,OAAO,uCAAuC;;AAE9C;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;;AAExC;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;;AAEzC;AACA,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIb,WAAW,CAAC;EAClCc,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;AAEF,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEP,OAAA,CAACX,mBAAmB;IAACmB,MAAM,EAAEP,WAAY;IAAAQ,QAAA,eACvCT,OAAA,CAACf,MAAM;MAAAwB,QAAA,eACLT,OAAA;QAAKU,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBT,OAAA,CAACT,MAAM;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEVd,OAAA;UAAMU,SAAS,EAAC,cAAc;UAAAD,QAAA,eAC5BT,OAAA,CAACd,MAAM;YAAAuB,QAAA,gBACLT,OAAA,CAACb,KAAK;cAAC4B,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEhB,OAAA,CAACP,QAAQ;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCd,OAAA,CAACb,KAAK;cAAC4B,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEhB,OAAA,CAACN,YAAY;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDd,OAAA,CAACb,KAAK;cAAC4B,IAAI,EAAC,eAAe;cAACC,OAAO,eAAEhB,OAAA,CAACL,iBAAiB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9Dd,OAAA,CAACb,KAAK;cAAC4B,IAAI,EAAC,SAAS;cAACC,OAAO,eAAEhB,OAAA,CAACJ,UAAU;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDd,OAAA,CAACb,KAAK;cAAC4B,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEhB,OAAA,CAACH,WAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDd,OAAA,CAACb,KAAK;cAAC4B,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEhB,OAAA,CAACF,SAAS;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEPd,OAAA,CAACR,MAAM;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEVd,OAAA,CAACV,cAAc;UACb2B,QAAQ,EAAC,WAAW;UACpBC,SAAS,EAAE,IAAK;UAChBC,eAAe,EAAE,KAAM;UACvBC,WAAW,EAAE,KAAM;UACnBC,YAAY;UACZC,GAAG,EAAE,KAAM;UACXC,gBAAgB;UAChBC,SAAS;UACTC,YAAY;UACZC,KAAK,EAAC;QAAO;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B,CAAC;AAACa,EAAA,GApCIpB,GAAa;AAsCnB,eAAeA,GAAG;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}