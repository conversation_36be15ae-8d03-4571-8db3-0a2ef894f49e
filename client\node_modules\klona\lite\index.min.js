!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.klona={})}(this,(function(e){e.klona=function e(t){if("object"!=typeof t)return t;var o,n,r=Object.prototype.toString.call(t);if("[object Object]"===r){if(t.constructor!==Object&&"function"==typeof t.constructor)for(o in n=new t.constructor,t)t.hasOwnProperty(o)&&n[o]!==t[o]&&(n[o]=e(t[o]));else for(o in n={},t)"__proto__"===o?Object.defineProperty(n,o,{value:e(t[o]),configurable:!0,enumerable:!0,writable:!0}):n[o]=e(t[o]);return n}if("[object Array]"===r){for(o=t.length,n=Array(o);o--;)n[o]=e(t[o]);return n}return"[object Date]"===r?new Date(+t):"[object RegExp]"===r?((n=new RegExp(t.source,t.flags)).lastIndex=t.lastIndex,n):t}}));