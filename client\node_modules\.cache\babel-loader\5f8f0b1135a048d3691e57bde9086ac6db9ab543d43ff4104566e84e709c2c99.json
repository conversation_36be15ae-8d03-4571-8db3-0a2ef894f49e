{"ast": null, "code": "var _jsxFileName = \"c:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\pages\\\\ProductPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { productsAPI, pricesAPI, apiUtils } from '../services/api';\nimport useGeolocation from '../hooks/useGeolocation';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport ProductCard from '../components/ProductCard';\nimport Map from '../components/Map';\nimport './ProductPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    location\n  } = useGeolocation({\n    autoStart: true\n  });\n  const [product, setProduct] = useState(null);\n  const [priceComparison, setPriceComparison] = useState([]);\n  const [similarProducts, setSimilarProducts] = useState([]);\n  const [priceHistory, setPriceHistory] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedStore, setSelectedStore] = useState(null);\n  const [activeTab, setActiveTab] = useState('prices');\n\n  // Load product data\n  useEffect(() => {\n    if (id) {\n      loadProductData();\n    }\n  }, [id, location]);\n  const loadProductData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await productsAPI.getById(id, location);\n      setProduct(response.product);\n      setPriceComparison(response.priceComparison || []);\n      setSimilarProducts(response.similarProducts || []);\n\n      // Load price history for the first store if available\n      if (response.priceComparison && response.priceComparison.length > 0) {\n        const firstStore = response.priceComparison[0];\n        loadPriceHistory(id, firstStore.store._id);\n      }\n    } catch (err) {\n      setError('Failed to load product details. Please try again.');\n      console.error('Error loading product:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadPriceHistory = async (productId, storeId) => {\n    try {\n      const response = await pricesAPI.getHistory(productId, storeId, 30);\n      setPriceHistory(response.history || []);\n    } catch (err) {\n      console.error('Error loading price history:', err);\n    }\n  };\n\n  // Handle store selection for price history\n  const handleStoreSelect = storeData => {\n    setSelectedStore(storeData);\n    loadPriceHistory(id, storeData.store._id);\n  };\n\n  // Handle similar product click\n  const handleSimilarProductClick = similarProduct => {\n    navigate(`/product/${similarProduct._id}`);\n  };\n\n  // Get cheapest price info\n  const getCheapestPrice = () => {\n    if (priceComparison.length === 0) return null;\n    return priceComparison.reduce((min, current) => current.price.current < min.price.current ? current : min);\n  };\n\n  // Get price range\n  const getPriceRange = () => {\n    if (priceComparison.length === 0) return null;\n    const prices = priceComparison.map(p => p.price.current);\n    const min = Math.min(...prices);\n    const max = Math.max(...prices);\n    return {\n      min,\n      max\n    };\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      message: \"Loading product details...\",\n      fullScreen: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: error,\n      onRetry: loadProductData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 12\n    }, this);\n  }\n  if (!product) {\n    return /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: \"Product not found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 12\n    }, this);\n  }\n  const cheapestPrice = getCheapestPrice();\n  const priceRange = getPriceRange();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"breadcrumb\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate(-1),\n          className: \"back-button\",\n          children: \"\\u2190 Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"breadcrumb-separator\",\n          children: \"/\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"breadcrumb-category\",\n          children: product.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"breadcrumb-separator\",\n          children: \"/\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"breadcrumb-current\",\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-images\",\n          children: product.images && product.images.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-gallery\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.images[0].url,\n              alt: product.name,\n              className: \"main-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), product.images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"thumbnail-images\",\n              children: product.images.slice(1, 4).map((image, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n                src: image.url,\n                alt: `${product.name} ${index + 2}`,\n                className: \"thumbnail-image\"\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-image-placeholder\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"placeholder-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-title-section\",\n            children: [product.brand && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-brand\",\n              children: product.brand\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"product-title\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-category-badge\",\n              children: product.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), product.description && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-description\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), priceRange && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price-range\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"price-label\",\n                children: \"Price Range:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"price-values\",\n                children: [apiUtils.formatPrice(priceRange.min), \" - \", apiUtils.formatPrice(priceRange.max)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), cheapestPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cheapest-price\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"price-label\",\n                children: \"Best Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"price-value\",\n                children: apiUtils.formatPrice(cheapestPrice.price.current)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"store-name\",\n                children: [\"at \", cheapestPrice.store.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"store-count\",\n              children: [\"Available at \", priceComparison.length, \" store\", priceComparison.length !== 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), product.specifications && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-specifications\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Specifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"specs-grid\",\n              children: [product.specifications.weight && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spec-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"spec-label\",\n                  children: \"Weight:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"spec-value\",\n                  children: product.specifications.weight\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), product.specifications.volume && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spec-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"spec-label\",\n                  children: \"Volume:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"spec-value\",\n                  children: product.specifications.volume\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this), product.specifications.dimensions && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spec-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"spec-label\",\n                  children: \"Dimensions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"spec-value\",\n                  children: product.specifications.dimensions\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-button ${activeTab === 'prices' ? 'active' : ''}`,\n            onClick: () => setActiveTab('prices'),\n            children: [\"Price Comparison (\", priceComparison.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-button ${activeTab === 'map' ? 'active' : ''}`,\n            onClick: () => setActiveTab('map'),\n            children: \"Store Locations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-button ${activeTab === 'history' ? 'active' : ''}`,\n            onClick: () => setActiveTab('history'),\n            children: \"Price History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: [activeTab === 'prices' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"prices-tab\",\n            children: priceComparison.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price-comparison-grid\",\n              children: priceComparison.map((priceData, index) => {\n                var _priceData$savings;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `price-card ${(_priceData$savings = priceData.savings) !== null && _priceData$savings !== void 0 && _priceData$savings.isCheapest ? 'cheapest' : ''}`,\n                  onClick: () => handleStoreSelect(priceData),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-card-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"store-name\",\n                      children: priceData.store.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"store-branch\",\n                      children: priceData.store.branch\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"current-price\",\n                      children: apiUtils.formatPrice(priceData.price.current)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this), priceData.price.original > priceData.price.current && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"original-price\",\n                      children: [\"Was: \", apiUtils.formatPrice(priceData.price.original)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 29\n                    }, this), priceData.savings && priceData.savings.amount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"savings-info\",\n                      children: priceData.savings.isCheapest ? /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"cheapest-badge\",\n                        children: \"Cheapest!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 278,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"savings-amount\",\n                        children: [\"+\", apiUtils.formatPrice(priceData.savings.amount), \" more\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"availability-info\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `stock-status ${priceData.availability.inStock ? 'in-stock' : 'out-of-stock'}`,\n                      children: priceData.availability.inStock ? '✅ In Stock' : '❌ Out of Stock'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this), priceData.promotion.isOnPromotion && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"promotion-badge\",\n                    children: \"\\uD83C\\uDFF7\\uFE0F On Promotion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 27\n                  }, this)]\n                }, `${priceData.store._id}-${index}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-prices\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No price information available for this product.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), activeTab === 'map' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-tab\",\n            children: /*#__PURE__*/_jsxDEV(Map, {\n              stores: priceComparison.map(p => p.store),\n              userLocation: location,\n              height: \"400px\",\n              showUserLocation: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), activeTab === 'history' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-tab\",\n            children: selectedStore ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price-history\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [\"Price History - \", selectedStore.store.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this), priceHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"history-chart\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"history-list\",\n                  children: priceHistory.map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"history-entry\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"history-date\",\n                      children: new Date(entry.date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"history-price\",\n                      children: apiUtils.formatPrice(entry.price)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `history-change ${entry.changeType}`,\n                      children: [entry.changeType === 'increase' && '↗️', entry.changeType === 'decrease' && '↘️', entry.changeType === 'no_change' && '➡️', entry.changePercentage !== 0 && ` ${entry.changePercentage.toFixed(1)}%`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No price history available for this store.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"select-store-message\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Select a store from the Price Comparison tab to view price history.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), similarProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"similar-products\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Similar Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"similar-products-grid\",\n          children: similarProducts.map(similarProduct => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: similarProduct,\n            onClick: handleSimilarProductClick,\n            showCompareButton: false\n          }, similarProduct._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductPage, \"kpETN2vuj9RVNKALcpLk2GM7ysA=\", false, function () {\n  return [useParams, useNavigate, useGeolocation];\n});\n_c = ProductPage;\nexport default ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "productsAPI", "pricesAPI", "apiUtils", "useGeolocation", "LoadingSpinner", "ErrorMessage", "ProductCard", "Map", "jsxDEV", "_jsxDEV", "ProductPage", "_s", "id", "navigate", "location", "autoStart", "product", "setProduct", "priceComparison", "setPriceComparison", "similarProducts", "setSimilarProducts", "priceHistory", "setPriceHistory", "loading", "setLoading", "error", "setError", "selectedStore", "setSelectedStore", "activeTab", "setActiveTab", "loadProductData", "response", "getById", "length", "firstStore", "loadPriceHistory", "store", "_id", "err", "console", "productId", "storeId", "getHistory", "history", "handleStoreSelect", "storeData", "handleSimilarProductClick", "similarProduct", "getCheapestPrice", "reduce", "min", "current", "price", "getPriceRange", "prices", "map", "p", "Math", "max", "message", "fullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onRetry", "cheapestPrice", "priceRange", "className", "children", "onClick", "category", "name", "images", "src", "url", "alt", "slice", "image", "index", "brand", "description", "formatPrice", "specifications", "weight", "volume", "dimensions", "priceData", "_priceData$savings", "savings", "isCheapest", "branch", "original", "amount", "availability", "inStock", "promotion", "isOnPromotion", "stores", "userLocation", "height", "showUserLocation", "entry", "Date", "date", "toLocaleDateString", "changeType", "changePercentage", "toFixed", "showCompareButton", "_c", "$RefreshReg$"], "sources": ["c:/laragon/www/basketcase/client/src/pages/ProductPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { productsAPI, pricesAPI, apiUtils } from '../services/api';\nimport useGeolocation from '../hooks/useGeolocation';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport ProductCard from '../components/ProductCard';\nimport Map from '../components/Map';\nimport './ProductPage.css';\n\nconst ProductPage = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { location } = useGeolocation({ autoStart: true });\n  \n  const [product, setProduct] = useState(null);\n  const [priceComparison, setPriceComparison] = useState([]);\n  const [similarProducts, setSimilarProducts] = useState([]);\n  const [priceHistory, setPriceHistory] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedStore, setSelectedStore] = useState(null);\n  const [activeTab, setActiveTab] = useState('prices');\n\n  // Load product data\n  useEffect(() => {\n    if (id) {\n      loadProductData();\n    }\n  }, [id, location]);\n\n  const loadProductData = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await productsAPI.getById(id, location);\n      setProduct(response.product);\n      setPriceComparison(response.priceComparison || []);\n      setSimilarProducts(response.similarProducts || []);\n      \n      // Load price history for the first store if available\n      if (response.priceComparison && response.priceComparison.length > 0) {\n        const firstStore = response.priceComparison[0];\n        loadPriceHistory(id, firstStore.store._id);\n      }\n      \n    } catch (err) {\n      setError('Failed to load product details. Please try again.');\n      console.error('Error loading product:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadPriceHistory = async (productId, storeId) => {\n    try {\n      const response = await pricesAPI.getHistory(productId, storeId, 30);\n      setPriceHistory(response.history || []);\n    } catch (err) {\n      console.error('Error loading price history:', err);\n    }\n  };\n\n  // Handle store selection for price history\n  const handleStoreSelect = (storeData) => {\n    setSelectedStore(storeData);\n    loadPriceHistory(id, storeData.store._id);\n  };\n\n  // Handle similar product click\n  const handleSimilarProductClick = (similarProduct) => {\n    navigate(`/product/${similarProduct._id}`);\n  };\n\n  // Get cheapest price info\n  const getCheapestPrice = () => {\n    if (priceComparison.length === 0) return null;\n    return priceComparison.reduce((min, current) => \n      current.price.current < min.price.current ? current : min\n    );\n  };\n\n  // Get price range\n  const getPriceRange = () => {\n    if (priceComparison.length === 0) return null;\n    \n    const prices = priceComparison.map(p => p.price.current);\n    const min = Math.min(...prices);\n    const max = Math.max(...prices);\n    \n    return { min, max };\n  };\n\n  if (loading) {\n    return <LoadingSpinner message=\"Loading product details...\" fullScreen />;\n  }\n\n  if (error) {\n    return <ErrorMessage message={error} onRetry={loadProductData} />;\n  }\n\n  if (!product) {\n    return <ErrorMessage message=\"Product not found\" />;\n  }\n\n  const cheapestPrice = getCheapestPrice();\n  const priceRange = getPriceRange();\n\n  return (\n    <div className=\"product-page\">\n      <div className=\"product-container\">\n        \n        {/* Breadcrumb */}\n        <nav className=\"breadcrumb\">\n          <button onClick={() => navigate(-1)} className=\"back-button\">\n            ← Back\n          </button>\n          <span className=\"breadcrumb-separator\">/</span>\n          <span className=\"breadcrumb-category\">{product.category}</span>\n          <span className=\"breadcrumb-separator\">/</span>\n          <span className=\"breadcrumb-current\">{product.name}</span>\n        </nav>\n\n        {/* Product Header */}\n        <div className=\"product-header\">\n          <div className=\"product-images\">\n            {product.images && product.images.length > 0 ? (\n              <div className=\"image-gallery\">\n                <img \n                  src={product.images[0].url} \n                  alt={product.name}\n                  className=\"main-image\"\n                />\n                {product.images.length > 1 && (\n                  <div className=\"thumbnail-images\">\n                    {product.images.slice(1, 4).map((image, index) => (\n                      <img \n                        key={index}\n                        src={image.url} \n                        alt={`${product.name} ${index + 2}`}\n                        className=\"thumbnail-image\"\n                      />\n                    ))}\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"no-image-placeholder\">\n                <div className=\"placeholder-icon\">📦</div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"product-info\">\n            <div className=\"product-title-section\">\n              {product.brand && (\n                <div className=\"product-brand\">{product.brand}</div>\n              )}\n              <h1 className=\"product-title\">{product.name}</h1>\n              <div className=\"product-category-badge\">{product.category}</div>\n            </div>\n\n            {product.description && (\n              <div className=\"product-description\">\n                <p>{product.description}</p>\n              </div>\n            )}\n\n            {/* Price Summary */}\n            {priceRange && (\n              <div className=\"price-summary\">\n                <div className=\"price-range\">\n                  <span className=\"price-label\">Price Range:</span>\n                  <span className=\"price-values\">\n                    {apiUtils.formatPrice(priceRange.min)} - {apiUtils.formatPrice(priceRange.max)}\n                  </span>\n                </div>\n                \n                {cheapestPrice && (\n                  <div className=\"cheapest-price\">\n                    <span className=\"price-label\">Best Price:</span>\n                    <span className=\"price-value\">{apiUtils.formatPrice(cheapestPrice.price.current)}</span>\n                    <span className=\"store-name\">at {cheapestPrice.store.name}</span>\n                  </div>\n                )}\n                \n                <div className=\"store-count\">\n                  Available at {priceComparison.length} store{priceComparison.length !== 1 ? 's' : ''}\n                </div>\n              </div>\n            )}\n\n            {/* Product Specifications */}\n            {product.specifications && (\n              <div className=\"product-specifications\">\n                <h3>Specifications</h3>\n                <div className=\"specs-grid\">\n                  {product.specifications.weight && (\n                    <div className=\"spec-item\">\n                      <span className=\"spec-label\">Weight:</span>\n                      <span className=\"spec-value\">{product.specifications.weight}</span>\n                    </div>\n                  )}\n                  {product.specifications.volume && (\n                    <div className=\"spec-item\">\n                      <span className=\"spec-label\">Volume:</span>\n                      <span className=\"spec-value\">{product.specifications.volume}</span>\n                    </div>\n                  )}\n                  {product.specifications.dimensions && (\n                    <div className=\"spec-item\">\n                      <span className=\"spec-label\">Dimensions:</span>\n                      <span className=\"spec-value\">{product.specifications.dimensions}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"product-tabs\">\n          <div className=\"tab-buttons\">\n            <button \n              className={`tab-button ${activeTab === 'prices' ? 'active' : ''}`}\n              onClick={() => setActiveTab('prices')}\n            >\n              Price Comparison ({priceComparison.length})\n            </button>\n            <button \n              className={`tab-button ${activeTab === 'map' ? 'active' : ''}`}\n              onClick={() => setActiveTab('map')}\n            >\n              Store Locations\n            </button>\n            <button \n              className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}\n              onClick={() => setActiveTab('history')}\n            >\n              Price History\n            </button>\n          </div>\n\n          <div className=\"tab-content\">\n            \n            {/* Price Comparison Tab */}\n            {activeTab === 'prices' && (\n              <div className=\"prices-tab\">\n                {priceComparison.length > 0 ? (\n                  <div className=\"price-comparison-grid\">\n                    {priceComparison.map((priceData, index) => (\n                      <div \n                        key={`${priceData.store._id}-${index}`} \n                        className={`price-card ${priceData.savings?.isCheapest ? 'cheapest' : ''}`}\n                        onClick={() => handleStoreSelect(priceData)}\n                      >\n                        <div className=\"price-card-header\">\n                          <h4 className=\"store-name\">{priceData.store.name}</h4>\n                          <div className=\"store-branch\">{priceData.store.branch}</div>\n                        </div>\n                        \n                        <div className=\"price-info\">\n                          <div className=\"current-price\">\n                            {apiUtils.formatPrice(priceData.price.current)}\n                          </div>\n                          \n                          {priceData.price.original > priceData.price.current && (\n                            <div className=\"original-price\">\n                              Was: {apiUtils.formatPrice(priceData.price.original)}\n                            </div>\n                          )}\n                          \n                          {priceData.savings && priceData.savings.amount > 0 && (\n                            <div className=\"savings-info\">\n                              {priceData.savings.isCheapest ? (\n                                <span className=\"cheapest-badge\">Cheapest!</span>\n                              ) : (\n                                <span className=\"savings-amount\">\n                                  +{apiUtils.formatPrice(priceData.savings.amount)} more\n                                </span>\n                              )}\n                            </div>\n                          )}\n                        </div>\n                        \n                        <div className=\"availability-info\">\n                          <span className={`stock-status ${priceData.availability.inStock ? 'in-stock' : 'out-of-stock'}`}>\n                            {priceData.availability.inStock ? '✅ In Stock' : '❌ Out of Stock'}\n                          </span>\n                        </div>\n                        \n                        {priceData.promotion.isOnPromotion && (\n                          <div className=\"promotion-badge\">\n                            🏷️ On Promotion\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"no-prices\">\n                    <p>No price information available for this product.</p>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* Map Tab */}\n            {activeTab === 'map' && (\n              <div className=\"map-tab\">\n                <Map\n                  stores={priceComparison.map(p => p.store)}\n                  userLocation={location}\n                  height=\"400px\"\n                  showUserLocation={true}\n                />\n              </div>\n            )}\n\n            {/* Price History Tab */}\n            {activeTab === 'history' && (\n              <div className=\"history-tab\">\n                {selectedStore ? (\n                  <div className=\"price-history\">\n                    <h4>Price History - {selectedStore.store.name}</h4>\n                    {priceHistory.length > 0 ? (\n                      <div className=\"history-chart\">\n                        {/* Simple price history list - could be enhanced with a chart library */}\n                        <div className=\"history-list\">\n                          {priceHistory.map((entry, index) => (\n                            <div key={index} className=\"history-entry\">\n                              <div className=\"history-date\">\n                                {new Date(entry.date).toLocaleDateString()}\n                              </div>\n                              <div className=\"history-price\">\n                                {apiUtils.formatPrice(entry.price)}\n                              </div>\n                              <div className={`history-change ${entry.changeType}`}>\n                                {entry.changeType === 'increase' && '↗️'}\n                                {entry.changeType === 'decrease' && '↘️'}\n                                {entry.changeType === 'no_change' && '➡️'}\n                                {entry.changePercentage !== 0 && ` ${entry.changePercentage.toFixed(1)}%`}\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    ) : (\n                      <p>No price history available for this store.</p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"select-store-message\">\n                    <p>Select a store from the Price Comparison tab to view price history.</p>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Similar Products */}\n        {similarProducts.length > 0 && (\n          <div className=\"similar-products\">\n            <h3>Similar Products</h3>\n            <div className=\"similar-products-grid\">\n              {similarProducts.map(similarProduct => (\n                <ProductCard\n                  key={similarProduct._id}\n                  product={similarProduct}\n                  onClick={handleSimilarProductClick}\n                  showCompareButton={false}\n                />\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ProductPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,iBAAiB;AAClE,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAS,CAAC,GAAGX,cAAc,CAAC;IAAEY,SAAS,EAAE;EAAK,CAAC,CAAC;EAExD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,QAAQ,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIe,EAAE,EAAE;MACNoB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACpB,EAAE,EAAEE,QAAQ,CAAC,CAAC;EAElB,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCP,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMjC,WAAW,CAACkC,OAAO,CAACtB,EAAE,EAAEE,QAAQ,CAAC;MACxDG,UAAU,CAACgB,QAAQ,CAACjB,OAAO,CAAC;MAC5BG,kBAAkB,CAACc,QAAQ,CAACf,eAAe,IAAI,EAAE,CAAC;MAClDG,kBAAkB,CAACY,QAAQ,CAACb,eAAe,IAAI,EAAE,CAAC;;MAElD;MACA,IAAIa,QAAQ,CAACf,eAAe,IAAIe,QAAQ,CAACf,eAAe,CAACiB,MAAM,GAAG,CAAC,EAAE;QACnE,MAAMC,UAAU,GAAGH,QAAQ,CAACf,eAAe,CAAC,CAAC,CAAC;QAC9CmB,gBAAgB,CAACzB,EAAE,EAAEwB,UAAU,CAACE,KAAK,CAACC,GAAG,CAAC;MAC5C;IAEF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZb,QAAQ,CAAC,mDAAmD,CAAC;MAC7Dc,OAAO,CAACf,KAAK,CAAC,wBAAwB,EAAEc,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,gBAAgB,GAAG,MAAAA,CAAOK,SAAS,EAAEC,OAAO,KAAK;IACrD,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMhC,SAAS,CAAC2C,UAAU,CAACF,SAAS,EAAEC,OAAO,EAAE,EAAE,CAAC;MACnEpB,eAAe,CAACU,QAAQ,CAACY,OAAO,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACf,KAAK,CAAC,8BAA8B,EAAEc,GAAG,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMM,iBAAiB,GAAIC,SAAS,IAAK;IACvClB,gBAAgB,CAACkB,SAAS,CAAC;IAC3BV,gBAAgB,CAACzB,EAAE,EAAEmC,SAAS,CAACT,KAAK,CAACC,GAAG,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMS,yBAAyB,GAAIC,cAAc,IAAK;IACpDpC,QAAQ,CAAC,YAAYoC,cAAc,CAACV,GAAG,EAAE,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhC,eAAe,CAACiB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC7C,OAAOjB,eAAe,CAACiC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KACzCA,OAAO,CAACC,KAAK,CAACD,OAAO,GAAGD,GAAG,CAACE,KAAK,CAACD,OAAO,GAAGA,OAAO,GAAGD,GACxD,CAAC;EACH,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIrC,eAAe,CAACiB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAE7C,MAAMqB,MAAM,GAAGtC,eAAe,CAACuC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACJ,KAAK,CAACD,OAAO,CAAC;IACxD,MAAMD,GAAG,GAAGO,IAAI,CAACP,GAAG,CAAC,GAAGI,MAAM,CAAC;IAC/B,MAAMI,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,GAAGJ,MAAM,CAAC;IAE/B,OAAO;MAAEJ,GAAG;MAAEQ;IAAI,CAAC;EACrB,CAAC;EAED,IAAIpC,OAAO,EAAE;IACX,oBAAOf,OAAA,CAACL,cAAc;MAACyD,OAAO,EAAC,4BAA4B;MAACC,UAAU;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3E;EAEA,IAAIxC,KAAK,EAAE;IACT,oBAAOjB,OAAA,CAACJ,YAAY;MAACwD,OAAO,EAAEnC,KAAM;MAACyC,OAAO,EAAEnC;IAAgB;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnE;EAEA,IAAI,CAAClD,OAAO,EAAE;IACZ,oBAAOP,OAAA,CAACJ,YAAY;MAACwD,OAAO,EAAC;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEA,MAAME,aAAa,GAAGlB,gBAAgB,CAAC,CAAC;EACxC,MAAMmB,UAAU,GAAGd,aAAa,CAAC,CAAC;EAElC,oBACE9C,OAAA;IAAK6D,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3B9D,OAAA;MAAK6D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAGhC9D,OAAA;QAAK6D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9D,OAAA;UAAQ+D,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,CAAC,CAAC,CAAE;UAACyD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE7D;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzD,OAAA;UAAM6D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CzD,OAAA;UAAM6D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAEvD,OAAO,CAACyD;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/DzD,OAAA;UAAM6D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CzD,OAAA;UAAM6D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEvD,OAAO,CAAC0D;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAGNzD,OAAA;QAAK6D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9D,OAAA;UAAK6D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BvD,OAAO,CAAC2D,MAAM,IAAI3D,OAAO,CAAC2D,MAAM,CAACxC,MAAM,GAAG,CAAC,gBAC1C1B,OAAA;YAAK6D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B9D,OAAA;cACEmE,GAAG,EAAE5D,OAAO,CAAC2D,MAAM,CAAC,CAAC,CAAC,CAACE,GAAI;cAC3BC,GAAG,EAAE9D,OAAO,CAAC0D,IAAK;cAClBJ,SAAS,EAAC;YAAY;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACDlD,OAAO,CAAC2D,MAAM,CAACxC,MAAM,GAAG,CAAC,iBACxB1B,OAAA;cAAK6D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC9BvD,OAAO,CAAC2D,MAAM,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACtB,GAAG,CAAC,CAACuB,KAAK,EAAEC,KAAK,kBAC3CxE,OAAA;gBAEEmE,GAAG,EAAEI,KAAK,CAACH,GAAI;gBACfC,GAAG,EAAE,GAAG9D,OAAO,CAAC0D,IAAI,IAAIO,KAAK,GAAG,CAAC,EAAG;gBACpCX,SAAS,EAAC;cAAiB,GAHtBW,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENzD,OAAA;YAAK6D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC9D,OAAA;cAAK6D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENzD,OAAA;UAAK6D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B9D,OAAA;YAAK6D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACnCvD,OAAO,CAACkE,KAAK,iBACZzE,OAAA;cAAK6D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEvD,OAAO,CAACkE;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACpD,eACDzD,OAAA;cAAI6D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEvD,OAAO,CAAC0D;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjDzD,OAAA;cAAK6D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAEvD,OAAO,CAACyD;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,EAELlD,OAAO,CAACmE,WAAW,iBAClB1E,OAAA;YAAK6D,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClC9D,OAAA;cAAA8D,QAAA,EAAIvD,OAAO,CAACmE;YAAW;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CACN,EAGAG,UAAU,iBACT5D,OAAA;YAAK6D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B9D,OAAA;cAAK6D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9D,OAAA;gBAAM6D,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDzD,OAAA;gBAAM6D,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC3BrE,QAAQ,CAACkF,WAAW,CAACf,UAAU,CAACjB,GAAG,CAAC,EAAC,KAAG,EAAClD,QAAQ,CAACkF,WAAW,CAACf,UAAU,CAACT,GAAG,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELE,aAAa,iBACZ3D,OAAA;cAAK6D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B9D,OAAA;gBAAM6D,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAW;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDzD,OAAA;gBAAM6D,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAErE,QAAQ,CAACkF,WAAW,CAAChB,aAAa,CAACd,KAAK,CAACD,OAAO;cAAC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxFzD,OAAA;gBAAM6D,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,KAAG,EAACH,aAAa,CAAC9B,KAAK,CAACoC,IAAI;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CACN,eAEDzD,OAAA;cAAK6D,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,eACd,EAACrD,eAAe,CAACiB,MAAM,EAAC,QAAM,EAACjB,eAAe,CAACiB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAlD,OAAO,CAACqE,cAAc,iBACrB5E,OAAA;YAAK6D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC9D,OAAA;cAAA8D,QAAA,EAAI;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBzD,OAAA;cAAK6D,SAAS,EAAC,YAAY;cAAAC,QAAA,GACxBvD,OAAO,CAACqE,cAAc,CAACC,MAAM,iBAC5B7E,OAAA;gBAAK6D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9D,OAAA;kBAAM6D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CzD,OAAA;kBAAM6D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEvD,OAAO,CAACqE,cAAc,CAACC;gBAAM;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACN,EACAlD,OAAO,CAACqE,cAAc,CAACE,MAAM,iBAC5B9E,OAAA;gBAAK6D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9D,OAAA;kBAAM6D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CzD,OAAA;kBAAM6D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEvD,OAAO,CAACqE,cAAc,CAACE;gBAAM;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACN,EACAlD,OAAO,CAACqE,cAAc,CAACG,UAAU,iBAChC/E,OAAA;gBAAK6D,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9D,OAAA;kBAAM6D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CzD,OAAA;kBAAM6D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEvD,OAAO,CAACqE,cAAc,CAACG;gBAAU;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAK6D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9D,OAAA;UAAK6D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9D,OAAA;YACE6D,SAAS,EAAE,cAAcxC,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAClE0C,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,QAAQ,CAAE;YAAAwC,QAAA,GACvC,oBACmB,EAACrD,eAAe,CAACiB,MAAM,EAAC,GAC5C;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzD,OAAA;YACE6D,SAAS,EAAE,cAAcxC,SAAS,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC/D0C,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,KAAK,CAAE;YAAAwC,QAAA,EACpC;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzD,OAAA;YACE6D,SAAS,EAAE,cAAcxC,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YACnE0C,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,SAAS,CAAE;YAAAwC,QAAA,EACxC;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzD,OAAA;UAAK6D,SAAS,EAAC,aAAa;UAAAC,QAAA,GAGzBzC,SAAS,KAAK,QAAQ,iBACrBrB,OAAA;YAAK6D,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxBrD,eAAe,CAACiB,MAAM,GAAG,CAAC,gBACzB1B,OAAA;cAAK6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCrD,eAAe,CAACuC,GAAG,CAAC,CAACgC,SAAS,EAAER,KAAK;gBAAA,IAAAS,kBAAA;gBAAA,oBACpCjF,OAAA;kBAEE6D,SAAS,EAAE,cAAc,CAAAoB,kBAAA,GAAAD,SAAS,CAACE,OAAO,cAAAD,kBAAA,eAAjBA,kBAAA,CAAmBE,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;kBAC3EpB,OAAO,EAAEA,CAAA,KAAM1B,iBAAiB,CAAC2C,SAAS,CAAE;kBAAAlB,QAAA,gBAE5C9D,OAAA;oBAAK6D,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC9D,OAAA;sBAAI6D,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEkB,SAAS,CAACnD,KAAK,CAACoC;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtDzD,OAAA;sBAAK6D,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAEkB,SAAS,CAACnD,KAAK,CAACuD;oBAAM;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eAENzD,OAAA;oBAAK6D,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB9D,OAAA;sBAAK6D,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAC3BrE,QAAQ,CAACkF,WAAW,CAACK,SAAS,CAACnC,KAAK,CAACD,OAAO;oBAAC;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,EAELuB,SAAS,CAACnC,KAAK,CAACwC,QAAQ,GAAGL,SAAS,CAACnC,KAAK,CAACD,OAAO,iBACjD5C,OAAA;sBAAK6D,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAAC,OACzB,EAACrE,QAAQ,CAACkF,WAAW,CAACK,SAAS,CAACnC,KAAK,CAACwC,QAAQ,CAAC;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CACN,EAEAuB,SAAS,CAACE,OAAO,IAAIF,SAAS,CAACE,OAAO,CAACI,MAAM,GAAG,CAAC,iBAChDtF,OAAA;sBAAK6D,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAC1BkB,SAAS,CAACE,OAAO,CAACC,UAAU,gBAC3BnF,OAAA;wBAAM6D,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,gBAEjDzD,OAAA;wBAAM6D,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,GAAC,GAC9B,EAACrE,QAAQ,CAACkF,WAAW,CAACK,SAAS,CAACE,OAAO,CAACI,MAAM,CAAC,EAAC,OACnD;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBACP;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAENzD,OAAA;oBAAK6D,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,eAChC9D,OAAA;sBAAM6D,SAAS,EAAE,gBAAgBmB,SAAS,CAACO,YAAY,CAACC,OAAO,GAAG,UAAU,GAAG,cAAc,EAAG;sBAAA1B,QAAA,EAC7FkB,SAAS,CAACO,YAAY,CAACC,OAAO,GAAG,YAAY,GAAG;oBAAgB;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAELuB,SAAS,CAACS,SAAS,CAACC,aAAa,iBAChC1F,OAAA;oBAAK6D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAEjC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN;gBAAA,GA3CI,GAAGuB,SAAS,CAACnD,KAAK,CAACC,GAAG,IAAI0C,KAAK,EAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4CnC,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENzD,OAAA;cAAK6D,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxB9D,OAAA;gBAAA8D,QAAA,EAAG;cAAgD;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGApC,SAAS,KAAK,KAAK,iBAClBrB,OAAA;YAAK6D,SAAS,EAAC,SAAS;YAAAC,QAAA,eACtB9D,OAAA,CAACF,GAAG;cACF6F,MAAM,EAAElF,eAAe,CAACuC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACpB,KAAK,CAAE;cAC1C+D,YAAY,EAAEvF,QAAS;cACvBwF,MAAM,EAAC,OAAO;cACdC,gBAAgB,EAAE;YAAK;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGApC,SAAS,KAAK,SAAS,iBACtBrB,OAAA;YAAK6D,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzB3C,aAAa,gBACZnB,OAAA;cAAK6D,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B9D,OAAA;gBAAA8D,QAAA,GAAI,kBAAgB,EAAC3C,aAAa,CAACU,KAAK,CAACoC,IAAI;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAClD5C,YAAY,CAACa,MAAM,GAAG,CAAC,gBACtB1B,OAAA;gBAAK6D,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAE5B9D,OAAA;kBAAK6D,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAC1BjD,YAAY,CAACmC,GAAG,CAAC,CAAC+C,KAAK,EAAEvB,KAAK,kBAC7BxE,OAAA;oBAAiB6D,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBACxC9D,OAAA;sBAAK6D,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAC1B,IAAIkC,IAAI,CAACD,KAAK,CAACE,IAAI,CAAC,CAACC,kBAAkB,CAAC;oBAAC;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNzD,OAAA;sBAAK6D,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAC3BrE,QAAQ,CAACkF,WAAW,CAACoB,KAAK,CAAClD,KAAK;oBAAC;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACNzD,OAAA;sBAAK6D,SAAS,EAAE,kBAAkBkC,KAAK,CAACI,UAAU,EAAG;sBAAArC,QAAA,GAClDiC,KAAK,CAACI,UAAU,KAAK,UAAU,IAAI,IAAI,EACvCJ,KAAK,CAACI,UAAU,KAAK,UAAU,IAAI,IAAI,EACvCJ,KAAK,CAACI,UAAU,KAAK,WAAW,IAAI,IAAI,EACxCJ,KAAK,CAACK,gBAAgB,KAAK,CAAC,IAAI,IAAIL,KAAK,CAACK,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;oBAAA;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA,GAZEe,KAAK;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAaV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENzD,OAAA;gBAAA8D,QAAA,EAAG;cAA0C;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAENzD,OAAA;cAAK6D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACnC9D,OAAA;gBAAA8D,QAAA,EAAG;cAAmE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL9C,eAAe,CAACe,MAAM,GAAG,CAAC,iBACzB1B,OAAA;QAAK6D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9D,OAAA;UAAA8D,QAAA,EAAI;QAAgB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBzD,OAAA;UAAK6D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACnCnD,eAAe,CAACqC,GAAG,CAACR,cAAc,iBACjCxC,OAAA,CAACH,WAAW;YAEVU,OAAO,EAAEiC,cAAe;YACxBuB,OAAO,EAAExB,yBAA0B;YACnC+D,iBAAiB,EAAE;UAAM,GAHpB9D,cAAc,CAACV,GAAG;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIxB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CApXID,WAAW;EAAA,QACAZ,SAAS,EACPC,WAAW,EACPI,cAAc;AAAA;AAAA6G,EAAA,GAH/BtG,WAAW;AAsXjB,eAAeA,WAAW;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}