/* Map Component Styles */
.map-container {
  position: relative;
  width: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.leaflet-map {
  width: 100%;
  z-index: 1;
}

/* Custom Marker Styles */
.custom-marker {
  background: white;
  border: 2px solid;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.spar-marker {
  border-color: #00a651;
  color: #00a651;
}

.checkers-marker {
  border-color: #e31e24;
  color: #e31e24;
}

.picknpay-marker {
  border-color: #0066cc;
  color: #0066cc;
}

.woolworths-marker {
  border-color: #8b4513;
  color: #8b4513;
}

.marker-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* User Location Marker */
.user-location-marker {
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-marker-content {
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* Custom Popup Styles */
.custom-popup .leaflet-popup-content-wrapper {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

.store-popup {
  min-width: 250px;
  font-family: inherit;
}

.store-popup-header {
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.store-name {
  margin: 0 0 0.25rem 0;
  color: var(--primary-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.store-branch {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
}

.store-popup-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.store-address {
  font-size: 0.85rem;
  line-height: 1.4;
}

.store-contact {
  font-size: 0.85rem;
  color: var(--primary-color);
}

.store-distance {
  font-size: 0.85rem;
  color: var(--accent-color);
  font-weight: 500;
}

.store-hours {
  font-size: 0.8rem;
  line-height: 1.3;
}

.store-popup-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.popup-button {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.view-store-button {
  background-color: var(--primary-color);
  color: white;
}

.view-store-button:hover {
  background-color: #1e3d72;
}

.directions-button {
  background-color: var(--accent-color);
  color: white;
}

.directions-button:hover {
  background-color: #218838;
}

/* User Location Popup */
.user-location-popup {
  text-align: center;
}

.user-location-popup h4 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
}

.user-location-popup p {
  margin: 0.25rem 0;
  font-size: 0.85rem;
  color: #6c757d;
}

/* Map Legend */
.map-legend {
  position: absolute;
  top: 10px;
  right: 10px;
  background: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  z-index: 1000;
  min-width: 150px;
}

.map-legend h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: var(--dark-color);
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.legend-marker {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 10px;
  border: 2px solid;
  background: white;
  flex-shrink: 0;
}

.legend-marker.spar-marker {
  border-color: #00a651;
  color: #00a651;
}

.legend-marker.checkers-marker {
  border-color: #e31e24;
  color: #e31e24;
}

.legend-marker.picknpay-marker {
  border-color: #0066cc;
  color: #0066cc;
}

.legend-marker.woolworths-marker {
  border-color: #8b4513;
  color: #8b4513;
}

.legend-marker.user-marker {
  border: none;
  background: transparent;
  font-size: 16px;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .map-legend {
    top: 5px;
    right: 5px;
    padding: 0.75rem;
    min-width: 120px;
  }
  
  .map-legend h4 {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }
  
  .legend-item {
    font-size: 0.75rem;
    gap: 0.4rem;
  }
  
  .legend-marker {
    width: 16px;
    height: 16px;
    font-size: 8px;
  }
  
  .store-popup {
    min-width: 200px;
  }
  
  .store-popup-actions {
    flex-direction: column;
  }
}

/* Override Leaflet default styles */
.leaflet-popup-content {
  margin: 8px 12px !important;
}

.leaflet-popup-tip-container {
  margin: 0 auto;
}
