"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.enableCors({
        origin: process.env.CLIENT_URL || 'http://localhost:3000',
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    app.setGlobalPrefix('api');
    const config = new swagger_1.DocumentBuilder()
        .setTitle('BasketCase API')
        .setDescription('Price comparison platform API')
        .setVersion('1.0')
        .addTag('products', 'Product management')
        .addTag('stores', 'Store management')
        .addTag('prices', 'Price tracking and comparison')
        .addTag('scraping', 'Web scraping operations')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document);
    const port = process.env.PORT || 5000;
    await app.listen(port);
    console.log('🚀 BasketCase API started successfully!');
    console.log(`📖 API Documentation: http://localhost:${port}/api/docs`);
    console.log(`🌐 API Base URL: http://localhost:${port}/api`);
    console.log(`🔗 Frontend URL: ${process.env.CLIENT_URL || 'http://localhost:3000'}`);
}
bootstrap();
//# sourceMappingURL=main.js.map