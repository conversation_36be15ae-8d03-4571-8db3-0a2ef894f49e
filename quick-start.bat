@echo off
echo.
echo ==========================================
echo    BasketCase - Quick Start
echo ==========================================
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ERROR: Please run this from the basketcase directory
    pause
    exit /b 1
)

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js is installed ✓

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing root dependencies...
    npm install
)

if not exist "server\node_modules" (
    echo Installing server dependencies...
    cd server
    npm install
    cd ..
)

if not exist "client\node_modules" (
    echo Installing client dependencies...
    cd client
    npm install
    cd ..
)

REM Create environment files if they don't exist
if not exist "server\.env" (
    echo Creating server environment file...
    echo PORT=5000> server\.env
    echo NODE_ENV=development>> server\.env
    echo CLIENT_URL=http://localhost:3000>> server\.env
    echo MONGODB_URI=mongodb://localhost:27017/basketcase>> server\.env
    echo JWT_SECRET=basketcase_dev_secret>> server\.env
)

if not exist "client\.env" (
    echo Creating client environment file...
    echo REACT_APP_API_URL=http://localhost:5000/api> client\.env
    echo REACT_APP_NAME=BasketCase>> client\.env
)

REM Create sample stores
echo Setting up sample data...
node scripts\create-sample-stores.js >nul 2>&1

echo.
echo ✓ Setup complete!
echo.
echo Starting BasketCase...
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:5000
echo.
echo Press Ctrl+C to stop
echo.

REM Start the application
npm run dev

echo.
echo Application stopped.
pause
