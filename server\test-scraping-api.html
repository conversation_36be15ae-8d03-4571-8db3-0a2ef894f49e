<!DOCTYPE html>
<html>
<head>
    <title>Scraping API Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <h1>🚀 BasketCase Scraping API Test</h1>
    
    <button onclick="checkStatus()">Check Scraping Status</button>
    <button onclick="runScraping()">Run Full Scraping</button>
    <button onclick="scrapeSpar()">Scrape SPAR Only</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        function showResult(title, data, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.insertBefore(div, results.firstChild);
        }
        
        async function checkStatus() {
            try {
                const response = await fetch(`${API_BASE}/scraping/status`);
                const data = await response.json();
                showResult('✅ Scraping Status', data);
            } catch (error) {
                showResult('❌ Status Check Failed', { error: error.message }, false);
            }
        }
        
        async function runScraping() {
            try {
                showResult('🚀 Starting Full Scraping...', { message: 'Please wait...' });
                
                const response = await fetch(`${API_BASE}/scraping/run`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                showResult('✅ Full Scraping Complete', data);
            } catch (error) {
                showResult('❌ Scraping Failed', { error: error.message }, false);
            }
        }
        
        async function scrapeSpar() {
            try {
                showResult('🔍 Starting SPAR Scraping...', { message: 'Please wait...' });
                
                const response = await fetch(`${API_BASE}/scraping/store/spar`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ limit: 10 })
                });
                const data = await response.json();
                showResult('✅ SPAR Scraping Complete', data);
            } catch (error) {
                showResult('❌ SPAR Scraping Failed', { error: error.message }, false);
            }
        }
        
        // Auto-check status on load
        window.onload = () => checkStatus();
    </script>
</body>
</html>
