"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceSchema = exports.Price = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const swagger_1 = require("@nestjs/swagger");
let Price = class Price {
};
exports.Price = Price;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Product reference' }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Product', required: true, index: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Price.prototype, "product", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store reference' }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Store', required: true, index: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Price.prototype, "store", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current price' }),
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", Number)
], Price.prototype, "current", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Original price (before discount)' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Price.prototype, "original", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency code' }),
    (0, mongoose_1.Prop)({ default: 'ZAR' }),
    __metadata("design:type", String)
], Price.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Product availability' }),
    (0, mongoose_1.Prop)({
        inStock: { type: Boolean, default: true },
        stockLevel: String,
        lastChecked: Date,
    }),
    __metadata("design:type", Object)
], Price.prototype, "availability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Promotion information' }),
    (0, mongoose_1.Prop)({
        isOnPromotion: { type: Boolean, default: false },
        promotionDescription: String,
        promotionType: String,
        validFrom: Date,
        validUntil: Date,
    }),
    __metadata("design:type", Object)
], Price.prototype, "promotion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Price history' }),
    (0, mongoose_1.Prop)([{
            price: { type: Number, required: true },
            date: { type: Date, required: true },
            source: String,
        }]),
    __metadata("design:type", Array)
], Price.prototype, "priceHistory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Scraping information' }),
    (0, mongoose_1.Prop)({
        lastScraped: Date,
        sourceUrl: String,
        scrapingMethod: String,
        confidence: Number,
    }),
    __metadata("design:type", Object)
], Price.prototype, "scrapingInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether price is active' }),
    (0, mongoose_1.Prop)({ default: true, index: true }),
    __metadata("design:type", Boolean)
], Price.prototype, "isActive", void 0);
exports.Price = Price = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Price);
exports.PriceSchema = mongoose_1.SchemaFactory.createForClass(Price);
//# sourceMappingURL=price.schema.js.map