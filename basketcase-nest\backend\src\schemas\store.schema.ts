import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type StoreDocument = Store & Document;

@Schema({ timestamps: true })
export class Store {
  @ApiProperty({ description: 'Store name' })
  @Prop({ required: true, index: true })
  name: string;

  @ApiProperty({ description: 'Store branch name' })
  @Prop({ required: true })
  branch: string;

  @ApiProperty({ description: 'Store location coordinates' })
  @Prop({
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point',
    },
    coordinates: {
      type: [Number],
      index: '2dsphere',
    },
  })
  location?: {
    type: string;
    coordinates: [number, number]; // [longitude, latitude]
  };

  @ApiProperty({ description: 'Store address' })
  @Prop({
    street: { type: String, required: true },
    city: { type: String, required: true, index: true },
    province: { type: String, required: true, index: true },
    postalCode: String,
    country: { type: String, default: 'South Africa' },
  })
  address: {
    street: string;
    city: string;
    province: string;
    postalCode?: string;
    country: string;
  };

  @ApiProperty({ description: 'Store contact information' })
  @Prop({
    phone: String,
    email: String,
    website: String,
  })
  contact?: {
    phone?: string;
    email?: string;
    website?: string;
  };

  @ApiProperty({ description: 'Store operating hours' })
  @Prop({
    monday: { open: String, close: String },
    tuesday: { open: String, close: String },
    wednesday: { open: String, close: String },
    thursday: { open: String, close: String },
    friday: { open: String, close: String },
    saturday: { open: String, close: String },
    sunday: { open: String, close: String },
  })
  operatingHours?: {
    monday?: { open: string; close: string };
    tuesday?: { open: string; close: string };
    wednesday?: { open: string; close: string };
    thursday?: { open: string; close: string };
    friday?: { open: string; close: string };
    saturday?: { open: string; close: string };
    sunday?: { open: string; close: string };
  };

  @ApiProperty({ description: 'Whether store is active' })
  @Prop({ default: true, index: true })
  isActive: boolean;
}

export const StoreSchema = SchemaFactory.createForClass(Store);
