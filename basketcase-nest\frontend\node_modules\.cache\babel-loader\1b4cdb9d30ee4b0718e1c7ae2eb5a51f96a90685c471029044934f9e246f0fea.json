{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\basketcase-nest\\\\frontend\\\\src\\\\pages\\\\ComparePage.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComparePage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"compare-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"page-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-balance-scale me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 10,\n              columnNumber: 15\n            }, this), \"Compare Prices\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: \"Compare product prices across different stores\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-info-circle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), \"Price comparison tool is under development. This will allow you to compare specific products across multiple stores.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"card-title\",\n                children: \"Coming Soon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"card-text\",\n                children: \"This page will feature:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Side-by-side price comparisons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Store location mapping\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Price history charts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Savings calculator\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Best deal recommendations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = ComparePage;\nexport default ComparePage;\nvar _c;\n$RefreshReg$(_c, \"ComparePage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ComparePage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/basketcase-nest/frontend/src/pages/ComparePage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst ComparePage: React.FC = () => {\n  return (\n    <div className=\"compare-page\">\n      <div className=\"container py-4\">\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <h1 className=\"page-title\">\n              <i className=\"fas fa-balance-scale me-2\"></i>\n              Compare Prices\n            </h1>\n            <p className=\"text-muted\">Compare product prices across different stores</p>\n          </div>\n        </div>\n\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"alert alert-info\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              Price comparison tool is under development. This will allow you to compare specific products across multiple stores.\n            </div>\n            \n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h5 className=\"card-title\">Coming Soon</h5>\n                <p className=\"card-text\">\n                  This page will feature:\n                </p>\n                <ul>\n                  <li>Side-by-side price comparisons</li>\n                  <li>Store location mapping</li>\n                  <li>Price history charts</li>\n                  <li>Savings calculator</li>\n                  <li>Best deal recommendations</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ComparePage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,oBACED,OAAA;IAAKE,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BH,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBH,OAAA;UAAKE,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBH,OAAA;YAAIE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACxBH,OAAA;cAAGE,SAAS,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,kBAE/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLP,OAAA;YAAGE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,KAAK;QAAAC,QAAA,eAClBH,OAAA;UAAKE,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBH,OAAA;YAAKE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BH,OAAA;cAAGE,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,wHAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBH,OAAA;cAAKE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBH,OAAA;gBAAIE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CP,OAAA;gBAAGE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAEzB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJP,OAAA;gBAAAG,QAAA,gBACEH,OAAA;kBAAAG,QAAA,EAAI;gBAA8B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvCP,OAAA;kBAAAG,QAAA,EAAI;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/BP,OAAA;kBAAAG,QAAA,EAAI;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BP,OAAA;kBAAAG,QAAA,EAAI;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3BP,OAAA;kBAAAG,QAAA,EAAI;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAzCIP,WAAqB;AA2C3B,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}