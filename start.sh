#!/bin/bash

echo ""
echo "=========================================="
echo "   BasketCase - Grocery Price Comparison"
echo "=========================================="
echo "   One-Click Startup Script"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Make sure the script is executable
chmod +x "$0"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Ask user for startup preference
echo "Choose your startup method:"
echo "1. Docker (Recommended - Includes MongoDB)"
echo "2. Local Development (Requires local MongoDB)"
echo "3. Docker with MongoDB only (Hybrid)"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1) startup_mode="docker_full" ;;
    2) startup_mode="local_dev" ;;
    3) startup_mode="docker_mongo" ;;
    *) echo "Invalid choice. Defaulting to Docker setup..."; startup_mode="docker_full" ;;
esac

if [ "$startup_mode" = "docker_full" ]; then
    echo ""
    echo -e "${BLUE}Setting up with Docker (Full Stack)...${NC}"
    echo ""

    # Check if Docker is installed
    if ! command_exists docker; then
        echo -e "${RED}ERROR: Docker is not installed${NC}"
        echo "Please install Docker from https://www.docker.com/get-started"
        exit 1
    fi

    if ! command_exists docker-compose; then
        echo -e "${RED}ERROR: Docker Compose is not installed${NC}"
        echo "Please install Docker Compose"
        exit 1
    fi

    echo -e "${GREEN}Docker is installed ✓${NC}"

    # Stop any existing containers
    echo "Stopping any existing containers..."
    docker-compose -f docker-compose.dev.yml down >/dev/null 2>&1

    # Build and start containers
    echo "Building and starting Docker containers..."
    echo "This may take a few minutes on first run..."

    if ! docker-compose -f docker-compose.dev.yml up --build -d; then
        echo -e "${RED}ERROR: Failed to start Docker containers${NC}"
        echo "Trying to start without build..."
        docker-compose -f docker-compose.dev.yml up -d
    fi

    # Wait for services to be ready
    echo "Waiting for services to start..."
    sleep 10

    # Check if services are running
    docker-compose -f docker-compose.dev.yml ps

    echo ""
    echo -e "${GREEN}✓ Docker containers are running!${NC}"
    echo ""
    echo "Services available at:"
    echo "- Frontend: http://localhost:3000"
    echo "- Backend API: http://localhost:5000"
    echo "- MongoDB: localhost:27017"
    echo ""

    echo -e "${YELLOW}Setting up initial data...${NC}"
    sleep 5

    # Create sample stores (run inside container)
    docker-compose -f docker-compose.dev.yml exec -T server node scripts/create-sample-stores.js 2>/dev/null || true

    # Run initial scraping (optional)
    read -p "Run initial product scraping? (y/N): " scrape
    if [[ $scrape =~ ^[Yy]$ ]]; then
        echo "Running initial scraping..."
        docker-compose -f docker-compose.dev.yml exec -T server npm run scrape &
    fi

    echo ""
    echo -e "${GREEN}🎉 BasketCase is ready!${NC}"
    echo ""
    echo "Open your browser and visit: http://localhost:3000"
    echo ""
    echo "To stop the application, run: docker-compose -f docker-compose.dev.yml down"
    echo "To view logs, run: docker-compose -f docker-compose.dev.yml logs -f"
    echo ""

    # Try to open browser (works on macOS and some Linux distros)
    if command_exists open; then
        open http://localhost:3000
    elif command_exists xdg-open; then
        xdg-open http://localhost:3000
    fi

    echo "Press any key to exit..."
    read -n 1
    exit 0

elif [ "$startup_mode" = "docker_mongo" ]; then
    echo ""
    echo -e "${BLUE}Setting up with Docker MongoDB + Local Development...${NC}"
    echo ""

    # Check Docker
    if ! command_exists docker; then
        echo -e "${RED}ERROR: Docker is not installed${NC}"
        exit 1
    fi

    # Start only MongoDB in Docker
    echo "Starting MongoDB in Docker..."
    if ! docker run -d --name basketcase-mongo -p 27017:27017 \
        -e MONGO_INITDB_ROOT_USERNAME=admin \
        -e MONGO_INITDB_ROOT_PASSWORD=password123 \
        mongo:6.0 2>/dev/null; then
        echo "MongoDB container might already exist, trying to start it..."
        docker start basketcase-mongo
    fi

    echo -e "${GREEN}MongoDB is running in Docker ✓${NC}"
    sleep 3

    startup_mode="local_setup"
fi

if [ "$startup_mode" = "local_dev" ]; then
    echo ""
    echo -e "${BLUE}Setting up Local Development Environment...${NC}"
    echo ""

    # Check if MongoDB is running locally
    echo "Checking for local MongoDB..."
    if ! port_in_use 27017; then
        echo -e "${YELLOW}Warning: MongoDB doesn't appear to be running on port 27017${NC}"
        echo "Please make sure MongoDB is installed and running, or choose Docker option."
        read -p "Continue anyway? (y/N): " continue
        if [[ ! $continue =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi

    startup_mode="local_setup"
fi

if [ "$startup_mode" = "local_setup" ]; then
    # Check Node.js
    if ! command_exists node; then
        echo -e "${RED}ERROR: Node.js is not installed${NC}"
        echo "Please install Node.js from https://nodejs.org/"
        exit 1
    fi

    if ! command_exists npm; then
        echo -e "${RED}ERROR: npm is not installed${NC}"
        exit 1
    fi

    echo -e "${GREEN}Node.js and npm are installed ✓${NC}"

    # Install dependencies
    echo ""
    echo -e "${YELLOW}Installing dependencies...${NC}"

    if [ ! -d "node_modules" ]; then
        echo "Installing root dependencies..."
        if ! npm install; then
            echo -e "${RED}ERROR: Failed to install root dependencies${NC}"
            exit 1
        fi
    fi

    if [ ! -d "server/node_modules" ]; then
        echo "Installing server dependencies..."
        cd server
        if ! npm install; then
            echo -e "${RED}ERROR: Failed to install server dependencies${NC}"
            exit 1
        fi
        cd ..
    fi

    if [ ! -d "client/node_modules" ]; then
        echo "Installing client dependencies..."
        cd client
        if ! npm install; then
            echo -e "${RED}ERROR: Failed to install client dependencies${NC}"
            exit 1
        fi
        cd ..
    fi

    echo -e "${GREEN}✓ Dependencies installed${NC}"

    # Setup environment
    if [ ! -f "server/.env" ]; then
        echo -e "${YELLOW}Setting up environment configuration...${NC}"
        node scripts/setup-env.js
    fi

    # Create sample data
    echo -e "${YELLOW}Setting up sample data...${NC}"
    sleep 2
    node scripts/create-sample-stores.js 2>/dev/null || true

    # Ask about initial scraping
    read -p "Run initial product scraping? This may take several minutes (y/N): " scrape

    echo ""
    echo -e "${GREEN}Starting BasketCase application...${NC}"
    echo ""
    echo "Services will be available at:"
    echo "- Frontend: http://localhost:3000"
    echo "- Backend API: http://localhost:5000"
    echo "- MongoDB: localhost:27017"
    echo ""
    echo -e "${YELLOW}Press Ctrl+C to stop the application${NC}"
    echo ""

    # Start the application in background
    npm run dev &
    APP_PID=$!

    # Wait a bit for services to start
    sleep 8

    # Run scraping if requested
    if [[ $scrape =~ ^[Yy]$ ]]; then
        echo ""
        echo -e "${YELLOW}Running initial scraping in background...${NC}"
        npm run scrape &
    fi

    # Try to open browser
    if command_exists open; then
        open http://localhost:3000
    elif command_exists xdg-open; then
        xdg-open http://localhost:3000
    fi

    echo ""
    echo -e "${GREEN}🎉 BasketCase is running!${NC}"
    echo ""
    echo "The application is now running in the background."
    echo "Check your browser at http://localhost:3000"
    echo ""
    echo "Press any key to stop the application..."
    read -n 1

    # Kill the application
    kill $APP_PID 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true

    echo ""
    echo "Application stopped."
fi
