#!/bin/bash

echo ""
echo "=========================================="
echo "   BasketCase - Grocery Price Comparison"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}ERROR: Node.js is not installed${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}ERROR: npm is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}Node.js and npm are installed ✓${NC}"
echo ""

# Check if MongoDB is running
echo "Checking MongoDB connection..."
sleep 2

# Install dependencies if not present
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing root dependencies...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: Failed to install root dependencies${NC}"
        exit 1
    fi
fi

if [ ! -d "server/node_modules" ]; then
    echo -e "${YELLOW}Installing server dependencies...${NC}"
    cd server
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: Failed to install server dependencies${NC}"
        exit 1
    fi
    cd ..
fi

if [ ! -d "client/node_modules" ]; then
    echo -e "${YELLOW}Installing client dependencies...${NC}"
    cd client
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: Failed to install client dependencies${NC}"
        exit 1
    fi
    cd ..
fi

# Check if environment files exist
if [ ! -f "server/.env" ]; then
    echo -e "${YELLOW}Setting up environment configuration...${NC}"
    node scripts/setup-env.js
fi

echo ""
echo -e "${GREEN}Starting BasketCase application...${NC}"
echo ""
echo "Server will start on: http://localhost:5000"
echo "Client will start on: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop the application"
echo ""

# Make sure the script is executable
chmod +x "$0"

# Start both server and client
npm run dev
