/* Product Grid Styles */
.product-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 1rem 0;
}

.product-grid .product-card {
  flex: 0 0 calc(20% - 1.2rem);
  min-width: 280px;
}

.empty-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 2rem;
}

.empty-message {
  text-align: center;
  color: #6c757d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-message h3 {
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.empty-message p {
  margin: 0;
  font-size: 0.9rem;
}

/* Skeleton Loading */
.product-card.skeleton {
  background: white;
  border-radius: var(--border-radius);
  padding: 1rem;
  box-shadow: var(--shadow);
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-image {
  width: 100%;
  height: 200px;
  background: #e9ecef;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-line {
  height: 1rem;
  background: #e9ecef;
  border-radius: 4px;
}

.skeleton-title {
  width: 80%;
}

.skeleton-brand {
  width: 60%;
}

.skeleton-price {
  width: 40%;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-grid .product-card {
    flex: 0 0 calc(50% - 0.75rem);
    min-width: 250px;
  }
}

@media (max-width: 480px) {
  .product-grid {
    flex-direction: column;
  }

  .product-grid .product-card {
    flex: 1 1 100%;
    min-width: unset;
  }
}
