const BaseScraper = require('./BaseScraper');

class CheckersScraper extends BaseScraper {
  constructor(config = {}) {
    super('Checkers', config);
    this.baseUrl = 'https://www.checkers.co.za';
    this.categories = [
      { name: 'Dairy & Eggs', url: '/c-2256/All-Departments/Food/Dairy-Eggs-Milk' },
      { name: 'Meat & Poultry', url: '/c-2257/All-Departments/Food/Meat-Seafood' },
      { name: 'Fruits & Vegetables', url: '/c-2258/All-Departments/Food/Fruit-Vegetables' },
      { name: '<PERSON><PERSON>', url: '/c-2259/All-Departments/Food/Bakery' },
      { name: 'Pantry Staples', url: '/c-2260/All-Departments/Food/Pantry' },
      { name: 'Snacks & Confectionery', url: '/c-2261/All-Departments/Food/Snacks-Confectionery' },
      { name: 'Beverages', url: '/c-2262/All-Departments/Food/Beverages' },
      { name: 'Frozen Foods', url: '/c-2263/All-Departments/Food/Frozen' }
    ];
  }

  async scrapeProducts() {
    const store = await this.getStore();
    
    for (const category of this.categories) {
      try {
        console.log(`Scraping ${category.name} category...`);
        await this.scrapeCategoryProducts(category, store);
        await this.delay(2000);
      } catch (error) {
        console.error(`Error scraping category ${category.name}:`, error);
        this.errors.push({
          category: category.name,
          error: error.message
        });
      }
    }
  }

  async scrapeCategoryProducts(category, store) {
    const categoryUrl = `${this.baseUrl}${category.url}`;
    await this.navigateToPage(categoryUrl);

    let currentPage = 1;
    let hasNextPage = true;

    while (hasNextPage && currentPage <= 10) {
      try {
        console.log(`Scraping page ${currentPage} of ${category.name}...`);
        
        // Wait for products to load
        await this.page.waitForSelector('[data-testid="product-tile"], .product-tile, .product-item', { timeout: 10000 });
        
        const productLinks = await this.page.evaluate(() => {
          const links = [];
          const productElements = document.querySelectorAll('[data-testid="product-tile"] a, .product-tile a, .product-item a');
          
          productElements.forEach(element => {
            const href = element.getAttribute('href');
            if (href && (href.includes('/p/') || href.includes('/product/'))) {
              links.push(href);
            }
          });
          
          return [...new Set(links)];
        });

        console.log(`Found ${productLinks.length} products on page ${currentPage}`);

        for (const productLink of productLinks.slice(0, 20)) {
          try {
            const fullUrl = productLink.startsWith('http') ? productLink : `${this.baseUrl}${productLink}`;
            const productData = await this.scrapeProductDetails(fullUrl);
            
            if (productData) {
              productData.category = category.name;
              const product = await this.findOrCreateProduct(productData);
              await this.updatePrice(product, store, productData.price);
            }
            
            await this.delay(500);
          } catch (error) {
            console.error(`Error scraping product ${productLink}:`, error);
            this.errors.push({
              productUrl: productLink,
              error: error.message
            });
          }
        }

        // Check for next page
        hasNextPage = await this.page.evaluate(() => {
          const nextButton = document.querySelector('[data-testid="pagination-next"], .pagination-next, .next-page');
          return nextButton && !nextButton.disabled && !nextButton.classList.contains('disabled');
        });

        if (hasNextPage) {
          await this.page.click('[data-testid="pagination-next"], .pagination-next, .next-page');
          await this.page.waitForLoadState('networkidle');
          currentPage++;
        }

      } catch (error) {
        console.error(`Error on page ${currentPage} of ${category.name}:`, error);
        hasNextPage = false;
      }
    }
  }

  async scrapeProductDetails(productUrl) {
    return await this.retryOperation(async () => {
      await this.navigateToPage(productUrl);
      
      // Wait for product details to load
      await this.page.waitForSelector('[data-testid="pdp-product-title"], .product-title, h1', { timeout: 10000 });

      const productData = await this.page.evaluate(() => {
        const getText = (selector) => {
          const element = document.querySelector(selector);
          return element ? element.textContent.trim() : '';
        };

        const getAttribute = (selector, attribute) => {
          const element = document.querySelector(selector);
          return element ? element.getAttribute(attribute) : '';
        };

        // Extract product name
        const name = getText('[data-testid="pdp-product-title"], .product-title, h1') ||
                    getText('.pdp-product-name, .product-name');

        // Extract brand
        const brand = getText('[data-testid="pdp-brand"], .brand, .product-brand') ||
                     getText('.manufacturer, .brand-name');

        // Extract price information
        const priceText = getText('[data-testid="pdp-price"], .price, .current-price') ||
                         getText('.product-price, .price-current');
        
        const originalPriceText = getText('[data-testid="pdp-original-price"], .original-price, .was-price') ||
                                 getText('.price-was, .strikethrough');

        // Parse prices
        const parsePrice = (text) => {
          if (!text) return null;
          const match = text.match(/R?\s*(\d+(?:\.\d{2})?)/);
          return match ? parseFloat(match[1]) : null;
        };

        const currentPrice = parsePrice(priceText);
        const originalPrice = parsePrice(originalPriceText);

        // Extract promotion information
        const promotionElement = document.querySelector('[data-testid="pdp-promotion"], .promotion, .special-offer');
        const isOnPromotion = !!promotionElement || (originalPrice && originalPrice > currentPrice);
        const promotionDescription = promotionElement ? promotionElement.textContent.trim() : '';

        // Extract stock information
        const stockElement = document.querySelector('[data-testid="pdp-stock"], .stock-status, .availability');
        const stockText = stockElement ? stockElement.textContent.toLowerCase() : '';
        const inStock = !stockText.includes('out of stock') && !stockText.includes('unavailable') && !stockText.includes('sold out');

        // Extract images
        const images = [];
        const imageElements = document.querySelectorAll('[data-testid="pdp-image"] img, .product-image img, .product-gallery img');
        imageElements.forEach((img, index) => {
          const src = img.getAttribute('src') || img.getAttribute('data-src');
          if (src && !src.includes('placeholder')) {
            images.push({
              url: src.startsWith('http') ? src : `https://www.checkers.co.za${src}`,
              alt: img.getAttribute('alt') || `Product image ${index + 1}`,
              isPrimary: index === 0
            });
          }
        });

        // Extract description
        const description = getText('[data-testid="pdp-description"], .product-description, .description') ||
                           getText('.product-details, .details');

        // Extract unit price
        const unitPriceText = getText('[data-testid="pdp-unit-price"], .unit-price, .price-per-unit');
        let unitPrice = null;
        if (unitPriceText) {
          const unitMatch = unitPriceText.match(/R?\s*(\d+(?:\.\d{2})?)\s*\/\s*(\w+)/);
          if (unitMatch) {
            unitPrice = {
              value: parseFloat(unitMatch[1]),
              unit: unitMatch[2].toLowerCase()
            };
          }
        }

        // Extract specifications
        const specifications = {};
        const specElements = document.querySelectorAll('[data-testid="pdp-specs"] tr, .specifications tr, .product-specs tr');
        specElements.forEach(row => {
          const cells = row.querySelectorAll('td, th');
          if (cells.length >= 2) {
            const key = cells[0].textContent.trim().toLowerCase();
            const value = cells[1].textContent.trim();
            
            if (key.includes('weight')) specifications.weight = value;
            if (key.includes('volume')) specifications.volume = value;
            if (key.includes('size')) specifications.dimensions = value;
          }
        });

        return {
          name,
          brand,
          description,
          images,
          specifications,
          price: {
            current: currentPrice,
            original: originalPrice || currentPrice,
            sourceUrl: window.location.href,
            inStock,
            isOnPromotion,
            promotionDescription: isOnPromotion ? promotionDescription : null
          },
          unitPrice
        };
      });

      if (!productData.name || !productData.price.current) {
        throw new Error('Missing required product data');
      }

      return productData;
    });
  }
}

module.exports = CheckersScraper;
