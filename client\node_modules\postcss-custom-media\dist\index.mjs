import e from"postcss-value-parser";import t from"fs";import n from"path";import{parse as s}from"postcss";function r(e,t){const n=[];let s="",r=!1,o=0,u=-1;for(;++u<e.length;){const f=e[u];"("===f?o+=1:")"===f?o>0&&(o-=1):0===o&&(t&&c.test(s+f)?r=!0:t||","!==f||(r=!0)),r?(n.push(t?new a(s+f):new i(s)),s="",r=!1):s+=f}return""!==s&&n.push(t?new a(s):new i(s)),n}class o{constructor(e){this.nodes=r(e)}invert(){return this.nodes.forEach((e=>{e.invert()})),this}clone(){return new o(String(this))}toString(){return this.nodes.join(",")}}class i{constructor(e){const[,t,n,s]=e.match(u),[,o="",i=" ",a="",c="",d="",m="",p="",l=""]=n.match(f)||[],w={before:t,after:s,afterModifier:i,originalModifier:o||"",beforeAnd:c,and:d,beforeExpression:m},h=r(p||l,!0);Object.assign(this,{modifier:o,type:a,raws:w,nodes:h})}clone(e){const t=new i(String(this));return Object.assign(t,e),t}invert(){return this.modifier=this.modifier?"":this.raws.originalModifier,this}toString(){const{raws:e}=this;return`${e.before}${this.modifier}${this.modifier?`${e.afterModifier}`:""}${this.type}${e.beforeAnd}${e.and}${e.beforeExpression}${this.nodes.join("")}${this.raws.after}`}}class a{constructor(e){const[,t,n="",s="",r=""]=e.match(c)||[null,e],o={after:n,and:s,afterAnd:r};Object.assign(this,{value:t,raws:o})}clone(e){const t=new a(String(this));return Object.assign(t,e),t}toString(){const{raws:e}=this;return`${this.value}${e.after}${e.and}${e.afterAnd}`}}const c=new RegExp("^([\\W\\w]+)(?:(?:(\\s+)(and))(\\s+))$","i"),u=new RegExp("^(\\s*)([\\W\\w]*)(\\s*)$"),f=new RegExp("^(?:(not|only)(\\s+))?(?:(all|print|screen|speech)(?:(?:(\\s+)(and))(\\s+)([\\W\\w]+))?|([\\W\\w]+))$","i");var d=e=>new o(e),m=(t,n)=>{const s={};return t.nodes.slice().forEach((t=>{if("atrule"!==t.type)return;if("custom-media"!==t.name.toLowerCase())return;let r=null;try{r=e(t.params)}catch(e){return}if(!r||!r.nodes||!r.nodes.length)return;let o=-1;for(let e=0;e<r.nodes.length;e++){const t=r.nodes[e];if("space"!==t.type&&"comment"!==t.type){if("word"===t.type&&t.value.startsWith("--")){o=e;break}return}}if(o<0)return;const i=r.nodes[o].value.trim(),a=e.stringify(r.nodes.slice(o+1)).trim();s[i]=d(a),Object(n).preserve||t.remove()})),s};function p(e){const t=Object.assign({},Object(e).customMedia,Object(e)["custom-media"]);for(const e in t)t[e]=d(t[e]);return t}function l(e){return e.map((e=>{if(e instanceof Promise)return e;if(e instanceof Function)return e();const t=e===Object(e)?e:{from:String(e)};if(Object(t).customMedia||Object(t)["custom-media"])return t;const s=n.resolve(String(t.from||""));return{type:(t.type||n.extname(s).slice(1)).toLowerCase(),from:s}})).reduce((async(e,t)=>{const{type:n,from:r}=await t;return"css"===n||"pcss"===n?Object.assign(await e,await async function(e){const t=await w(e),n=s(t,{from:e});return m(n,{preserve:!0})}(r)):"js"===n?Object.assign(await e,await async function(e){return p(await import(e))}(r)):"json"===n?Object.assign(await e,await async function(e){return p(await h(e))}(r)):Object.assign(await e,p(await t))}),{})}const w=e=>new Promise(((n,s)=>{t.readFile(e,"utf8",((e,t)=>{e?s(e):n(t)}))})),h=async e=>JSON.parse(await w(e));function g(t){if(!t)return;let n=null;try{n=e(t)}catch(e){return}if(!n||!n.nodes||!n.nodes.length)return;if(1!==n.nodes.length)return;for(;"function"===n.nodes[0].type&&""===n.nodes[0].value;)n=n.nodes[0];let s=-1;for(let e=0;e<n.nodes.length;e++){const t=n.nodes[e];if("space"!==t.type&&"comment"!==t.type){if("word"===t.type&&t.value.startsWith("--")){s=e;break}return}}return s<0?void 0:n.nodes[s].value.trim()}function y(e,t){let n=e.nodes.length-1;for(;n>=0;){const s=b(e.nodes[n],t);s.length&&e.nodes.splice(n,1,...s),--n}return e}function b(e,t){const n=[];for(const s in e.nodes){const{value:r,nodes:o}=e.nodes[s],i=g(r);if(i&&i in t){for(const r of t[i].nodes){const o=e.modifier!==r.modifier?e.modifier||r.modifier:"",a=e.clone({modifier:o,raws:!o||e.modifier?{...e.raws}:{...r.raws},type:e.type||r.type});a.type===r.type&&Object.assign(a.raws,{and:r.raws.and,beforeAnd:r.raws.beforeAnd,beforeExpression:r.raws.beforeExpression}),a.nodes.splice(s,1,...r.clone().nodes.map((t=>(e.nodes[s].raws.and&&(t.raws={...e.nodes[s].raws}),t.spaces={...e.nodes[s].spaces},t))));const c=b(a,j(t,i));c.length?n.push(...c):n.push(a)}return n}o&&o.length&&y(e.nodes[s],t)}return n}const j=(e,t)=>{const n=Object.assign({},e);return delete n[t],n};function O(e,t){return Promise.all(t.map((async t=>{if(t instanceof Function)await t($(e));else{const s=t===Object(t)?t:{to:String(t)},r=s.toJSON||$;if("customMedia"in s)s.customMedia=r(e);else if("custom-media"in s)s["custom-media"]=r(e);else{const t=String(s.to||""),o=(s.type||n.extname(t).slice(1)).toLowerCase(),i=r(e);"css"===o&&await async function(e,t){const n=`${Object.keys(t).reduce(((e,n)=>(e.push(`@custom-media ${n} ${t[n]};`),e)),[]).join("\n")}\n`;await v(e,n)}(t,i),"js"===o&&await async function(e,t){const n=`module.exports = {\n\tcustomMedia: {\n${Object.keys(t).reduce(((e,n)=>(e.push(`\t\t'${S(n)}': '${S(t[n])}'`),e)),[]).join(",\n")}\n\t}\n};\n`;await v(e,n)}(t,i),"json"===o&&await async function(e,t){const n=`${JSON.stringify({"custom-media":t},null,"\t")}\n`;await v(e,n)}(t,i),"mjs"===o&&await async function(e,t){const n=`export const customMedia = {\n${Object.keys(t).reduce(((e,n)=>(e.push(`\t'${S(n)}': '${S(t[n])}'`),e)),[]).join(",\n")}\n};\n`;await v(e,n)}(t,i)}}})))}const $=e=>Object.keys(e).reduce(((t,n)=>(t[n]=String(e[n]),t)),{}),v=(e,n)=>new Promise(((s,r)=>{t.writeFile(e,n,(e=>{e?r(e):s()}))})),S=e=>e.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r"),x=e=>{const t="preserve"in Object(e)&&Boolean(e.preserve),n=[].concat(Object(e).importFrom||[]),s=[].concat(Object(e).exportTo||[]),r=l(n),o=Symbol("customMediaHelper");return{postcssPlugin:"postcss-custom-media",Once:async(e,n)=>{n[o]=Object.assign(await r,m(e,{preserve:t})),await O(n[o],s)},AtRule:(e,n)=>{"media"===e.name&&((e,t,{preserve:n})=>{if(e.params.indexOf("--")>-1){const s=d(e.params),r=String(y(s,t));if(null===r)return;if(r===e.params)return;e.cloneBefore({params:r}),n||e.remove()}})(e,n[o],{preserve:t})}}};x.postcss=!0;export{x as default};
