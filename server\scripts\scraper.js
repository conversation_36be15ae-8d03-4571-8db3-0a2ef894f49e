const mongoose = require('mongoose');
const SparScraper = require('../scrapers/SparScraper');
const CheckersScraper = require('../scrapers/CheckersScraper');
// const PicknPayScraper = require('../scrapers/PicknPayScraper');
// const WoolworthsScraper = require('../scrapers/WoolworthsScraper');
require('dotenv').config();

class ScraperOrchestrator {
  constructor() {
    this.scrapers = [
      new SparScraper({ headless: true }),
      new CheckersScraper({ headless: true })
      // new PicknPayScraper({ headless: true }),
      // new WoolworthsScraper({ headless: true })
    ];
    this.results = [];
  }

  async initialize() {
    try {
      // Connect to MongoDB
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/basketcase', {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      console.log('Connected to MongoDB');
    } catch (error) {
      console.error('Error connecting to MongoDB:', error);
      throw error;
    }
  }

  async runScraper(scraper) {
    const startTime = Date.now();
    console.log(`\n=== Starting ${scraper.storeName} scraper ===`);
    
    try {
      const result = await scraper.scrape();
      const duration = (Date.now() - startTime) / 1000;
      
      const scraperResult = {
        store: scraper.storeName,
        success: result.success,
        scrapedCount: result.scrapedCount,
        errors: result.errors,
        duration,
        timestamp: new Date()
      };
      
      this.results.push(scraperResult);
      
      console.log(`=== ${scraper.storeName} scraper completed ===`);
      console.log(`Success: ${result.success}`);
      console.log(`Products scraped: ${result.scrapedCount}`);
      console.log(`Errors: ${result.errors.length}`);
      console.log(`Duration: ${duration}s\n`);
      
      return scraperResult;
    } catch (error) {
      console.error(`Error running ${scraper.storeName} scraper:`, error);
      
      const scraperResult = {
        store: scraper.storeName,
        success: false,
        scrapedCount: 0,
        errors: [{ error: error.message }],
        duration: (Date.now() - startTime) / 1000,
        timestamp: new Date()
      };
      
      this.results.push(scraperResult);
      return scraperResult;
    }
  }

  async runAllScrapers() {
    console.log('Starting scraping process for all stores...');
    const overallStartTime = Date.now();
    
    // Run scrapers sequentially to avoid overwhelming the system
    for (const scraper of this.scrapers) {
      await this.runScraper(scraper);
      
      // Add delay between scrapers
      if (this.scrapers.indexOf(scraper) < this.scrapers.length - 1) {
        console.log('Waiting 30 seconds before next scraper...');
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
    
    const overallDuration = (Date.now() - overallStartTime) / 1000;
    
    // Generate summary report
    const summary = this.generateSummaryReport(overallDuration);
    console.log('\n' + '='.repeat(50));
    console.log('SCRAPING SUMMARY REPORT');
    console.log('='.repeat(50));
    console.log(summary);
    
    return {
      success: true,
      results: this.results,
      summary
    };
  }

  generateSummaryReport(overallDuration) {
    const totalScraped = this.results.reduce((sum, result) => sum + result.scrapedCount, 0);
    const totalErrors = this.results.reduce((sum, result) => sum + result.errors.length, 0);
    const successfulStores = this.results.filter(result => result.success).length;
    
    let report = `
Overall Duration: ${overallDuration}s
Total Products Scraped: ${totalScraped}
Total Errors: ${totalErrors}
Successful Stores: ${successfulStores}/${this.results.length}

Store Results:`;

    this.results.forEach(result => {
      report += `
- ${result.store}: ${result.success ? 'SUCCESS' : 'FAILED'}
  Products: ${result.scrapedCount}
  Errors: ${result.errors.length}
  Duration: ${result.duration}s`;
    });

    if (totalErrors > 0) {
      report += '\n\nError Details:';
      this.results.forEach(result => {
        if (result.errors.length > 0) {
          report += `\n${result.store} Errors:`;
          result.errors.slice(0, 5).forEach(error => { // Show first 5 errors
            report += `\n  - ${error.error || error.message || JSON.stringify(error)}`;
          });
          if (result.errors.length > 5) {
            report += `\n  ... and ${result.errors.length - 5} more errors`;
          }
        }
      });
    }

    return report;
  }

  async cleanup() {
    try {
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
    } catch (error) {
      console.error('Error closing MongoDB connection:', error);
    }
  }
}

// CLI interface
async function main() {
  const orchestrator = new ScraperOrchestrator();
  
  try {
    await orchestrator.initialize();
    
    const args = process.argv.slice(2);
    const storeName = args[0];
    
    if (storeName) {
      // Run specific store scraper
      const scraper = orchestrator.scrapers.find(s => 
        s.storeName.toLowerCase() === storeName.toLowerCase()
      );
      
      if (!scraper) {
        console.error(`Store "${storeName}" not found. Available stores: ${orchestrator.scrapers.map(s => s.storeName).join(', ')}`);
        process.exit(1);
      }
      
      console.log(`Running scraper for ${scraper.storeName} only...`);
      await orchestrator.runScraper(scraper);
    } else {
      // Run all scrapers
      await orchestrator.runAllScrapers();
    }
    
  } catch (error) {
    console.error('Fatal error in scraping process:', error);
    process.exit(1);
  } finally {
    await orchestrator.cleanup();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT. Gracefully shutting down...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM. Gracefully shutting down...');
  process.exit(0);
});

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = ScraperOrchestrator;
