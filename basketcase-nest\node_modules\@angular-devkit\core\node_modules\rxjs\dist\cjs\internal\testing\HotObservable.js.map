{"version": 3, "file": "HotObservable.js", "sourceRoot": "", "sources": ["../../../../src/internal/testing/HotObservable.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,sCAAqC;AAErC,gDAA+C;AAI/C,+DAA8D;AAC9D,mDAAkD;AAClD,gDAAsD;AAEtD;IAAsC,iCAAU;IAQ9C,uBAAmB,QAAuB,EAAE,SAAoB;QAAhE,YACE,iBAAO,SAER;QAHkB,cAAQ,GAAR,QAAQ,CAAe;QAPnC,mBAAa,GAAsB,EAAE,CAAC;QAS3C,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;IAC7B,CAAC;IAGS,kCAAU,GAApB,UAAqB,UAA2B;QAC9C,IAAM,OAAO,GAAqB,IAAI,CAAC;QACvC,IAAM,KAAK,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC3C,IAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACxC,YAAY,CAAC,GAAG,CACd,IAAI,2BAAY,CAAC;YACf,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CACH,CAAC;QACF,YAAY,CAAC,GAAG,CAAC,iBAAM,UAAU,YAAC,UAAU,CAAC,CAAC,CAAC;QAC/C,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,6BAAK,GAAL;QACE,IAAM,OAAO,GAAG,IAAI,CAAC;QACrB,IAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;gCAEtC,CAAC;YACR,CAAC;gBACO,IAAA,KAA0B,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAA3C,YAAY,kBAAA,EAAE,KAAK,WAAwB,CAAC;gBAEpD,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACzB,kCAAmB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;gBAC7C,CAAC,EAAE,KAAK,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC;;QAPP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE;oBAA9B,CAAC;SAQT;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAzCD,CAAsC,iBAAO,GAyC5C;AAzCY,sCAAa;AA0C1B,yBAAW,CAAC,aAAa,EAAE,CAAC,2CAAoB,CAAC,CAAC,CAAC"}