import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type PriceDocument = Price & Document;

@Schema({ timestamps: true })
export class Price {
  @ApiProperty({ description: 'Product reference' })
  @Prop({ type: Types.ObjectId, ref: 'Product', required: true, index: true })
  product: Types.ObjectId;

  @ApiProperty({ description: 'Store reference' })
  @Prop({ type: Types.ObjectId, ref: 'Store', required: true, index: true })
  store: Types.ObjectId;

  @ApiProperty({ description: 'Current price' })
  @Prop({ required: true, index: true })
  current: number;

  @ApiProperty({ description: 'Original price (before discount)' })
  @Prop()
  original?: number;

  @ApiProperty({ description: 'Currency code' })
  @Prop({ default: 'ZAR' })
  currency: string;

  @ApiProperty({ description: 'Product availability' })
  @Prop({
    inStock: { type: Boolean, default: true },
    stockLevel: String,
    lastChecked: Date,
  })
  availability?: {
    inStock: boolean;
    stockLevel?: string;
    lastChecked?: Date;
  };

  @ApiProperty({ description: 'Promotion information' })
  @Prop({
    isOnPromotion: { type: Boolean, default: false },
    promotionDescription: String,
    promotionType: String,
    validFrom: Date,
    validUntil: Date,
  })
  promotion?: {
    isOnPromotion: boolean;
    promotionDescription?: string;
    promotionType?: string;
    validFrom?: Date;
    validUntil?: Date;
  };

  @ApiProperty({ description: 'Price history' })
  @Prop([{
    price: { type: Number, required: true },
    date: { type: Date, required: true },
    source: String,
  }])
  priceHistory?: Array<{
    price: number;
    date: Date;
    source?: string;
  }>;

  @ApiProperty({ description: 'Scraping information' })
  @Prop({
    lastScraped: Date,
    sourceUrl: String,
    scrapingMethod: String,
    confidence: Number,
  })
  scrapingInfo?: {
    lastScraped?: Date;
    sourceUrl?: string;
    scrapingMethod?: string;
    confidence?: number;
  };

  @ApiProperty({ description: 'Whether price is active' })
  @Prop({ default: true, index: true })
  isActive: boolean;
}

export const PriceSchema = SchemaFactory.createForClass(Price);
