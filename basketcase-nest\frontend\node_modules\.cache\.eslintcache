[{"C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\index.tsx": "1", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\App.tsx": "2", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\StoresPage.tsx": "3", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\ProductDetailPage.tsx": "4", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\ComparePage.tsx": "5", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\HomePage.tsx": "6", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\ProductsPage.tsx": "7", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\AboutPage.tsx": "8", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\components\\Navbar.tsx": "9", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\components\\Footer.tsx": "10", "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\services\\api.ts": "11"}, {"size": 252, "mtime": 1752825284230, "results": "12", "hashOfConfig": "13"}, {"size": 2105, "mtime": 1752832910629, "results": "14", "hashOfConfig": "13"}, {"size": 3046, "mtime": 1752833047993, "results": "15", "hashOfConfig": "13"}, {"size": 892, "mtime": 1752830161400, "results": "16", "hashOfConfig": "13"}, {"size": 1496, "mtime": 1752830215482, "results": "17", "hashOfConfig": "13"}, {"size": 9758, "mtime": 1752832978057, "results": "18", "hashOfConfig": "13"}, {"size": 4031, "mtime": 1752832995829, "results": "19", "hashOfConfig": "13"}, {"size": 4370, "mtime": 1752830242417, "results": "20", "hashOfConfig": "13"}, {"size": 2453, "mtime": 1752830034525, "results": "21", "hashOfConfig": "13"}, {"size": 3096, "mtime": 1752830060854, "results": "22", "hashOfConfig": "13"}, {"size": 6881, "mtime": 1752833145410, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3s4yf5", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\index.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\App.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\StoresPage.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\ProductDetailPage.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\ComparePage.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\ProductsPage.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\components\\Footer.tsx", ["57", "58", "59"], [], "C:\\laragon\\www\\basketcase\\basketcase-nest\\frontend\\src\\services\\api.ts", ["60"], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 51, "column": 15, "nodeType": "63", "endLine": 51, "endColumn": 56}, {"ruleId": "61", "severity": 1, "message": "62", "line": 54, "column": 15, "nodeType": "63", "endLine": 54, "endColumn": 56}, {"ruleId": "61", "severity": 1, "message": "62", "line": 57, "column": 15, "nodeType": "63", "endLine": 57, "endColumn": 56}, {"ruleId": "64", "severity": 1, "message": "65", "line": 9, "column": 3, "nodeType": "66", "messageId": "67", "endLine": 9, "endColumn": 17}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'ScrapingStatus' is defined but never used.", "Identifier", "unusedVar"]