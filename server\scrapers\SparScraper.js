const BaseScraper = require('./BaseScraper');

class SparScraper extends BaseScraper {
  constructor(config = {}) {
    super('SPAR', config);
    this.baseUrl = 'https://www.spar.co.za';
    this.categories = [
      { name: 'Dairy & Eggs', url: '/Food-Cupboard/Dairy-Eggs' },
      { name: 'Meat & Poultry', url: '/Food-Cupboard/Meat-Poultry' },
      { name: 'Fruits & Vegetables', url: '/Food-Cupboard/Fresh-Produce' },
      { name: '<PERSON><PERSON>', url: '/Food-Cupboard/Bakery' },
      { name: 'Pantry Staples', url: '/Food-Cupboard/Pantry-Staples' },
      { name: 'Snacks & Confectionery', url: '/Food-Cupboard/Snacks-Confectionery' },
      { name: 'Beverages', url: '/Food-Cupboard/Beverages' },
      { name: 'Frozen Foods', url: '/Food-Cupboard/Frozen' }
    ];
  }

  async scrapeProducts() {
    const store = await this.getStore();
    
    for (const category of this.categories) {
      try {
        console.log(`Scraping ${category.name} category...`);
        await this.scrapeCategoryProducts(category, store);
        await this.delay(2000); // Delay between categories
      } catch (error) {
        console.error(`Error scraping category ${category.name}:`, error);
        this.errors.push({
          category: category.name,
          error: error.message
        });
      }
    }
  }

  async scrapeCategoryProducts(category, store) {
    const categoryUrl = `${this.baseUrl}${category.url}`;
    await this.navigateToPage(categoryUrl);

    let currentPage = 1;
    let hasNextPage = true;

    while (hasNextPage && currentPage <= 10) { // Limit to 10 pages per category
      try {
        console.log(`Scraping page ${currentPage} of ${category.name}...`);
        
        // Wait for products to load
        await this.page.waitForSelector('.product-item, .product-card, [data-testid="product"]', { timeout: 10000 });
        
        // Get product links from current page
        const productLinks = await this.page.evaluate(() => {
          const links = [];
          const productElements = document.querySelectorAll('.product-item a, .product-card a, [data-testid="product"] a');
          
          productElements.forEach(element => {
            const href = element.getAttribute('href');
            if (href && href.includes('/product/')) {
              links.push(href);
            }
          });
          
          return [...new Set(links)]; // Remove duplicates
        });

        console.log(`Found ${productLinks.length} products on page ${currentPage}`);

        // Scrape each product
        for (const productLink of productLinks.slice(0, 20)) { // Limit products per page
          try {
            const fullUrl = productLink.startsWith('http') ? productLink : `${this.baseUrl}${productLink}`;
            const productData = await this.scrapeProductDetails(fullUrl);
            
            if (productData) {
              productData.category = category.name;
              const product = await this.findOrCreateProduct(productData);
              await this.updatePrice(product, store, productData.price);
            }
            
            await this.delay(500); // Small delay between products
          } catch (error) {
            console.error(`Error scraping product ${productLink}:`, error);
            this.errors.push({
              productUrl: productLink,
              error: error.message
            });
          }
        }

        // Check for next page
        hasNextPage = await this.page.evaluate(() => {
          const nextButton = document.querySelector('.pagination .next, .pagination-next, [aria-label="Next page"]');
          return nextButton && !nextButton.disabled && !nextButton.classList.contains('disabled');
        });

        if (hasNextPage) {
          await this.page.click('.pagination .next, .pagination-next, [aria-label="Next page"]');
          await this.page.waitForLoadState('networkidle');
          currentPage++;
        }

      } catch (error) {
        console.error(`Error on page ${currentPage} of ${category.name}:`, error);
        hasNextPage = false;
      }
    }
  }

  async scrapeProductDetails(productUrl) {
    return await this.retryOperation(async () => {
      await this.navigateToPage(productUrl);
      
      // Wait for product details to load
      await this.page.waitForSelector('.product-detail, .product-info, [data-testid="product-detail"]', { timeout: 10000 });

      const productData = await this.page.evaluate(() => {
        // Helper function to get text content safely
        const getText = (selector) => {
          const element = document.querySelector(selector);
          return element ? element.textContent.trim() : '';
        };

        // Helper function to get attribute safely
        const getAttribute = (selector, attribute) => {
          const element = document.querySelector(selector);
          return element ? element.getAttribute(attribute) : '';
        };

        // Extract product name
        const name = getText('h1, .product-title, [data-testid="product-name"]') ||
                    getText('.product-name, .product-detail h1');

        // Extract brand
        const brand = getText('.brand, .product-brand, [data-testid="brand"]') ||
                     getText('.manufacturer, .product-manufacturer');

        // Extract price information
        const priceText = getText('.price, .product-price, [data-testid="price"]') ||
                         getText('.current-price, .price-current');
        
        const originalPriceText = getText('.original-price, .price-original, .was-price') ||
                                 getText('.price-was, .strikethrough');

        // Parse prices
        const parsePrice = (text) => {
          if (!text) return null;
          const match = text.match(/R?\s*(\d+(?:\.\d{2})?)/);
          return match ? parseFloat(match[1]) : null;
        };

        const currentPrice = parsePrice(priceText);
        const originalPrice = parsePrice(originalPriceText);

        // Extract promotion information
        const promotionElement = document.querySelector('.promotion, .special, .offer, [data-testid="promotion"]');
        const isOnPromotion = !!promotionElement || (originalPrice && originalPrice > currentPrice);
        const promotionDescription = promotionElement ? promotionElement.textContent.trim() : '';

        // Extract stock information
        const stockElement = document.querySelector('.stock, .availability, [data-testid="stock"]');
        const stockText = stockElement ? stockElement.textContent.toLowerCase() : '';
        const inStock = !stockText.includes('out of stock') && !stockText.includes('unavailable');

        // Extract images
        const images = [];
        const imageElements = document.querySelectorAll('.product-image img, .product-gallery img, [data-testid="product-image"]');
        imageElements.forEach((img, index) => {
          const src = img.getAttribute('src') || img.getAttribute('data-src');
          if (src) {
            images.push({
              url: src.startsWith('http') ? src : `https://www.spar.co.za${src}`,
              alt: img.getAttribute('alt') || `Product image ${index + 1}`,
              isPrimary: index === 0
            });
          }
        });

        // Extract description
        const description = getText('.product-description, .description, [data-testid="description"]') ||
                           getText('.product-details, .details');

        // Extract unit price if available
        const unitPriceText = getText('.unit-price, .price-per-unit, [data-testid="unit-price"]');
        let unitPrice = null;
        if (unitPriceText) {
          const unitMatch = unitPriceText.match(/R?\s*(\d+(?:\.\d{2})?)\s*\/\s*(\w+)/);
          if (unitMatch) {
            unitPrice = {
              value: parseFloat(unitMatch[1]),
              unit: unitMatch[2].toLowerCase()
            };
          }
        }

        return {
          name,
          brand,
          description,
          images,
          price: {
            current: currentPrice,
            original: originalPrice || currentPrice,
            sourceUrl: window.location.href,
            inStock,
            isOnPromotion,
            promotionDescription: isOnPromotion ? promotionDescription : null
          },
          unitPrice
        };
      });

      // Validate required fields
      if (!productData.name || !productData.price.current) {
        throw new Error('Missing required product data');
      }

      return productData;
    });
  }
}

module.exports = SparScraper;
