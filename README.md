# 🛒 BasketCase - South African Grocery Price Comparison

A comprehensive MERN stack application that compares grocery prices across major South African retailers including SPAR, Checkers, Pick n Pay, and Woolworths. Find the best deals and save money on your grocery shopping!

## ✨ Features

- **Price Comparison**: Compare prices across multiple retailers
- **Geolocation Support**: Find stores near your location
- **Interactive Maps**: View store locations with Leaflet maps
- **Product Search**: Advanced search with filters and suggestions
- **Price History**: Track price changes over time
- **Automated Scraping**: Daily price updates via Playwright scrapers
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Data**: Up-to-date pricing information

## 🏪 Supported Retailers

- **SPAR** - South African retailer
- **Checkers** - Shoprite Holdings
- **Pick n Pay** - Major grocery chain
- **Woolworths** - Premium retailer

## 🚀 One-Click Quick Start

### 🎯 **Super Easy Setup (Recommended)**

Just clone and run - everything is automated!

#### Windows
```bash
git clone https://github.com/yourusername/basketcase.git
cd basketcase
start.bat
```

#### Linux/Mac
```bash
git clone https://github.com/yourusername/basketcase.git
cd basketcase
./start.sh
```

#### Alternative (Cross-platform)
```bash
git clone https://github.com/yourusername/basketcase.git
cd basketcase
npm run quickstart
```

### 🔧 **What the startup script does:**
1. **Detects your system** and available tools
2. **Offers 3 setup options:**
   - 🐳 **Docker Full Stack** (Recommended - includes MongoDB)
   - 💻 **Local Development** (requires local MongoDB)
   - 🔄 **Hybrid** (Docker MongoDB + local development)
3. **Installs all dependencies** automatically
4. **Sets up environment** configuration
5. **Creates sample store data**
6. **Starts the application**
7. **Opens your browser** to http://localhost:3000
8. **Optionally runs initial scraping**

### 📋 **Prerequisites**

**For Docker setup (Recommended):**
- Docker Desktop

**For Local setup:**
- Node.js (v16+)
- MongoDB (v4.4+)
- npm

### 🐳 **Docker Setup (Zero Configuration)**

The easiest way - includes everything:

```bash
# Clone and start with Docker
git clone https://github.com/yourusername/basketcase.git
cd basketcase
docker-compose -f docker-compose.dev.yml up --build
```

**What you get:**
- ✅ MongoDB database
- ✅ Backend API server
- ✅ Frontend React app
- ✅ Sample store data
- ✅ All dependencies installed

### 💻 **Manual Setup (If you prefer control)**

```bash
# 1. Clone repository
git clone https://github.com/yourusername/basketcase.git
cd basketcase

# 2. Install dependencies
npm run install:all

# 3. Setup environment
npm run setup

# 4. Create sample data
npm run setup:stores

# 5. Start application
npm run dev

# 6. (Optional) Run scraping
npm run scrape
```

## 📁 Project Structure

```
basketcase/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   ├── hooks/         # Custom React hooks
│   │   └── utils/         # Utility functions
│   └── public/
├── server/                # Node.js backend
│   ├── models/           # MongoDB models
│   ├── routes/           # API routes
│   ├── services/         # Business logic
│   ├── scrapers/         # Web scrapers
│   └── scripts/          # Utility scripts
├── scripts/              # Setup and utility scripts
└── docker-compose.yml    # Docker configuration
```

## 🔧 Configuration

### Environment Variables

#### Server (.env)
```env
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000
MONGODB_URI=mongodb://localhost:27017/basketcase
JWT_SECRET=your_jwt_secret_here
```

#### Client (.env)
```env
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_ENABLE_GEOLOCATION=true
```

## 📊 API Endpoints

### Products
- `GET /api/products` - Search products
- `GET /api/products/:id` - Get product details
- `GET /api/products/:id/prices` - Get price history

### Stores
- `GET /api/stores` - Get all stores
- `GET /api/stores/nearby` - Find nearby stores
- `GET /api/stores/:id` - Get store details

### Prices
- `GET /api/prices/compare` - Compare prices
- `GET /api/prices/trending` - Get trending prices
- `GET /api/prices/promotions` - Get current promotions

### Scheduler
- `GET /api/scheduler/status` - Get scraper status
- `POST /api/scheduler/run` - Run manual scraping

## 🕷️ Web Scraping

The application uses Playwright to scrape product data from retailer websites:

```bash
# Run all scrapers
npm run scrape

# Run specific scraper
npm run scrape:spar
npm run scrape:checkers
```

### Scraper Features
- **Error Handling**: Robust error handling and retry logic
- **Rate Limiting**: Respectful scraping with delays
- **Data Validation**: Ensures data quality
- **Scheduling**: Automated daily scraping

## 🗺️ Maps Integration

Uses Leaflet with OpenStreetMap for:
- Store location visualization
- User geolocation
- Distance calculations
- Interactive store markers

## 📱 Available Scripts

```bash
# Development
npm run dev              # Start both client and server
npm run client:dev       # Start client only
npm run server:dev       # Start server only

# Production
npm start               # Start production build
npm run build          # Build client for production

# Scraping
npm run scrape         # Run all scrapers
npm run scrape:spar    # Run SPAR scraper only

# Docker
npm run docker:dev     # Start development containers
npm run docker:up      # Start production containers
npm run docker:build   # Build Docker images

# Setup
npm run setup          # Initial setup
npm run install:all    # Install all dependencies
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run server tests
npm run server:test

# Run client tests
npm run client:test
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build and start containers
docker-compose up -d

# View logs
docker-compose logs -f
```

### Manual Deployment
1. Build the client: `npm run build`
2. Set production environment variables
3. Start the server: `npm run server:start`
4. Serve client build with nginx or similar

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This application is for educational and personal use only. Please respect the terms of service of the retailers' websites. The developers are not responsible for any misuse of this application.

## 🆘 Support

If you encounter any issues:

1. Check the [Issues](https://github.com/yourusername/basketcase/issues) page
2. Create a new issue with detailed information
3. Include error logs and system information

## 🙏 Acknowledgments

- [React](https://reactjs.org/) - Frontend framework
- [Node.js](https://nodejs.org/) - Backend runtime
- [MongoDB](https://www.mongodb.com/) - Database
- [Playwright](https://playwright.dev/) - Web scraping
- [Leaflet](https://leafletjs.com/) - Maps
- [OpenStreetMap](https://www.openstreetmap.org/) - Map data
