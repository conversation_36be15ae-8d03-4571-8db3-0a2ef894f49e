{"ast": null, "code": "var _jsxFileName = \"c:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\components\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport './LoadingSpinner.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'medium',\n  message = 'Loading...',\n  showMessage = true,\n  fullScreen = false\n}) => {\n  const spinnerClass = `loading-spinner ${size} ${fullScreen ? 'fullscreen' : ''}`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: spinnerClass,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"spinner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), showMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-message\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 23\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "message", "showMessage", "fullScreen", "spinnerClass", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["c:/laragon/www/basketcase/client/src/components/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\nimport './LoadingSpinner.css';\n\nconst LoadingSpinner = ({ \n  size = 'medium', \n  message = 'Loading...', \n  showMessage = true,\n  fullScreen = false \n}) => {\n  const spinnerClass = `loading-spinner ${size} ${fullScreen ? 'fullscreen' : ''}`;\n\n  return (\n    <div className={spinnerClass}>\n      <div className=\"spinner\">\n        <div className=\"spinner-ring\"></div>\n        <div className=\"spinner-ring\"></div>\n        <div className=\"spinner-ring\"></div>\n        <div className=\"spinner-ring\"></div>\n      </div>\n      {showMessage && <div className=\"loading-message\">{message}</div>}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EACtBC,IAAI,GAAG,QAAQ;EACfC,OAAO,GAAG,YAAY;EACtBC,WAAW,GAAG,IAAI;EAClBC,UAAU,GAAG;AACf,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAG,mBAAmBJ,IAAI,IAAIG,UAAU,GAAG,YAAY,GAAG,EAAE,EAAE;EAEhF,oBACEL,OAAA;IAAKO,SAAS,EAAED,YAAa;IAAAE,QAAA,gBAC3BR,OAAA;MAAKO,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBR,OAAA;QAAKO,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCZ,OAAA;QAAKO,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCZ,OAAA;QAAKO,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCZ,OAAA;QAAKO,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,EACLR,WAAW,iBAAIJ,OAAA;MAAKO,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAEL;IAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7D,CAAC;AAEV,CAAC;AAACC,EAAA,GAnBIZ,cAAc;AAqBpB,eAAeA,cAAc;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}