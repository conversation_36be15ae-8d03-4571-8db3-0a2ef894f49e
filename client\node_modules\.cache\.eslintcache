[{"c:\\laragon\\www\\basketcase\\client\\src\\index.js": "1", "c:\\laragon\\www\\basketcase\\client\\src\\App.js": "2", "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "3", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js": "4", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "5", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "6", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "7", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "8", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "9", "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "10", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "11", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "12", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js": "13", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "14", "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js": "15", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "16", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "17", "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "18", "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "19"}, {"size": 535, "mtime": 1752743717447, "results": "20", "hashOfConfig": "21"}, {"size": 931, "mtime": 1752745250835, "results": "22", "hashOfConfig": "21"}, {"size": 362, "mtime": 1752743718231, "results": "23", "hashOfConfig": "21"}, {"size": 1139, "mtime": 1752747688516, "results": "24", "hashOfConfig": "21"}, {"size": 11966, "mtime": 1752752502030, "results": "25", "hashOfConfig": "21"}, {"size": 14799, "mtime": 1752745778135, "results": "26", "hashOfConfig": "21"}, {"size": 2352, "mtime": 1752745268589, "results": "27", "hashOfConfig": "21"}, {"size": 2852, "mtime": 1752745603421, "results": "28", "hashOfConfig": "21"}, {"size": 10550, "mtime": 1752745717131, "results": "29", "hashOfConfig": "21"}, {"size": 5505, "mtime": 1752745312884, "results": "30", "hashOfConfig": "21"}, {"size": 664, "mtime": 1752745559823, "results": "31", "hashOfConfig": "21"}, {"size": 4557, "mtime": 1752745499332, "results": "32", "hashOfConfig": "21"}, {"size": 1343, "mtime": 1752745437409, "results": "33", "hashOfConfig": "21"}, {"size": 1212, "mtime": 1752745575156, "results": "34", "hashOfConfig": "21"}, {"size": 7348, "mtime": 1752745539496, "results": "35", "hashOfConfig": "21"}, {"size": 8300, "mtime": 1752745367796, "results": "36", "hashOfConfig": "21"}, {"size": 8664, "mtime": 1752745666834, "results": "37", "hashOfConfig": "21"}, {"size": 6950, "mtime": 1752745174942, "results": "38", "hashOfConfig": "21"}, {"size": 3402, "mtime": 1752745207658, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1extrba", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "c:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js", ["97", "98"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["99"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["100"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["101"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["102", "103"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["104", "105"], [], {"ruleId": "106", "severity": 1, "message": "107", "line": 7, "column": 19, "nodeType": "108", "messageId": "109", "endLine": 7, "endColumn": 29}, {"ruleId": "106", "severity": 1, "message": "110", "line": 8, "column": 17, "nodeType": "108", "messageId": "109", "endLine": 8, "endColumn": 25}, {"ruleId": "111", "severity": 1, "message": "112", "line": 108, "column": 6, "nodeType": "113", "endLine": 108, "endColumn": 15, "suggestions": "114"}, {"ruleId": "111", "severity": 1, "message": "115", "line": 30, "column": 6, "nodeType": "113", "endLine": 30, "endColumn": 20, "suggestions": "116"}, {"ruleId": "111", "severity": 1, "message": "117", "line": 36, "column": 6, "nodeType": "113", "endLine": 36, "endColumn": 8, "suggestions": "118"}, {"ruleId": "106", "severity": 1, "message": "119", "line": 8, "column": 10, "nodeType": "108", "messageId": "109", "endLine": 8, "endColumn": 24}, {"ruleId": "106", "severity": 1, "message": "120", "line": 8, "column": 26, "nodeType": "108", "messageId": "109", "endLine": 8, "endColumn": 43}, {"ruleId": "111", "severity": 1, "message": "121", "line": 8, "column": 9, "nodeType": "122", "endLine": 13, "endColumn": 4}, {"ruleId": "111", "severity": 1, "message": "123", "line": 8, "column": 9, "nodeType": "122", "endLine": 13, "endColumn": 4}, "no-unused-vars", "'setLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["124"], "React Hook useEffect has a missing dependency: 'loadProductData'. Either include it or remove the dependency array.", ["125"], "React Hook useEffect has a missing dependency: 'loadStores'. Either include it or remove the dependency array.", ["126"], "'manualLocation' is assigned a value but never used.", "'setManualLocation' is assigned a value but never used.", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 58) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", "VariableDeclarator", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 105) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", {"desc": "127", "fix": "128"}, {"desc": "129", "fix": "130"}, {"desc": "131", "fix": "132"}, "Update the dependencies array to be: [filters, loadProducts]", {"range": "133", "text": "134"}, "Update the dependencies array to be: [id, loadProductData, location]", {"range": "135", "text": "136"}, "Update the dependencies array to be: [loadStores]", {"range": "137", "text": "138"}, [3358, 3367], "[filters, loadProducts]", [1151, 1165], "[id, loadProductData, location]", [1114, 1116], "[loadStores]"]