[{"c:\\laragon\\www\\basketcase\\client\\src\\index.js": "1", "c:\\laragon\\www\\basketcase\\client\\src\\App.js": "2", "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "3", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js": "4", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "5", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "6", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "7", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "8", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "9", "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "10", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "11", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "12", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js": "13", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "14", "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js": "15", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "16", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "17", "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "18", "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "19", "C:\\laragon\\www\\basketcase\\client\\src\\index.js": "20", "C:\\laragon\\www\\basketcase\\client\\src\\App.js": "21", "C:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "22", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js": "23", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "24", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "25", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "26", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "27", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "28", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "29", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "30", "C:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "31", "C:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "32", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "33", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js": "34", "C:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "35", "C:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js": "36", "C:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "37", "C:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "38"}, {"size": 535, "mtime": 1752743717447, "results": "39", "hashOfConfig": "40"}, {"size": 931, "mtime": 1752745250835, "results": "41", "hashOfConfig": "40"}, {"size": 362, "mtime": 1752743718231, "results": "42", "hashOfConfig": "40"}, {"size": 1139, "mtime": 1752747688516, "results": "43", "hashOfConfig": "40"}, {"size": 11966, "mtime": 1752752502030, "results": "44", "hashOfConfig": "40"}, {"size": 14799, "mtime": 1752745778135, "results": "45", "hashOfConfig": "40"}, {"size": 2352, "mtime": 1752745268589, "results": "46", "hashOfConfig": "40"}, {"size": 2852, "mtime": 1752745603421, "results": "47", "hashOfConfig": "40"}, {"size": 10550, "mtime": 1752745717131, "results": "48", "hashOfConfig": "40"}, {"size": 5505, "mtime": 1752745312884, "results": "49", "hashOfConfig": "40"}, {"size": 664, "mtime": 1752745559823, "results": "50", "hashOfConfig": "40"}, {"size": 4557, "mtime": 1752745499332, "results": "51", "hashOfConfig": "40"}, {"size": 1343, "mtime": 1752745437409, "results": "52", "hashOfConfig": "40"}, {"size": 1212, "mtime": 1752745575156, "results": "53", "hashOfConfig": "40"}, {"size": 7348, "mtime": 1752745539496, "results": "54", "hashOfConfig": "40"}, {"size": 8300, "mtime": 1752745367796, "results": "55", "hashOfConfig": "40"}, {"size": 8664, "mtime": 1752745666834, "results": "56", "hashOfConfig": "40"}, {"size": 6950, "mtime": 1752745174942, "results": "57", "hashOfConfig": "40"}, {"size": 3402, "mtime": 1752745207658, "results": "58", "hashOfConfig": "40"}, {"size": 535, "mtime": 1752743717447, "results": "59", "hashOfConfig": "60"}, {"size": 931, "mtime": 1752745250835, "results": "61", "hashOfConfig": "60"}, {"size": 362, "mtime": 1752743718231, "results": "62", "hashOfConfig": "60"}, {"size": 1139, "mtime": 1752747688516, "results": "63", "hashOfConfig": "60"}, {"size": 14799, "mtime": 1752745778135, "results": "64", "hashOfConfig": "60"}, {"size": 11966, "mtime": 1752752502030, "results": "65", "hashOfConfig": "60"}, {"size": 2352, "mtime": 1752745268589, "results": "66", "hashOfConfig": "60"}, {"size": 2852, "mtime": 1752745603421, "results": "67", "hashOfConfig": "60"}, {"size": 10550, "mtime": 1752745717131, "results": "68", "hashOfConfig": "60"}, {"size": 1212, "mtime": 1752745575156, "results": "69", "hashOfConfig": "60"}, {"size": 4557, "mtime": 1752745499332, "results": "70", "hashOfConfig": "60"}, {"size": 5505, "mtime": 1752745312884, "results": "71", "hashOfConfig": "60"}, {"size": 664, "mtime": 1752745559823, "results": "72", "hashOfConfig": "60"}, {"size": 8664, "mtime": 1752745666834, "results": "73", "hashOfConfig": "60"}, {"size": 1343, "mtime": 1752745437409, "results": "74", "hashOfConfig": "60"}, {"size": 8300, "mtime": 1752745367796, "results": "75", "hashOfConfig": "60"}, {"size": 7348, "mtime": 1752745539496, "results": "76", "hashOfConfig": "60"}, {"size": 6950, "mtime": 1752745174942, "results": "77", "hashOfConfig": "60"}, {"size": 3402, "mtime": 1752745207658, "results": "78", "hashOfConfig": "60"}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1extrba", {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ge4x1l", {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "c:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js", ["193", "194"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["195"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["196"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["197"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["198", "199"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["200", "201"], [], "C:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js", ["202", "203"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["204"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["205"], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["206"], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["207", "208"], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["209", "210"], [], {"ruleId": "211", "severity": 1, "message": "212", "line": 7, "column": 19, "nodeType": "213", "messageId": "214", "endLine": 7, "endColumn": 29}, {"ruleId": "211", "severity": 1, "message": "215", "line": 8, "column": 17, "nodeType": "213", "messageId": "214", "endLine": 8, "endColumn": 25}, {"ruleId": "216", "severity": 1, "message": "217", "line": 108, "column": 6, "nodeType": "218", "endLine": 108, "endColumn": 15, "suggestions": "219"}, {"ruleId": "216", "severity": 1, "message": "220", "line": 30, "column": 6, "nodeType": "218", "endLine": 30, "endColumn": 20, "suggestions": "221"}, {"ruleId": "216", "severity": 1, "message": "222", "line": 36, "column": 6, "nodeType": "218", "endLine": 36, "endColumn": 8, "suggestions": "223"}, {"ruleId": "211", "severity": 1, "message": "224", "line": 8, "column": 10, "nodeType": "213", "messageId": "214", "endLine": 8, "endColumn": 24}, {"ruleId": "211", "severity": 1, "message": "225", "line": 8, "column": 26, "nodeType": "213", "messageId": "214", "endLine": 8, "endColumn": 43}, {"ruleId": "216", "severity": 1, "message": "226", "line": 8, "column": 9, "nodeType": "227", "endLine": 13, "endColumn": 4}, {"ruleId": "216", "severity": 1, "message": "228", "line": 8, "column": 9, "nodeType": "227", "endLine": 13, "endColumn": 4}, {"ruleId": "211", "severity": 1, "message": "212", "line": 7, "column": 19, "nodeType": "213", "messageId": "214", "endLine": 7, "endColumn": 29}, {"ruleId": "211", "severity": 1, "message": "215", "line": 8, "column": 17, "nodeType": "213", "messageId": "214", "endLine": 8, "endColumn": 25}, {"ruleId": "216", "severity": 1, "message": "220", "line": 30, "column": 6, "nodeType": "218", "endLine": 30, "endColumn": 20, "suggestions": "229"}, {"ruleId": "216", "severity": 1, "message": "217", "line": 108, "column": 6, "nodeType": "218", "endLine": 108, "endColumn": 15, "suggestions": "230"}, {"ruleId": "216", "severity": 1, "message": "222", "line": 36, "column": 6, "nodeType": "218", "endLine": 36, "endColumn": 8, "suggestions": "231"}, {"ruleId": "211", "severity": 1, "message": "224", "line": 8, "column": 10, "nodeType": "213", "messageId": "214", "endLine": 8, "endColumn": 24}, {"ruleId": "211", "severity": 1, "message": "225", "line": 8, "column": 26, "nodeType": "213", "messageId": "214", "endLine": 8, "endColumn": 43}, {"ruleId": "216", "severity": 1, "message": "226", "line": 8, "column": 9, "nodeType": "227", "endLine": 13, "endColumn": 4}, {"ruleId": "216", "severity": 1, "message": "228", "line": 8, "column": 9, "nodeType": "227", "endLine": 13, "endColumn": 4}, "no-unused-vars", "'setLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["232"], "React Hook useEffect has a missing dependency: 'loadProductData'. Either include it or remove the dependency array.", ["233"], "React Hook useEffect has a missing dependency: 'loadStores'. Either include it or remove the dependency array.", ["234"], "'manualLocation' is assigned a value but never used.", "'setManualLocation' is assigned a value but never used.", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 58) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", "VariableDeclarator", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 105) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", ["235"], ["236"], ["237"], {"desc": "238", "fix": "239"}, {"desc": "240", "fix": "241"}, {"desc": "242", "fix": "243"}, {"desc": "240", "fix": "244"}, {"desc": "238", "fix": "245"}, {"desc": "242", "fix": "246"}, "Update the dependencies array to be: [filters, loadProducts]", {"range": "247", "text": "248"}, "Update the dependencies array to be: [id, loadProductData, location]", {"range": "249", "text": "250"}, "Update the dependencies array to be: [loadStores]", {"range": "251", "text": "252"}, {"range": "253", "text": "250"}, {"range": "254", "text": "248"}, {"range": "255", "text": "252"}, [3358, 3367], "[filters, loadProducts]", [1151, 1165], "[id, loadProductData, location]", [1114, 1116], "[loadStores]", [1151, 1165], [3358, 3367], [1114, 1116]]