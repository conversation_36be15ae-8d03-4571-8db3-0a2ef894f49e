[{"c:\\laragon\\www\\basketcase\\client\\src\\index.js": "1", "c:\\laragon\\www\\basketcase\\client\\src\\App.js": "2", "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "3", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "4", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "5", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "6", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "7", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js": "8", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "9", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "10", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "11", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "12", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "13", "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js": "14", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js": "15", "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "16", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "17", "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "18", "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "19", "C:\\laragon\\www\\basketcase\\client\\src\\index.js": "20", "C:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "21", "C:\\laragon\\www\\basketcase\\client\\src\\App.js": "22", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "23", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "24", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js": "25", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "26", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "27", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "28", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "29", "C:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "30", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "31", "C:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "32", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "33", "C:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "34", "C:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "35", "C:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "36"}, {"size": 582, "mtime": 1752754083463, "results": "37", "hashOfConfig": "38"}, {"size": 931, "mtime": 1752745250835, "results": "39", "hashOfConfig": "38"}, {"size": 362, "mtime": 1752743718231, "results": "40", "hashOfConfig": "38"}, {"size": 16810, "mtime": 1752755035831, "results": "41", "hashOfConfig": "38"}, {"size": 10550, "mtime": 1752745717131, "results": "42", "hashOfConfig": "38"}, {"size": 14799, "mtime": 1752745778135, "results": "43", "hashOfConfig": "38"}, {"size": 2352, "mtime": 1752745268589, "results": "44", "hashOfConfig": "38"}, {"size": 1139, "mtime": 1752747688516, "results": "45", "hashOfConfig": "38"}, {"size": 2852, "mtime": 1752745603421, "results": "46", "hashOfConfig": "38"}, {"size": 1212, "mtime": 1752745575156, "results": "47", "hashOfConfig": "38"}, {"size": 8664, "mtime": 1752745666834, "results": "48", "hashOfConfig": "38"}, {"size": 664, "mtime": 1752745559823, "results": "49", "hashOfConfig": "38"}, {"size": 4557, "mtime": 1752745499332, "results": "50", "hashOfConfig": "38"}, {"size": 7348, "mtime": 1752745539496, "results": "51", "hashOfConfig": "38"}, {"size": 1343, "mtime": 1752745437409, "results": "52", "hashOfConfig": "38"}, {"size": 5505, "mtime": 1752745312884, "results": "53", "hashOfConfig": "38"}, {"size": 8300, "mtime": 1752745367796, "results": "54", "hashOfConfig": "38"}, {"size": 6950, "mtime": 1752745174942, "results": "55", "hashOfConfig": "38"}, {"size": 3402, "mtime": 1752745207658, "results": "56", "hashOfConfig": "38"}, {"size": 582, "mtime": 1752754083463, "results": "57", "hashOfConfig": "58"}, {"size": 362, "mtime": 1752743718231, "results": "59", "hashOfConfig": "58"}, {"size": 931, "mtime": 1752745250835, "results": "60", "hashOfConfig": "58"}, {"size": 16918, "mtime": 1752755330585, "results": "61", "hashOfConfig": "58"}, {"size": 14799, "mtime": 1752745778135, "results": "62", "hashOfConfig": "58"}, {"size": 1139, "mtime": 1752747688516, "results": "63", "hashOfConfig": "58"}, {"size": 10550, "mtime": 1752745717131, "results": "64", "hashOfConfig": "58"}, {"size": 2352, "mtime": 1752745268589, "results": "65", "hashOfConfig": "58"}, {"size": 2852, "mtime": 1752745603421, "results": "66", "hashOfConfig": "58"}, {"size": 4557, "mtime": 1752745499332, "results": "67", "hashOfConfig": "58"}, {"size": 664, "mtime": 1752745559823, "results": "68", "hashOfConfig": "58"}, {"size": 1212, "mtime": 1752745575156, "results": "69", "hashOfConfig": "58"}, {"size": 5505, "mtime": 1752745312884, "results": "70", "hashOfConfig": "58"}, {"size": 8664, "mtime": 1752745666834, "results": "71", "hashOfConfig": "58"}, {"size": 8300, "mtime": 1752745367796, "results": "72", "hashOfConfig": "58"}, {"size": 6950, "mtime": 1752745174942, "results": "73", "hashOfConfig": "58"}, {"size": 3402, "mtime": 1752745207658, "results": "74", "hashOfConfig": "58"}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1extrba", {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ge4x1l", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "c:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["183"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["184"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["185"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js", ["186", "187"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["188", "189"], [], "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["190", "191"], [], "C:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["192"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["193"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js", ["194", "195"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["196"], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["197", "198"], [], "C:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["199", "200"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "201", "line": 238, "column": 38, "nodeType": null}, {"ruleId": "202", "severity": 1, "message": "203", "line": 36, "column": 6, "nodeType": "204", "endLine": 36, "endColumn": 8, "suggestions": "205"}, {"ruleId": "202", "severity": 1, "message": "206", "line": 30, "column": 6, "nodeType": "204", "endLine": 30, "endColumn": 20, "suggestions": "207"}, {"ruleId": "208", "severity": 1, "message": "209", "line": 7, "column": 19, "nodeType": "210", "messageId": "211", "endLine": 7, "endColumn": 29}, {"ruleId": "208", "severity": 1, "message": "212", "line": 8, "column": 17, "nodeType": "210", "messageId": "211", "endLine": 8, "endColumn": 25}, {"ruleId": "208", "severity": 1, "message": "213", "line": 8, "column": 10, "nodeType": "210", "messageId": "211", "endLine": 8, "endColumn": 24}, {"ruleId": "208", "severity": 1, "message": "214", "line": 8, "column": 26, "nodeType": "210", "messageId": "211", "endLine": 8, "endColumn": 43}, {"ruleId": "202", "severity": 1, "message": "215", "line": 8, "column": 9, "nodeType": "216", "endLine": 13, "endColumn": 4}, {"ruleId": "202", "severity": 1, "message": "217", "line": 8, "column": 9, "nodeType": "216", "endLine": 13, "endColumn": 4}, {"ruleId": null, "fatal": true, "severity": 2, "message": "218", "line": 428, "column": 10, "nodeType": null}, {"ruleId": "202", "severity": 1, "message": "206", "line": 30, "column": 6, "nodeType": "204", "endLine": 30, "endColumn": 20, "suggestions": "219"}, {"ruleId": "208", "severity": 1, "message": "209", "line": 7, "column": 19, "nodeType": "210", "messageId": "211", "endLine": 7, "endColumn": 29}, {"ruleId": "208", "severity": 1, "message": "212", "line": 8, "column": 17, "nodeType": "210", "messageId": "211", "endLine": 8, "endColumn": 25}, {"ruleId": "202", "severity": 1, "message": "203", "line": 36, "column": 6, "nodeType": "204", "endLine": 36, "endColumn": 8, "suggestions": "220"}, {"ruleId": "208", "severity": 1, "message": "213", "line": 8, "column": 10, "nodeType": "210", "messageId": "211", "endLine": 8, "endColumn": 24}, {"ruleId": "208", "severity": 1, "message": "214", "line": 8, "column": 26, "nodeType": "210", "messageId": "211", "endLine": 8, "endColumn": 43}, {"ruleId": "202", "severity": 1, "message": "215", "line": 8, "column": 9, "nodeType": "216", "endLine": 13, "endColumn": 4}, {"ruleId": "202", "severity": 1, "message": "217", "line": 8, "column": 9, "nodeType": "216", "endLine": 13, "endColumn": 4}, "Parsing error: Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>? (238:38)", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadStores'. Either include it or remove the dependency array.", "ArrayExpression", ["221"], "React Hook useEffect has a missing dependency: 'loadProductData'. Either include it or remove the dependency array.", ["222"], "no-unused-vars", "'setLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setError' is assigned a value but never used.", "'manualLocation' is assigned a value but never used.", "'setManualLocation' is assigned a value but never used.", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 58) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", "VariableDeclarator", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 105) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", "Parsing error: Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>? (428:10)", ["223"], ["224"], {"desc": "225", "fix": "226"}, {"desc": "227", "fix": "228"}, {"desc": "227", "fix": "229"}, {"desc": "225", "fix": "230"}, "Update the dependencies array to be: [loadStores]", {"range": "231", "text": "232"}, "Update the dependencies array to be: [id, loadProductData, location]", {"range": "233", "text": "234"}, {"range": "235", "text": "234"}, {"range": "236", "text": "232"}, [1114, 1116], "[loadStores]", [1151, 1165], "[id, loadProductData, location]", [1151, 1165], [1114, 1116]]