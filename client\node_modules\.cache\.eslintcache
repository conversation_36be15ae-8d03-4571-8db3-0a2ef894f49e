[{"c:\\laragon\\www\\basketcase\\client\\src\\index.js": "1", "c:\\laragon\\www\\basketcase\\client\\src\\App.js": "2", "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "3", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "4", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "5", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "6", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "7", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js": "8", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "9", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "10", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "11", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "12", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "13", "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js": "14", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js": "15", "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "16", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "17", "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "18", "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "19", "C:\\laragon\\www\\basketcase\\client\\src\\index.js": "20", "C:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "21", "C:\\laragon\\www\\basketcase\\client\\src\\App.js": "22", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "23", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "24", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js": "25", "C:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "26", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "27", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "28", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "29", "C:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "30", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "31", "C:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "32", "C:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "33", "C:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "34", "C:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "35", "C:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "36", "C:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js": "37", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js": "38", "C:\\laragon\\www\\basketcase\\client\\src\\components\\ScrapingPanel.js": "39"}, {"size": 582, "mtime": 1752754083463, "results": "40", "hashOfConfig": "41"}, {"size": 931, "mtime": 1752745250835, "results": "42", "hashOfConfig": "41"}, {"size": 362, "mtime": 1752743718231, "results": "43", "hashOfConfig": "41"}, {"size": 16810, "mtime": 1752755035831, "results": "44", "hashOfConfig": "41"}, {"size": 10550, "mtime": 1752745717131, "results": "45", "hashOfConfig": "41"}, {"size": 14799, "mtime": 1752745778135, "results": "46", "hashOfConfig": "41"}, {"size": 2352, "mtime": 1752745268589, "results": "47", "hashOfConfig": "41"}, {"size": 1139, "mtime": 1752747688516, "results": "48", "hashOfConfig": "41"}, {"size": 2852, "mtime": 1752745603421, "results": "49", "hashOfConfig": "41"}, {"size": 1212, "mtime": 1752745575156, "results": "50", "hashOfConfig": "41"}, {"size": 8664, "mtime": 1752745666834, "results": "51", "hashOfConfig": "41"}, {"size": 664, "mtime": 1752745559823, "results": "52", "hashOfConfig": "41"}, {"size": 4557, "mtime": 1752745499332, "results": "53", "hashOfConfig": "41"}, {"size": 7348, "mtime": 1752745539496, "results": "54", "hashOfConfig": "41"}, {"size": 1343, "mtime": 1752745437409, "results": "55", "hashOfConfig": "41"}, {"size": 5505, "mtime": 1752745312884, "results": "56", "hashOfConfig": "41"}, {"size": 8300, "mtime": 1752745367796, "results": "57", "hashOfConfig": "41"}, {"size": 6950, "mtime": 1752745174942, "results": "58", "hashOfConfig": "41"}, {"size": 3402, "mtime": 1752745207658, "results": "59", "hashOfConfig": "41"}, {"size": 582, "mtime": 1752754083463, "results": "60", "hashOfConfig": "61"}, {"size": 362, "mtime": 1752743718231, "results": "62", "hashOfConfig": "61"}, {"size": 931, "mtime": 1752745250835, "results": "63", "hashOfConfig": "61"}, {"size": 11291, "mtime": 1752760684167, "results": "64", "hashOfConfig": "61"}, {"size": 14799, "mtime": 1752745778135, "results": "65", "hashOfConfig": "61"}, {"size": 1139, "mtime": 1752747688516, "results": "66", "hashOfConfig": "61"}, {"size": 10550, "mtime": 1752745717131, "results": "67", "hashOfConfig": "61"}, {"size": 2352, "mtime": 1752745268589, "results": "68", "hashOfConfig": "61"}, {"size": 2852, "mtime": 1752745603421, "results": "69", "hashOfConfig": "61"}, {"size": 4557, "mtime": 1752745499332, "results": "70", "hashOfConfig": "61"}, {"size": 664, "mtime": 1752745559823, "results": "71", "hashOfConfig": "61"}, {"size": 1212, "mtime": 1752745575156, "results": "72", "hashOfConfig": "61"}, {"size": 5505, "mtime": 1752745312884, "results": "73", "hashOfConfig": "61"}, {"size": 8664, "mtime": 1752745666834, "results": "74", "hashOfConfig": "61"}, {"size": 8300, "mtime": 1752745367796, "results": "75", "hashOfConfig": "61"}, {"size": 6950, "mtime": 1752745174942, "results": "76", "hashOfConfig": "61"}, {"size": 3402, "mtime": 1752745207658, "results": "77", "hashOfConfig": "61"}, {"size": 7348, "mtime": 1752745539496, "results": "78", "hashOfConfig": "61"}, {"size": 1343, "mtime": 1752745437409, "results": "79", "hashOfConfig": "61"}, {"size": 7086, "mtime": 1752760541653, "results": "80", "hashOfConfig": "61"}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1extrba", {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ge4x1l", {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["198"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["199"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["200"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js", ["201", "202"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["203", "204"], [], "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["205", "206"], [], "C:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["207", "208"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["209"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\ComparePage.js", ["210", "211"], [], "C:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["212"], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["213", "214"], [], "C:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["215", "216"], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js", [], [], "C:\\laragon\\www\\basketcase\\client\\src\\components\\ScrapingPanel.js", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "217", "line": 238, "column": 38, "nodeType": null}, {"ruleId": "218", "severity": 1, "message": "219", "line": 36, "column": 6, "nodeType": "220", "endLine": 36, "endColumn": 8, "suggestions": "221"}, {"ruleId": "218", "severity": 1, "message": "222", "line": 30, "column": 6, "nodeType": "220", "endLine": 30, "endColumn": 20, "suggestions": "223"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 7, "column": 19, "nodeType": "226", "messageId": "227", "endLine": 7, "endColumn": 29}, {"ruleId": "224", "severity": 1, "message": "228", "line": 8, "column": 17, "nodeType": "226", "messageId": "227", "endLine": 8, "endColumn": 25}, {"ruleId": "224", "severity": 1, "message": "229", "line": 8, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 8, "endColumn": 24}, {"ruleId": "224", "severity": 1, "message": "230", "line": 8, "column": 26, "nodeType": "226", "messageId": "227", "endLine": 8, "endColumn": 43}, {"ruleId": "218", "severity": 1, "message": "231", "line": 8, "column": 9, "nodeType": "232", "endLine": 13, "endColumn": 4}, {"ruleId": "218", "severity": 1, "message": "233", "line": 8, "column": 9, "nodeType": "232", "endLine": 13, "endColumn": 4}, {"ruleId": "218", "severity": 1, "message": "234", "line": 94, "column": 6, "nodeType": "220", "endLine": 94, "endColumn": 8, "suggestions": "235"}, {"ruleId": "218", "severity": 1, "message": "234", "line": 98, "column": 6, "nodeType": "220", "endLine": 98, "endColumn": 15, "suggestions": "236"}, {"ruleId": "218", "severity": 1, "message": "222", "line": 30, "column": 6, "nodeType": "220", "endLine": 30, "endColumn": 20, "suggestions": "237"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 7, "column": 19, "nodeType": "226", "messageId": "227", "endLine": 7, "endColumn": 29}, {"ruleId": "224", "severity": 1, "message": "228", "line": 8, "column": 17, "nodeType": "226", "messageId": "227", "endLine": 8, "endColumn": 25}, {"ruleId": "218", "severity": 1, "message": "219", "line": 36, "column": 6, "nodeType": "220", "endLine": 36, "endColumn": 8, "suggestions": "238"}, {"ruleId": "224", "severity": 1, "message": "229", "line": 8, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 8, "endColumn": 24}, {"ruleId": "224", "severity": 1, "message": "230", "line": 8, "column": 26, "nodeType": "226", "messageId": "227", "endLine": 8, "endColumn": 43}, {"ruleId": "218", "severity": 1, "message": "231", "line": 8, "column": 9, "nodeType": "232", "endLine": 13, "endColumn": 4}, {"ruleId": "218", "severity": 1, "message": "233", "line": 8, "column": 9, "nodeType": "232", "endLine": 13, "endColumn": 4}, "Parsing error: Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>? (238:38)", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadStores'. Either include it or remove the dependency array.", "ArrayExpression", ["239"], "React Hook useEffect has a missing dependency: 'loadProductData'. Either include it or remove the dependency array.", ["240"], "no-unused-vars", "'setLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setError' is assigned a value but never used.", "'manualLocation' is assigned a value but never used.", "'setManualLocation' is assigned a value but never used.", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 58) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", "VariableDeclarator", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 105) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["241"], ["242"], ["243"], ["244"], {"desc": "245", "fix": "246"}, {"desc": "247", "fix": "248"}, {"desc": "249", "fix": "250"}, {"desc": "251", "fix": "252"}, {"desc": "247", "fix": "253"}, {"desc": "245", "fix": "254"}, "Update the dependencies array to be: [loadStores]", {"range": "255", "text": "256"}, "Update the dependencies array to be: [id, loadProductData, location]", {"range": "257", "text": "258"}, "Update the dependencies array to be: [loadProducts]", {"range": "259", "text": "260"}, "Update the dependencies array to be: [filters, loadProducts]", {"range": "261", "text": "262"}, {"range": "263", "text": "258"}, {"range": "264", "text": "256"}, [1114, 1116], "[loadStores]", [1151, 1165], "[id, loadProductData, location]", [2842, 2844], "[loadProducts]", [2894, 2903], "[filters, loadProducts]", [1151, 1165], [1114, 1116]]