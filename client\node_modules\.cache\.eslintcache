[{"c:\\laragon\\www\\basketcase\\client\\src\\index.js": "1", "c:\\laragon\\www\\basketcase\\client\\src\\App.js": "2", "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js": "3", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js": "4", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js": "5", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js": "6", "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js": "7", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js": "8", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js": "9", "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js": "10", "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js": "11", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js": "12", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js": "13", "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js": "14", "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js": "15", "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js": "16", "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js": "17", "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js": "18"}, {"size": 535, "mtime": 1752743717447, "results": "19", "hashOfConfig": "20"}, {"size": 931, "mtime": 1752745250835, "results": "21", "hashOfConfig": "20"}, {"size": 362, "mtime": 1752743718231, "results": "22", "hashOfConfig": "20"}, {"size": 14799, "mtime": 1752745778135, "results": "23", "hashOfConfig": "20"}, {"size": 9603, "mtime": 1752745422647, "results": "24", "hashOfConfig": "20"}, {"size": 2352, "mtime": 1752745268589, "results": "25", "hashOfConfig": "20"}, {"size": 10550, "mtime": 1752745717131, "results": "26", "hashOfConfig": "20"}, {"size": 2852, "mtime": 1752745603421, "results": "27", "hashOfConfig": "20"}, {"size": 4557, "mtime": 1752745499332, "results": "28", "hashOfConfig": "20"}, {"size": 8664, "mtime": 1752745666834, "results": "29", "hashOfConfig": "20"}, {"size": 7348, "mtime": 1752745539496, "results": "30", "hashOfConfig": "20"}, {"size": 664, "mtime": 1752745559823, "results": "31", "hashOfConfig": "20"}, {"size": 1212, "mtime": 1752745575156, "results": "32", "hashOfConfig": "20"}, {"size": 1343, "mtime": 1752745437409, "results": "33", "hashOfConfig": "20"}, {"size": 5505, "mtime": 1752745312884, "results": "34", "hashOfConfig": "20"}, {"size": 8300, "mtime": 1752745367796, "results": "35", "hashOfConfig": "20"}, {"size": 6950, "mtime": 1752745174942, "results": "36", "hashOfConfig": "20"}, {"size": 3402, "mtime": 1752745207658, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1extrba", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "c:\\laragon\\www\\basketcase\\client\\src\\index.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\App.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\reportWebVitals.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\ProductPage.js", ["92"], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\HomePage.js", ["93"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Header.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\pages\\StoresPage.js", ["94"], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Footer.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductCard.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\Map.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\FilterPanel.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LoadingSpinner.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ErrorMessage.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\ProductGrid.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\SearchBar.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\components\\LocationSelector.js", ["95", "96"], [], "c:\\laragon\\www\\basketcase\\client\\src\\services\\api.js", [], [], "c:\\laragon\\www\\basketcase\\client\\src\\hooks\\useGeolocation.js", ["97", "98"], [], {"ruleId": "99", "severity": 1, "message": "100", "line": 30, "column": 6, "nodeType": "101", "endLine": 30, "endColumn": 20, "suggestions": "102"}, {"ruleId": "99", "severity": 1, "message": "103", "line": 96, "column": 6, "nodeType": "101", "endLine": 96, "endColumn": 15, "suggestions": "104"}, {"ruleId": "99", "severity": 1, "message": "105", "line": 36, "column": 6, "nodeType": "101", "endLine": 36, "endColumn": 8, "suggestions": "106"}, {"ruleId": "107", "severity": 1, "message": "108", "line": 8, "column": 10, "nodeType": "109", "messageId": "110", "endLine": 8, "endColumn": 24}, {"ruleId": "107", "severity": 1, "message": "111", "line": 8, "column": 26, "nodeType": "109", "messageId": "110", "endLine": 8, "endColumn": 43}, {"ruleId": "99", "severity": 1, "message": "112", "line": 8, "column": 9, "nodeType": "113", "endLine": 13, "endColumn": 4}, {"ruleId": "99", "severity": 1, "message": "114", "line": 8, "column": 9, "nodeType": "113", "endLine": 13, "endColumn": 4}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadProductData'. Either include it or remove the dependency array.", "ArrayExpression", ["115"], "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["116"], "React Hook useEffect has a missing dependency: 'loadStores'. Either include it or remove the dependency array.", ["117"], "no-unused-vars", "'manualLocation' is assigned a value but never used.", "Identifier", "unusedVar", "'setManualLocation' is assigned a value but never used.", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 58) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", "VariableDeclarator", "The 'defaultOptions' object makes the dependencies of useCallback Hook (at line 105) change on every render. To fix this, wrap the initialization of 'defaultOptions' in its own useMemo() Hook.", {"desc": "118", "fix": "119"}, {"desc": "120", "fix": "121"}, {"desc": "122", "fix": "123"}, "Update the dependencies array to be: [id, loadProductData, location]", {"range": "124", "text": "125"}, "Update the dependencies array to be: [filters, loadProducts]", {"range": "126", "text": "127"}, "Update the dependencies array to be: [loadStores]", {"range": "128", "text": "129"}, [1151, 1165], "[id, loadProductData, location]", [2939, 2948], "[filters, loadProducts]", [1114, 1116], "[loadStores]"]