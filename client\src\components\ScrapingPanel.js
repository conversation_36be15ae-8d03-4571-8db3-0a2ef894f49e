import React, { useState, useEffect } from 'react';
import './ScrapingPanel.css';

const ScrapingPanel = () => {
  const [scrapingStatus, setScrapingStatus] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [lastResults, setLastResults] = useState(null);
  const [loading, setLoading] = useState(false);

  // Load scraping status
  const loadScrapingStatus = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/scraping/status');
      const data = await response.json();
      setScrapingStatus(data.status);
    } catch (error) {
      console.error('Error loading scraping status:', error);
    }
  };

  // Run full scraping
  const runFullScraping = async () => {
    try {
      setLoading(true);
      setIsRunning(true);
      
      const response = await fetch('http://localhost:5000/api/scraping/run', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      setLastResults(data.results);
      
      // Refresh status after scraping
      setTimeout(() => {
        loadScrapingStatus();
        setIsRunning(false);
      }, 2000);
      
    } catch (error) {
      console.error('Error running scraping:', error);
      setIsRunning(false);
    } finally {
      setLoading(false);
    }
  };

  // Scrape specific store
  const scrapeStore = async (storeName) => {
    try {
      setLoading(true);
      
      const response = await fetch(`http://localhost:5000/api/scraping/store/${storeName}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ limit: 20 })
      });
      
      const data = await response.json();
      setLastResults(data);
      
      // Refresh status
      setTimeout(() => {
        loadScrapingStatus();
      }, 1000);
      
    } catch (error) {
      console.error(`Error scraping ${storeName}:`, error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadScrapingStatus();
    // Auto-refresh status every 30 seconds
    const interval = setInterval(loadScrapingStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="scraping-panel">
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="mb-0">🔄 Live Price Scraping</h5>
          <span className={`badge ${isRunning ? 'bg-warning' : 'bg-success'}`}>
            {isRunning ? 'Running...' : 'Ready'}
          </span>
        </div>
        
        <div className="card-body">
          {/* Scraping Status */}
          {scrapingStatus && (
            <div className="scraping-status mb-3">
              <div className="row">
                <div className="col-md-6">
                  <small className="text-muted">Available Scrapers:</small>
                  <div className="d-flex flex-wrap gap-1 mt-1">
                    {scrapingStatus.availableScrapers?.map(scraper => (
                      <span key={scraper} className="badge bg-primary">{scraper.toUpperCase()}</span>
                    ))}
                  </div>
                </div>
                <div className="col-md-6">
                  <small className="text-muted">Last Run:</small>
                  <div className="small">
                    {scrapingStatus.lastRun ? new Date(scrapingStatus.lastRun).toLocaleString() : 'Never'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Control Buttons */}
          <div className="scraping-controls mb-3">
            <div className="row">
              <div className="col-md-6">
                <button 
                  className="btn btn-primary w-100 mb-2"
                  onClick={runFullScraping}
                  disabled={loading || isRunning}
                >
                  {loading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                      Scraping All Stores...
                    </>
                  ) : (
                    <>🚀 Run Full Scraping</>
                  )}
                </button>
              </div>
              <div className="col-md-6">
                <button 
                  className="btn btn-outline-primary w-100 mb-2"
                  onClick={() => loadScrapingStatus()}
                  disabled={loading}
                >
                  🔄 Refresh Status
                </button>
              </div>
            </div>
            
            {/* Individual Store Buttons */}
            <div className="row">
              {scrapingStatus?.availableScrapers?.slice(0, 4).map(store => (
                <div key={store} className="col-6 col-md-3 mb-2">
                  <button 
                    className="btn btn-outline-secondary btn-sm w-100"
                    onClick={() => scrapeStore(store)}
                    disabled={loading || isRunning}
                  >
                    {store.toUpperCase()}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Results Display */}
          {lastResults && (
            <div className="scraping-results">
              <h6 className="text-success">✅ Latest Scraping Results:</h6>
              <div className="alert alert-success">
                <div className="row">
                  <div className="col-6">
                    <strong>Products Scraped:</strong> {lastResults.totalProducts || lastResults.productsScraped || 0}
                  </div>
                  <div className="col-6">
                    <strong>Store:</strong> {lastResults.store || 'Multiple Stores'}
                  </div>
                </div>
                {lastResults.stores && (
                  <div className="mt-2">
                    <strong>Stores Updated:</strong>
                    <div className="d-flex flex-wrap gap-1 mt-1">
                      {lastResults.stores.map((store, index) => (
                        <span key={index} className={`badge ${store.success ? 'bg-success' : 'bg-danger'}`}>
                          {store.name.toUpperCase()} ({store.productsScraped})
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                <small className="text-muted d-block mt-2">
                  Completed at: {new Date().toLocaleTimeString()}
                </small>
              </div>
            </div>
          )}

          {/* Info */}
          <div className="scraping-info">
            <small className="text-muted">
              💡 Our system automatically scrapes prices every 24 hours. 
              Use the controls above to trigger manual updates for the latest deals.
            </small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScrapingPanel;
