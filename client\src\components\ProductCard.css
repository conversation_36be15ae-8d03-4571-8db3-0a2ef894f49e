/* Product Card Styles */
.product-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background-color: var(--secondary-color);
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.product-image.loading {
  opacity: 0;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--secondary-color);
  color: #6c757d;
}

.placeholder-icon {
  font-size: 3rem;
  opacity: 0.5;
}

.image-skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.category-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.product-info {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.product-brand {
  font-size: 0.8rem;
  color: var(--primary-color);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dark-color);
  line-height: 1.3;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-description {
  font-size: 0.85rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.product-tag {
  background-color: var(--secondary-color);
  color: var(--dark-color);
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.product-tag.more {
  background-color: var(--info-color);
  color: white;
}

.product-actions {
  padding: 0 1rem 1rem;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.view-prices-button {
  flex: 1;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.view-prices-button:hover {
  background-color: #1e3d72;
}

.compare-button {
  background-color: var(--secondary-color);
  color: var(--dark-color);
  border: 1px solid var(--border-color);
  padding: 0.75rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
}

.compare-button:hover {
  background-color: #e9ecef;
  border-color: var(--primary-color);
}

.price-stats {
  padding: 0 1rem 1rem;
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.min-price {
  font-weight: 600;
  color: var(--accent-color);
}

.max-price {
  font-size: 0.85rem;
  color: #6c757d;
}

.store-count {
  font-size: 0.8rem;
  color: #6c757d;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .product-image-container {
    height: 150px;
  }
  
  .product-info {
    padding: 0.75rem;
  }
  
  .product-actions {
    padding: 0 0.75rem 0.75rem;
  }
}
