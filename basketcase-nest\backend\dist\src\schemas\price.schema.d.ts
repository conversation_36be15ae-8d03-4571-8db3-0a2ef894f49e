import { Document, Types } from 'mongoose';
export type PriceDocument = Price & Document;
export declare class Price {
    product: Types.ObjectId;
    store: Types.ObjectId;
    current: number;
    original?: number;
    currency: string;
    availability?: {
        inStock: boolean;
        stockLevel?: string;
        lastChecked?: Date;
    };
    promotion?: {
        isOnPromotion: boolean;
        promotionDescription?: string;
        promotionType?: string;
        validFrom?: Date;
        validUntil?: Date;
    };
    priceHistory?: Array<{
        price: number;
        date: Date;
        source?: string;
    }>;
    scrapingInfo?: {
        lastScraped?: Date;
        sourceUrl?: string;
        scrapingMethod?: string;
        confidence?: number;
    };
    isActive: boolean;
}
export declare const PriceSchema: import("mongoose").Schema<Price, import("mongoose").Model<Price, any, any, any, Document<unknown, any, Price> & Price & {
    _id: Types.ObjectId;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Price, Document<unknown, {}, import("mongoose").FlatRecord<Price>> & import("mongoose").FlatRecord<Price> & {
    _id: Types.ObjectId;
}>;
