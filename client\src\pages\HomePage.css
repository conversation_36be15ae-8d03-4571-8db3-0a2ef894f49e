/* Home Page Styles */
.home-page {
  min-height: calc(100vh - 80px);
}

.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 3rem 0;
  background: linear-gradient(135deg, #ff0000 0%, #00ff00 100%);
  color: white;
  margin: -80px calc(-50vw + 50%) 2rem calc(-50vw + 50%);
  padding-top: calc(3rem + 80px);
  width: 100vw;
  position: relative;
  border: 5px solid red;
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 0;
}

/* Main Content Layout */
.main-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 2rem;
  align-items: start;
}

.filters-sidebar {
  position: sticky;
  top: 100px;
}

.products-section {
  min-height: 500px;
}

/* Search Results Header */
.search-results-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.results-title {
  margin: 0 0 0.5rem 0;
  color: var(--dark-color);
  font-size: 1.75rem;
}

.results-info {
  color: #6c757d;
  font-size: 0.9rem;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.no-results h3 {
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.no-results p {
  margin-bottom: 1.5rem;
}

.no-results button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.no-results button:hover {
  background-color: #1e3d72;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1rem 0;
}

.pagination-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.pagination-button:hover:not(:disabled) {
  background-color: #1e3d72;
  transform: translateY(-1px);
}

.pagination-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.pagination-info {
  color: var(--dark-color);
  font-weight: 500;
}

/* Featured Sections */
.featured-sections {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.featured-section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Biggest Savings Section - Full Width at Top */
.biggest-savings-section {
  margin-bottom: 4rem;
}

/* Bottom Sections - Side by Side Layout */
.bottom-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.trending-section,
.promotions-section {
  margin-bottom: 0;
}

/* Biggest Savings */
.savings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.savings-item {
  background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
  border: 2px solid var(--danger-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  position: relative;
  transition: var(--transition);
  overflow: hidden;
}

.savings-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.savings-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--danger-color) 0%, var(--warning-color) 100%);
}

.savings-badge {
  background: var(--danger-color);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 1.1rem;
  text-align: center;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.savings-percentage {
  display: block;
  font-size: 0.85rem;
  font-weight: 500;
  opacity: 0.9;
  margin-top: 0.25rem;
}

.savings-product {
  margin-bottom: 1rem;
}

.savings-product-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.savings-brand {
  font-size: 0.85rem;
  color: var(--primary-color);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.savings-store {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.savings-prices {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.savings-current-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--accent-color);
}

.savings-original-price {
  font-size: 1rem;
  color: #6c757d;
  text-decoration: line-through;
}

.view-deal-button {
  width: 100%;
  background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.view-deal-button:hover {
  background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Trending Products */
.trending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.trending-item {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.trending-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.trending-product-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0 0 1rem 0;
}

.trending-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.avg-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--accent-color);
}

.price-range {
  font-size: 0.9rem;
  color: #6c757d;
}

/* Promotions */
.promotions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.promotion-item {
  background: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border-left: 4px solid var(--warning-color);
  transition: var(--transition);
}

.promotion-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.promotion-product {
  margin-bottom: 0.75rem;
}

.promotion-product h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0 0 0.25rem 0;
}

.promotion-store {
  font-size: 0.85rem;
  color: var(--primary-color);
  font-weight: 500;
}

.promotion-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.current-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--accent-color);
}

.original-price {
  font-size: 0.9rem;
  color: #6c757d;
  text-decoration: line-through;
}

.promotion-description {
  font-size: 0.8rem;
  color: var(--warning-color);
  font-weight: 500;
  background-color: rgba(255, 193, 7, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .hero-section {
    margin: -80px calc(-50vw + 50%) 1rem calc(-50vw + 50%);
    padding: 2rem 1rem;
    padding-top: calc(2rem + 80px);
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .main-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filters-sidebar {
    position: static;
    order: -1;
  }

  .bottom-sections {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .savings-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .trending-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .promotions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .pagination-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .home-container {
    padding: 0 0.5rem;
  }
  
  .hero-section {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  .hero-title {
    font-size: 1.75rem;
  }
  
  .trending-item,
  .promotion-item {
    padding: 1rem;
  }
}
