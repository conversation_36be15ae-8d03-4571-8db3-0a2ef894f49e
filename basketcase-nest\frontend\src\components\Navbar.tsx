import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar: React.FC = () => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path ? 'nav-link active' : 'nav-link';
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
      <div className="container">
        <Link className="navbar-brand" to="/">
          <i className="fas fa-shopping-basket me-2"></i>
          BasketCase
        </Link>

        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav me-auto">
            <li className="nav-item">
              <Link className={isActive('/')} to="/">
                <i className="fas fa-home me-1"></i>
                Home
              </Link>
            </li>
            <li className="nav-item">
              <Link className={isActive('/products')} to="/products">
                <i className="fas fa-box me-1"></i>
                Products
              </Link>
            </li>
            <li className="nav-item">
              <Link className={isActive('/stores')} to="/stores">
                <i className="fas fa-store me-1"></i>
                Stores
              </Link>
            </li>
            <li className="nav-item">
              <Link className={isActive('/compare')} to="/compare">
                <i className="fas fa-balance-scale me-1"></i>
                Compare
              </Link>
            </li>
            <li className="nav-item">
              <Link className={isActive('/about')} to="/about">
                <i className="fas fa-info-circle me-1"></i>
                About
              </Link>
            </li>
          </ul>

          <ul className="navbar-nav">
            <li className="nav-item">
              <span className="navbar-text">
                <i className="fas fa-clock me-1"></i>
                Live Price Updates
              </span>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
