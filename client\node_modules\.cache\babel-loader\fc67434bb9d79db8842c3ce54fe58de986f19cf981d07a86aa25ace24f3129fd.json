{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\components\\\\ErrorMessage.js\";\nimport React from 'react';\nimport './ErrorMessage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorMessage = ({\n  message = 'Something went wrong',\n  onRetry = null,\n  type = 'error',\n  // 'error', 'warning', 'info'\n  showIcon = true\n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n        return '⚠️';\n      case 'info':\n        return 'ℹ️';\n      case 'error':\n      default:\n        return '❌';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `error-message ${type}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-content\",\n      children: [showIcon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-icon\",\n        children: getIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-text\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-title\",\n          children: type === 'warning' ? 'Warning' : type === 'info' ? 'Information' : 'Error'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-description\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), onRetry && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-actions\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"retry-button\",\n        onClick: onRetry,\n        children: \"Try Again\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_c = ErrorMessage;\nexport default ErrorMessage;\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ErrorMessage", "message", "onRetry", "type", "showIcon", "getIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/client/src/components/ErrorMessage.js"], "sourcesContent": ["import React from 'react';\nimport './ErrorMessage.css';\n\nconst ErrorMessage = ({ \n  message = 'Something went wrong', \n  onRetry = null,\n  type = 'error', // 'error', 'warning', 'info'\n  showIcon = true \n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n        return '⚠️';\n      case 'info':\n        return 'ℹ️';\n      case 'error':\n      default:\n        return '❌';\n    }\n  };\n\n  return (\n    <div className={`error-message ${type}`}>\n      <div className=\"error-content\">\n        {showIcon && (\n          <div className=\"error-icon\">\n            {getIcon()}\n          </div>\n        )}\n        \n        <div className=\"error-text\">\n          <div className=\"error-title\">\n            {type === 'warning' ? 'Warning' : type === 'info' ? 'Information' : 'Error'}\n          </div>\n          <div className=\"error-description\">\n            {message}\n          </div>\n        </div>\n      </div>\n      \n      {onRetry && (\n        <div className=\"error-actions\">\n          <button \n            className=\"retry-button\"\n            onClick={onRetry}\n          >\n            Try Again\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ErrorMessage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EACpBC,OAAO,GAAG,sBAAsB;EAChCC,OAAO,GAAG,IAAI;EACdC,IAAI,GAAG,OAAO;EAAE;EAChBC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQF,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,IAAI;MACb,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,OAAO;MACZ;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKO,SAAS,EAAE,iBAAiBH,IAAI,EAAG;IAAAI,QAAA,gBACtCR,OAAA;MAAKO,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BH,QAAQ,iBACPL,OAAA;QAAKO,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBF,OAAO,CAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN,eAEDZ,OAAA;QAAKO,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBR,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBJ,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGA,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACNZ,OAAA;UAAKO,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/BN;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELT,OAAO,iBACNH,OAAA;MAAKO,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BR,OAAA;QACEO,SAAS,EAAC,cAAc;QACxBM,OAAO,EAAEV,OAAQ;QAAAK,QAAA,EAClB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACE,EAAA,GAjDIb,YAAY;AAmDlB,eAAeA,YAAY;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}