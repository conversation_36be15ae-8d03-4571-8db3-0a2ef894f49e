{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\basketcase-nest\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-detail-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"page-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-box me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this), \"Product Details\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: [\"Product ID: \", id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-info-circle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), \"Product detail page is under development. This will show detailed product information and price comparisons across stores.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"yQgCIz/jJfqV1l9s2yoba81MT5A=\", false, function () {\n  return [useParams];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useParams", "jsxDEV", "_jsxDEV", "ProductDetailPage", "_s", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/basketcase-nest/frontend/src/pages/ProductDetailPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\n\nconst ProductDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n\n  return (\n    <div className=\"product-detail-page\">\n      <div className=\"container py-4\">\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <h1 className=\"page-title\">\n              <i className=\"fas fa-box me-2\"></i>\n              Product Details\n            </h1>\n            <p className=\"text-muted\">Product ID: {id}</p>\n            \n            <div className=\"alert alert-info\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              Product detail page is under development. This will show detailed product information and price comparisons across stores.\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC;EAAG,CAAC,GAAGL,SAAS,CAAiB,CAAC;EAE1C,oBACEE,OAAA;IAAKI,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClCL,OAAA;MAAKI,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BL,OAAA;QAAKI,SAAS,EAAC,KAAK;QAAAC,QAAA,eAClBL,OAAA;UAAKI,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBL,OAAA;YAAII,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACxBL,OAAA;cAAGI,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLT,OAAA;YAAGI,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,cAAY,EAACF,EAAE;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE9CT,OAAA;YAAKI,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BL,OAAA;cAAGI,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,8HAE7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CAvBID,iBAA2B;EAAA,QAChBH,SAAS;AAAA;AAAAY,EAAA,GADpBT,iBAA2B;AAyBjC,eAAeA,iBAAiB;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}