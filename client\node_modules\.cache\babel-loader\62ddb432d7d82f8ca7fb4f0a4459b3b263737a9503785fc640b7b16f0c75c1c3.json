{"ast": null, "code": "var _jsxFileName = \"c:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\components\\\\ProductCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { apiUtils } from '../services/api';\nimport './ProductCard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product,\n  onClick,\n  showCompareButton = true\n}) => {\n  _s();\n  var _product$images, _product$images2;\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(true);\n\n  // Get primary image\n  const primaryImage = ((_product$images = product.images) === null || _product$images === void 0 ? void 0 : _product$images.find(img => img.isPrimary)) || ((_product$images2 = product.images) === null || _product$images2 === void 0 ? void 0 : _product$images2[0]);\n  const imageUrl = primaryImage === null || primaryImage === void 0 ? void 0 : primaryImage.url;\n\n  // Handle image load error\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n\n  // Handle image load success\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n\n  // Handle card click\n  const handleCardClick = e => {\n    // Don't trigger if clicking on buttons\n    if (e.target.closest('button')) return;\n    onClick(product);\n  };\n\n  // Handle compare button click\n  const handleCompareClick = e => {\n    e.stopPropagation();\n    // TODO: Add to comparison list\n    console.log('Add to compare:', product.name);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-card\",\n    onClick: handleCardClick,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-image-container\",\n      children: [imageUrl && !imageError ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [imageLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-placeholder\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-skeleton\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageUrl,\n          alt: product.name,\n          className: `product-image ${imageLoading ? 'loading' : ''}`,\n          onError: handleImageError,\n          onLoad: handleImageLoad,\n          loading: \"lazy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"image-placeholder\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"placeholder-icon\",\n          children: \"\\uD83D\\uDCE6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"category-badge\",\n        children: product.category\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-info\",\n      children: [product.brand && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-brand\",\n        children: product.brand\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"product-name\",\n        title: product.name,\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), product.description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"product-description\",\n        title: product.description,\n        children: product.description.length > 80 ? `${product.description.substring(0, 80)}...` : product.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this), product.tags && product.tags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-tags\",\n        children: [product.tags.slice(0, 3).map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"product-tag\",\n          children: tag\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 15\n        }, this)), product.tags.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"product-tag more\",\n          children: [\"+\", product.tags.length - 3]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"view-prices-button\",\n        onClick: handleCardClick,\n        \"aria-label\": `View prices for ${product.name}`,\n        children: \"View Prices\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), showCompareButton && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"compare-button\",\n        onClick: handleCompareClick,\n        \"aria-label\": `Add ${product.name} to comparison`,\n        title: \"Add to comparison\",\n        children: \"\\u2696\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), product.priceStats && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"price-stats\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"price-range\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"min-price\",\n          children: [\"From \", apiUtils.formatPrice(product.priceStats.minPrice)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), product.priceStats.maxPrice > product.priceStats.minPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"max-price\",\n          children: [\"to \", apiUtils.formatPrice(product.priceStats.maxPrice)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this), product.priceStats.storeCount && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"store-count\",\n        children: [\"Available at \", product.priceStats.storeCount, \" store\", product.priceStats.storeCount !== 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductCard, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "useState", "apiUtils", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductCard", "product", "onClick", "showCompareButton", "_s", "_product$images", "_product$images2", "imageError", "setImageError", "imageLoading", "setImageLoading", "primaryImage", "images", "find", "img", "isPrimary", "imageUrl", "url", "handleImageError", "handleImageLoad", "handleCardClick", "e", "target", "closest", "handleCompareClick", "stopPropagation", "console", "log", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onError", "onLoad", "loading", "category", "brand", "title", "description", "length", "substring", "tags", "slice", "map", "tag", "index", "priceStats", "formatPrice", "minPrice", "maxPrice", "storeCount", "_c", "$RefreshReg$"], "sources": ["c:/laragon/www/basketcase/client/src/components/ProductCard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { apiUtils } from '../services/api';\nimport './ProductCard.css';\n\nconst ProductCard = ({ product, onClick, showCompareButton = true }) => {\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(true);\n\n  // Get primary image\n  const primaryImage = product.images?.find(img => img.isPrimary) || product.images?.[0];\n  const imageUrl = primaryImage?.url;\n\n  // Handle image load error\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n\n  // Handle image load success\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n\n  // Handle card click\n  const handleCardClick = (e) => {\n    // Don't trigger if clicking on buttons\n    if (e.target.closest('button')) return;\n    onClick(product);\n  };\n\n  // Handle compare button click\n  const handleCompareClick = (e) => {\n    e.stopPropagation();\n    // TODO: Add to comparison list\n    console.log('Add to compare:', product.name);\n  };\n\n  return (\n    <div className=\"product-card\" onClick={handleCardClick}>\n      {/* Product Image */}\n      <div className=\"product-image-container\">\n        {imageUrl && !imageError ? (\n          <>\n            {imageLoading && (\n              <div className=\"image-placeholder\">\n                <div className=\"image-skeleton\"></div>\n              </div>\n            )}\n            <img\n              src={imageUrl}\n              alt={product.name}\n              className={`product-image ${imageLoading ? 'loading' : ''}`}\n              onError={handleImageError}\n              onLoad={handleImageLoad}\n              loading=\"lazy\"\n            />\n          </>\n        ) : (\n          <div className=\"image-placeholder\">\n            <div className=\"placeholder-icon\">📦</div>\n          </div>\n        )}\n        \n        {/* Category Badge */}\n        <div className=\"category-badge\">\n          {product.category}\n        </div>\n      </div>\n\n      {/* Product Info */}\n      <div className=\"product-info\">\n        {/* Brand */}\n        {product.brand && (\n          <div className=\"product-brand\">{product.brand}</div>\n        )}\n\n        {/* Product Name */}\n        <h3 className=\"product-name\" title={product.name}>\n          {product.name}\n        </h3>\n\n        {/* Description */}\n        {product.description && (\n          <p className=\"product-description\" title={product.description}>\n            {product.description.length > 80 \n              ? `${product.description.substring(0, 80)}...` \n              : product.description\n            }\n          </p>\n        )}\n\n        {/* Tags */}\n        {product.tags && product.tags.length > 0 && (\n          <div className=\"product-tags\">\n            {product.tags.slice(0, 3).map((tag, index) => (\n              <span key={index} className=\"product-tag\">\n                {tag}\n              </span>\n            ))}\n            {product.tags.length > 3 && (\n              <span className=\"product-tag more\">\n                +{product.tags.length - 3}\n              </span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Product Actions */}\n      <div className=\"product-actions\">\n        <button \n          className=\"view-prices-button\"\n          onClick={handleCardClick}\n          aria-label={`View prices for ${product.name}`}\n        >\n          View Prices\n        </button>\n        \n        {showCompareButton && (\n          <button \n            className=\"compare-button\"\n            onClick={handleCompareClick}\n            aria-label={`Add ${product.name} to comparison`}\n            title=\"Add to comparison\"\n          >\n            ⚖️\n          </button>\n        )}\n      </div>\n\n      {/* Quick Stats (if available) */}\n      {product.priceStats && (\n        <div className=\"price-stats\">\n          <div className=\"price-range\">\n            <span className=\"min-price\">\n              From {apiUtils.formatPrice(product.priceStats.minPrice)}\n            </span>\n            {product.priceStats.maxPrice > product.priceStats.minPrice && (\n              <span className=\"max-price\">\n                to {apiUtils.formatPrice(product.priceStats.maxPrice)}\n              </span>\n            )}\n          </div>\n          \n          {product.priceStats.storeCount && (\n            <div className=\"store-count\">\n              Available at {product.priceStats.storeCount} store{product.priceStats.storeCount !== 1 ? 's' : ''}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC,iBAAiB,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACtE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMiB,YAAY,GAAG,EAAAN,eAAA,GAAAJ,OAAO,CAACW,MAAM,cAAAP,eAAA,uBAAdA,eAAA,CAAgBQ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC,OAAAT,gBAAA,GAAIL,OAAO,CAACW,MAAM,cAAAN,gBAAA,uBAAdA,gBAAA,CAAiB,CAAC,CAAC;EACtF,MAAMU,QAAQ,GAAGL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEM,GAAG;;EAElC;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BV,aAAa,CAAC,IAAI,CAAC;IACnBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC5BT,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMU,eAAe,GAAIC,CAAC,IAAK;IAC7B;IACA,IAAIA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,QAAQ,CAAC,EAAE;IAChCrB,OAAO,CAACD,OAAO,CAAC;EAClB,CAAC;;EAED;EACA,MAAMuB,kBAAkB,GAAIH,CAAC,IAAK;IAChCA,CAAC,CAACI,eAAe,CAAC,CAAC;IACnB;IACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE1B,OAAO,CAAC2B,IAAI,CAAC;EAC9C,CAAC;EAED,oBACE/B,OAAA;IAAKgC,SAAS,EAAC,cAAc;IAAC3B,OAAO,EAAEkB,eAAgB;IAAAU,QAAA,gBAErDjC,OAAA;MAAKgC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,GACrCd,QAAQ,IAAI,CAACT,UAAU,gBACtBV,OAAA,CAAAE,SAAA;QAAA+B,QAAA,GACGrB,YAAY,iBACXZ,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCjC,OAAA;YAAKgC,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACN,eACDrC,OAAA;UACEsC,GAAG,EAAEnB,QAAS;UACdoB,GAAG,EAAEnC,OAAO,CAAC2B,IAAK;UAClBC,SAAS,EAAE,iBAAiBpB,YAAY,GAAG,SAAS,GAAG,EAAE,EAAG;UAC5D4B,OAAO,EAAEnB,gBAAiB;UAC1BoB,MAAM,EAAEnB,eAAgB;UACxBoB,OAAO,EAAC;QAAM;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA,eACF,CAAC,gBAEHrC,OAAA;QAAKgC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCjC,OAAA;UAAKgC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACN,eAGDrC,OAAA;QAAKgC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5B7B,OAAO,CAACuC;MAAQ;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,cAAc;MAAAC,QAAA,GAE1B7B,OAAO,CAACwC,KAAK,iBACZ5C,OAAA;QAAKgC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE7B,OAAO,CAACwC;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACpD,eAGDrC,OAAA;QAAIgC,SAAS,EAAC,cAAc;QAACa,KAAK,EAAEzC,OAAO,CAAC2B,IAAK;QAAAE,QAAA,EAC9C7B,OAAO,CAAC2B;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,EAGJjC,OAAO,CAAC0C,WAAW,iBAClB9C,OAAA;QAAGgC,SAAS,EAAC,qBAAqB;QAACa,KAAK,EAAEzC,OAAO,CAAC0C,WAAY;QAAAb,QAAA,EAC3D7B,OAAO,CAAC0C,WAAW,CAACC,MAAM,GAAG,EAAE,GAC5B,GAAG3C,OAAO,CAAC0C,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC5C5C,OAAO,CAAC0C;MAAW;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtB,CACJ,EAGAjC,OAAO,CAAC6C,IAAI,IAAI7C,OAAO,CAAC6C,IAAI,CAACF,MAAM,GAAG,CAAC,iBACtC/C,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1B7B,OAAO,CAAC6C,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvCrD,OAAA;UAAkBgC,SAAS,EAAC,aAAa;UAAAC,QAAA,EACtCmB;QAAG,GADKC,KAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACP,CAAC,EACDjC,OAAO,CAAC6C,IAAI,CAACF,MAAM,GAAG,CAAC,iBACtB/C,OAAA;UAAMgC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,GAChC,EAAC7B,OAAO,CAAC6C,IAAI,CAACF,MAAM,GAAG,CAAC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNrC,OAAA;MAAKgC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BjC,OAAA;QACEgC,SAAS,EAAC,oBAAoB;QAC9B3B,OAAO,EAAEkB,eAAgB;QACzB,cAAY,mBAAmBnB,OAAO,CAAC2B,IAAI,EAAG;QAAAE,QAAA,EAC/C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAER/B,iBAAiB,iBAChBN,OAAA;QACEgC,SAAS,EAAC,gBAAgB;QAC1B3B,OAAO,EAAEsB,kBAAmB;QAC5B,cAAY,OAAOvB,OAAO,CAAC2B,IAAI,gBAAiB;QAChDc,KAAK,EAAC,mBAAmB;QAAAZ,QAAA,EAC1B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLjC,OAAO,CAACkD,UAAU,iBACjBtD,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjC,OAAA;QAAKgC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjC,OAAA;UAAMgC,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,OACrB,EAACnC,QAAQ,CAACyD,WAAW,CAACnD,OAAO,CAACkD,UAAU,CAACE,QAAQ,CAAC;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,EACNjC,OAAO,CAACkD,UAAU,CAACG,QAAQ,GAAGrD,OAAO,CAACkD,UAAU,CAACE,QAAQ,iBACxDxD,OAAA;UAAMgC,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,KACvB,EAACnC,QAAQ,CAACyD,WAAW,CAACnD,OAAO,CAACkD,UAAU,CAACG,QAAQ,CAAC;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELjC,OAAO,CAACkD,UAAU,CAACI,UAAU,iBAC5B1D,OAAA;QAAKgC,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,eACd,EAAC7B,OAAO,CAACkD,UAAU,CAACI,UAAU,EAAC,QAAM,EAACtD,OAAO,CAACkD,UAAU,CAACI,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;MAAA;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9B,EAAA,CArJIJ,WAAW;AAAAwD,EAAA,GAAXxD,WAAW;AAuJjB,eAAeA,WAAW;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}