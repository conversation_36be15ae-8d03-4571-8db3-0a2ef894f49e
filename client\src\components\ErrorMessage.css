/* Error Message Styles */
.error-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin: 1rem 0;
  border: 1px solid;
}

.error-message.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.error-message.warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.error-message.info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.error-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.error-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
}

.error-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.error-description {
  font-size: 0.9rem;
  opacity: 0.9;
}

.error-actions {
  flex-shrink: 0;
}

.retry-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.9rem;
  transition: var(--transition);
}

.retry-button:hover {
  background-color: #1e3d72;
}
