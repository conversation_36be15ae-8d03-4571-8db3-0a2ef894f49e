{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\components\\\\ProductGrid.js\";\nimport React from 'react';\nimport ProductCard from './ProductCard';\nimport './ProductGrid.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductGrid = ({\n  products,\n  onProductClick,\n  loading = false\n}) => {\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-grid\",\n      children: Array.from({\n        length: 8\n      }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-card skeleton\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skeleton-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skeleton-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line skeleton-title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line skeleton-brand\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line skeleton-price\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this);\n  }\n  if (!products || products.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-grid\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83D\\uDCE6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No products found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Try adjusting your search criteria or browse different categories.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-grid\",\n    children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n      product: product,\n      onClick: () => onProductClick(product)\n    }, product._id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductGrid;\nexport default ProductGrid;\nvar _c;\n$RefreshReg$(_c, \"ProductGrid\");", "map": {"version": 3, "names": ["React", "ProductCard", "jsxDEV", "_jsxDEV", "ProductGrid", "products", "onProductClick", "loading", "className", "children", "Array", "from", "length", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "product", "onClick", "_id", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/client/src/components/ProductGrid.js"], "sourcesContent": ["import React from 'react';\nimport ProductCard from './ProductCard';\nimport './ProductGrid.css';\n\nconst ProductGrid = ({ products, onProductClick, loading = false }) => {\n  if (loading) {\n    return (\n      <div className=\"product-grid\">\n        {Array.from({ length: 8 }).map((_, index) => (\n          <div key={index} className=\"product-card skeleton\">\n            <div className=\"skeleton-image\"></div>\n            <div className=\"skeleton-content\">\n              <div className=\"skeleton-line skeleton-title\"></div>\n              <div className=\"skeleton-line skeleton-brand\"></div>\n              <div className=\"skeleton-line skeleton-price\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (!products || products.length === 0) {\n    return (\n      <div className=\"empty-grid\">\n        <div className=\"empty-message\">\n          <div className=\"empty-icon\">📦</div>\n          <h3>No products found</h3>\n          <p>Try adjusting your search criteria or browse different categories.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"product-grid\">\n      {products.map(product => (\n        <ProductCard\n          key={product._id}\n          product={product}\n          onClick={() => onProductClick(product)}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default ProductGrid;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,cAAc;EAAEC,OAAO,GAAG;AAAM,CAAC,KAAK;EACrE,IAAIA,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAKK,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACtCZ,OAAA;QAAiBK,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAChDN,OAAA;UAAKK,SAAS,EAAC;QAAgB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtChB,OAAA;UAAKK,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BN,OAAA;YAAKK,SAAS,EAAC;UAA8B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDhB,OAAA;YAAKK,SAAS,EAAC;UAA8B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDhB,OAAA;YAAKK,SAAS,EAAC;UAA8B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA,GANEJ,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,IAAI,CAACd,QAAQ,IAAIA,QAAQ,CAACO,MAAM,KAAK,CAAC,EAAE;IACtC,oBACET,OAAA;MAAKK,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBN,OAAA;QAAKK,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BN,OAAA;UAAKK,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpChB,OAAA;UAAAM,QAAA,EAAI;QAAiB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BhB,OAAA;UAAAM,QAAA,EAAG;QAAkE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhB,OAAA;IAAKK,SAAS,EAAC,cAAc;IAAAC,QAAA,EAC1BJ,QAAQ,CAACQ,GAAG,CAACO,OAAO,iBACnBjB,OAAA,CAACF,WAAW;MAEVmB,OAAO,EAAEA,OAAQ;MACjBC,OAAO,EAAEA,CAAA,KAAMf,cAAc,CAACc,OAAO;IAAE,GAFlCA,OAAO,CAACE,GAAG;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGjB,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACI,EAAA,GAzCInB,WAAW;AA2CjB,eAAeA,WAAW;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}