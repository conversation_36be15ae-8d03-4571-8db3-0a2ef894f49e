const mongoose = require('mongoose');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../server/.env') });

// Import Store model
const Store = require('../server/models/Store');

const sampleStores = [
  {
    name: 'SPAR',
    branch: 'Sandton City',
    location: {
      type: 'Point',
      coordinates: [28.0473, -26.1076] // [longitude, latitude]
    },
    address: {
      street: 'Sandton City Shopping Centre, 83 Rivonia Rd',
      city: 'Sandton',
      province: 'Gauteng',
      postalCode: '2196'
    },
    contact: {
      phone: '+27 11 784 7911',
      email: '<EMAIL>'
    },
    scrapeUrl: 'https://www.spar.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'SPAR',
    branch: 'Menlyn Park',
    location: {
      type: 'Point',
      coordinates: [28.2751, -25.7879]
    },
    address: {
      street: 'Menlyn Park Shopping Centre, Atterbury Rd',
      city: 'Pretoria',
      province: 'Gauteng',
      postalCode: '0181'
    },
    contact: {
      phone: '+27 12 348 2000'
    },
    scrapeUrl: 'https://www.spar.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Checkers',
    branch: 'Canal Walk',
    location: {
      type: 'Point',
      coordinates: [18.4896, -33.8938]
    },
    address: {
      street: 'Canal Walk Shopping Centre, Century Blvd',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '7441'
    },
    contact: {
      phone: '+27 21 555 4000',
      email: '<EMAIL>'
    },
    scrapeUrl: 'https://www.checkers.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Checkers',
    branch: 'Eastgate',
    location: {
      type: 'Point',
      coordinates: [28.1751, -26.1879]
    },
    address: {
      street: 'Eastgate Shopping Centre, 43 Bradford Rd',
      city: 'Bedfordview',
      province: 'Gauteng',
      postalCode: '2008'
    },
    contact: {
      phone: '+27 11 456 7890'
    },
    scrapeUrl: 'https://www.checkers.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Pick n Pay',
    branch: 'Gateway Theatre of Shopping',
    location: {
      type: 'Point',
      coordinates: [31.0218, -29.7833]
    },
    address: {
      street: 'Gateway Theatre of Shopping, 1 Palm Blvd',
      city: 'Durban',
      province: 'KwaZulu-Natal',
      postalCode: '4319'
    },
    contact: {
      phone: '+27 31 566 0000',
      email: '<EMAIL>'
    },
    scrapeUrl: 'https://www.picknpay.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Pick n Pay',
    branch: 'Rosebank',
    location: {
      type: 'Point',
      coordinates: [28.0436, -26.1467]
    },
    address: {
      street: 'The Zone @ Rosebank, 173 Oxford Rd',
      city: 'Rosebank',
      province: 'Gauteng',
      postalCode: '2196'
    },
    contact: {
      phone: '+27 11 447 9000'
    },
    scrapeUrl: 'https://www.picknpay.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Woolworths',
    branch: 'V&A Waterfront',
    location: {
      type: 'Point',
      coordinates: [18.4194, -33.9025]
    },
    address: {
      street: 'V&A Waterfront, Dock Rd',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '8001'
    },
    contact: {
      phone: '+27 21 408 7000',
      email: '<EMAIL>'
    },
    scrapeUrl: 'https://www.woolworths.co.za',
    operatingHours: {
      monday: { open: '09:00', close: '21:00' },
      tuesday: { open: '09:00', close: '21:00' },
      wednesday: { open: '09:00', close: '21:00' },
      thursday: { open: '09:00', close: '21:00' },
      friday: { open: '09:00', close: '21:00' },
      saturday: { open: '09:00', close: '21:00' },
      sunday: { open: '09:00', close: '21:00' }
    }
  },
  {
    name: 'Woolworths',
    branch: 'Hyde Park Corner',
    location: {
      type: 'Point',
      coordinates: [28.0167, -26.1167]
    },
    address: {
      street: 'Hyde Park Corner, Jan Smuts Ave',
      city: 'Hyde Park',
      province: 'Gauteng',
      postalCode: '2196'
    },
    contact: {
      phone: '+27 11 325 4000'
    },
    scrapeUrl: 'https://www.woolworths.co.za',
    operatingHours: {
      monday: { open: '09:00', close: '20:00' },
      tuesday: { open: '09:00', close: '20:00' },
      wednesday: { open: '09:00', close: '20:00' },
      thursday: { open: '09:00', close: '20:00' },
      friday: { open: '09:00', close: '20:00' },
      saturday: { open: '09:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  }
];

async function createSampleStores() {
  try {
    console.log('🏪 Creating sample stores...');
    
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/basketcase';
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Clear existing stores
    const existingCount = await Store.countDocuments();
    if (existingCount > 0) {
      console.log(`🗑️  Clearing ${existingCount} existing stores...`);
      await Store.deleteMany({});
    }
    
    // Insert sample stores
    console.log(`📦 Inserting ${sampleStores.length} sample stores...`);
    const insertedStores = await Store.insertMany(sampleStores);
    
    console.log('✅ Sample stores created successfully!');
    console.log(`📊 Created ${insertedStores.length} stores across ${[...new Set(sampleStores.map(s => s.name))].length} chains`);
    
    // Display summary
    const storeCounts = {};
    sampleStores.forEach(store => {
      storeCounts[store.name] = (storeCounts[store.name] || 0) + 1;
    });
    
    console.log('\n📍 Store Summary:');
    Object.entries(storeCounts).forEach(([chain, count]) => {
      console.log(`   ${chain}: ${count} store${count > 1 ? 's' : ''}`);
    });
    
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    
  } catch (error) {
    console.error('❌ Error creating sample stores:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure MongoDB is running on the specified connection string');
    }
    
    process.exit(1);
  }
}

// Run the function if called directly
if (require.main === module) {
  createSampleStores()
    .then(() => {
      console.log('🎉 Sample store setup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error);
      process.exit(1);
    });
}

module.exports = { createSampleStores, sampleStores };
