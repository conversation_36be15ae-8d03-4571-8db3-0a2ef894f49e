const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('\n🛒 BasketCase Quick Start');
console.log('========================\n');

const isWindows = os.platform() === 'win32';

// Function to run the appropriate startup script
function runStartupScript() {
  const scriptName = isWindows ? 'start.bat' : 'start.sh';
  const scriptPath = path.join(__dirname, '..', scriptName);
  
  if (!fs.existsSync(scriptPath)) {
    console.error(`❌ Startup script not found: ${scriptPath}`);
    process.exit(1);
  }
  
  console.log(`🚀 Running ${scriptName}...`);
  console.log('This will set up and start the entire BasketCase application.\n');
  
  const command = isWindows ? scriptPath : `bash ${scriptPath}`;
  
  // Execute the startup script
  const child = spawn(command, [], {
    stdio: 'inherit',
    shell: true,
    cwd: path.join(__dirname, '..')
  });
  
  child.on('error', (error) => {
    console.error(`❌ Error running startup script: ${error.message}`);
    process.exit(1);
  });
  
  child.on('close', (code) => {
    if (code !== 0) {
      console.error(`❌ Startup script exited with code ${code}`);
      process.exit(code);
    }
  });
}

// Check if we're in the right directory
const packageJsonPath = path.join(__dirname, '..', 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ This script must be run from the BasketCase root directory');
  process.exit(1);
}

// Run the startup script
runStartupScript();
