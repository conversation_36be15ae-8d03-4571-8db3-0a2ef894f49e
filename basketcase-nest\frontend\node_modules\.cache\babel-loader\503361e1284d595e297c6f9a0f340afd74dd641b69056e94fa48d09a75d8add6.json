{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\basketcase-nest\\\\frontend\\\\src\\\\pages\\\\StoresPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { storesApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StoresPage = () => {\n  _s();\n  const [stores, setStores] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const fetchStores = async () => {\n      try {\n        setLoading(true);\n        const response = await storesApi.getAll();\n        if (response.success) {\n          setStores(response.stores || []);\n        }\n      } catch (error) {\n        console.error('Error fetching stores:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchStores();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border loading-spinner text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stores-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"page-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-store me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), \"Stores\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: \"Find stores near you\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: stores.length > 0 ? stores.map(store => {\n          var _store$contact;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-md-6 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card store-card h-100\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"card-title\",\n                  children: store.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"card-subtitle mb-2 text-muted\",\n                  children: store.branch\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"store-address\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-map-marker-alt me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 60,\n                      columnNumber: 25\n                    }, this), store.address.street]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-1\",\n                    children: [store.address.city, \", \", store.address.province]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 23\n                  }, this), store.address.postalCode && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-1\",\n                    children: store.address.postalCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 21\n                }, this), ((_store$contact = store.contact) === null || _store$contact === void 0 ? void 0 : _store$contact.phone) && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-phone me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 25\n                  }, this), store.contact.phone]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)\n          }, store._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this);\n        }) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-store fa-3x text-muted mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"No stores found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Store data is being loaded...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(StoresPage, \"3xRNw8mL7M78EzDar2q6uneJ+dQ=\");\n_c = StoresPage;\nexport default StoresPage;\nvar _c;\n$RefreshReg$(_c, \"StoresPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "storesApi", "jsxDEV", "_jsxDEV", "StoresPage", "_s", "stores", "setStores", "loading", "setLoading", "fetchStores", "response", "getAll", "success", "error", "console", "className", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "store", "_store$contact", "name", "branch", "address", "street", "city", "province", "postalCode", "contact", "phone", "_id", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/basketcase-nest/frontend/src/pages/StoresPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { storesApi } from '../services/api';\nimport { Store } from '../types';\n\nconst StoresPage: React.FC = () => {\n  const [stores, setStores] = useState<Store[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStores = async () => {\n      try {\n        setLoading(true);\n        const response = await storesApi.getAll();\n        if (response.success) {\n          setStores(response.stores || []);\n        }\n      } catch (error) {\n        console.error('Error fetching stores:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStores();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"spinner-border loading-spinner text-primary\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"stores-page\">\n      <div className=\"container py-4\">\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <h1 className=\"page-title\">\n              <i className=\"fas fa-store me-2\"></i>\n              Stores\n            </h1>\n            <p className=\"text-muted\">Find stores near you</p>\n          </div>\n        </div>\n\n        <div className=\"row\">\n          {stores.length > 0 ? (\n            stores.map((store) => (\n              <div key={store._id} className=\"col-lg-4 col-md-6 mb-4\">\n                <div className=\"card store-card h-100\">\n                  <div className=\"card-body\">\n                    <h5 className=\"card-title\">{store.name}</h5>\n                    <h6 className=\"card-subtitle mb-2 text-muted\">{store.branch}</h6>\n                    <div className=\"store-address\">\n                      <p className=\"mb-1\">\n                        <i className=\"fas fa-map-marker-alt me-2\"></i>\n                        {store.address.street}\n                      </p>\n                      <p className=\"mb-1\">\n                        {store.address.city}, {store.address.province}\n                      </p>\n                      {store.address.postalCode && (\n                        <p className=\"mb-1\">{store.address.postalCode}</p>\n                      )}\n                    </div>\n                    {store.contact?.phone && (\n                      <p className=\"mb-1\">\n                        <i className=\"fas fa-phone me-2\"></i>\n                        {store.contact.phone}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))\n          ) : (\n            <div className=\"col-12\">\n              <div className=\"text-center py-5\">\n                <i className=\"fas fa-store fa-3x text-muted mb-3\"></i>\n                <h4>No stores found</h4>\n                <p className=\"text-muted\">Store data is being loaded...</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StoresPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG5C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMU,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChB,MAAME,QAAQ,GAAG,MAAMV,SAAS,CAACW,MAAM,CAAC,CAAC;QACzC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpBN,SAAS,CAACI,QAAQ,CAACL,MAAM,IAAI,EAAE,CAAC;QAClC;MACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD,CAAC,SAAS;QACRL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKa,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCd,OAAA;QAAKa,SAAS,EAAC,6CAA6C;QAACE,IAAI,EAAC,QAAQ;QAAAD,QAAA,eACxEd,OAAA;UAAMa,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnB,OAAA;IAAKa,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC1Bd,OAAA;MAAKa,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7Bd,OAAA;QAAKa,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBd,OAAA;UAAKa,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBd,OAAA;YAAIa,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACxBd,OAAA;cAAGa,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,UAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnB,OAAA;YAAGa,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKa,SAAS,EAAC,KAAK;QAAAC,QAAA,EACjBX,MAAM,CAACiB,MAAM,GAAG,CAAC,GAChBjB,MAAM,CAACkB,GAAG,CAAEC,KAAK;UAAA,IAAAC,cAAA;UAAA,oBACfvB,OAAA;YAAqBa,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eACrDd,OAAA;cAAKa,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCd,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBd,OAAA;kBAAIa,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEQ,KAAK,CAACE;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5CnB,OAAA;kBAAIa,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAEQ,KAAK,CAACG;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjEnB,OAAA;kBAAKa,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5Bd,OAAA;oBAAGa,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACjBd,OAAA;sBAAGa,SAAS,EAAC;oBAA4B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC7CG,KAAK,CAACI,OAAO,CAACC,MAAM;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACJnB,OAAA;oBAAGa,SAAS,EAAC,MAAM;oBAAAC,QAAA,GAChBQ,KAAK,CAACI,OAAO,CAACE,IAAI,EAAC,IAAE,EAACN,KAAK,CAACI,OAAO,CAACG,QAAQ;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,EACHG,KAAK,CAACI,OAAO,CAACI,UAAU,iBACvB9B,OAAA;oBAAGa,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAEQ,KAAK,CAACI,OAAO,CAACI;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAClD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACL,EAAAI,cAAA,GAAAD,KAAK,CAACS,OAAO,cAAAR,cAAA,uBAAbA,cAAA,CAAeS,KAAK,kBACnBhC,OAAA;kBAAGa,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjBd,OAAA;oBAAGa,SAAS,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACpCG,KAAK,CAACS,OAAO,CAACC,KAAK;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAxBEG,KAAK,CAACW,GAAG;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBd,CAAC;QAAA,CACP,CAAC,gBAEFnB,OAAA;UAAKa,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrBd,OAAA;YAAKa,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/Bd,OAAA;cAAGa,SAAS,EAAC;YAAoC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDnB,OAAA;cAAAc,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBnB,OAAA;cAAGa,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAxFID,UAAoB;AAAAiC,EAAA,GAApBjC,UAAoB;AA0F1B,eAAeA,UAAU;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}