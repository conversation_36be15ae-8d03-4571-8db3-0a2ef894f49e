const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => new Promise((resolve) => rl.question(query, resolve));

async function setupEnvironment() {
  console.log('🛒 BasketCase Environment Setup');
  console.log('================================\n');

  try {
    // Check if server .env already exists
    const serverEnvPath = path.join(__dirname, '../server/.env');
    const serverEnvExists = fs.existsSync(serverEnvPath);

    if (serverEnvExists) {
      const overwrite = await question('Server .env file already exists. Overwrite? (y/N): ');
      if (overwrite.toLowerCase() !== 'y') {
        console.log('Skipping server environment setup.');
      } else {
        await setupServerEnv();
      }
    } else {
      await setupServerEnv();
    }

    // Check if client .env already exists
    const clientEnvPath = path.join(__dirname, '../client/.env');
    const clientEnvExists = fs.existsSync(clientEnvPath);

    if (clientEnvExists) {
      const overwrite = await question('Client .env file already exists. Overwrite? (y/N): ');
      if (overwrite.toLowerCase() !== 'y') {
        console.log('Skipping client environment setup.');
      } else {
        await setupClientEnv();
      }
    } else {
      await setupClientEnv();
    }

    // Create sample store data
    const createSampleData = await question('Create sample store data? (Y/n): ');
    if (createSampleData.toLowerCase() !== 'n') {
      await createSampleStores();
    }

    console.log('\n✅ Environment setup completed!');
    console.log('\nNext steps:');
    console.log('1. Make sure MongoDB is running');
    console.log('2. Run "npm run dev" to start both server and client');
    console.log('3. Visit http://localhost:3000 to see the app');
    console.log('4. Run "npm run scrape" to start scraping product data');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  } finally {
    rl.close();
  }
}

async function setupServerEnv() {
  console.log('\n📋 Server Environment Configuration');
  console.log('-----------------------------------');

  const port = await question('Server port (5000): ') || '5000';
  const mongoUri = await question('MongoDB URI (mongodb://localhost:27017/basketcase): ') || 'mongodb://localhost:27017/basketcase';
  const clientUrl = await question('Client URL (http://localhost:3000): ') || 'http://localhost:3000';
  const nodeEnv = await question('Node environment (development): ') || 'development';

  const serverEnvContent = `# Server Configuration
PORT=${port}
NODE_ENV=${nodeEnv}
CLIENT_URL=${clientUrl}

# Database Configuration
MONGODB_URI=${mongoUri}

# Scraping Configuration
SCRAPE_INTERVAL_HOURS=24
MAX_CONCURRENT_SCRAPERS=3

# Security
JWT_SECRET=basketcase_dev_secret_change_in_production

# Logging
LOG_LEVEL=info
`;

  const serverEnvPath = path.join(__dirname, '../server/.env');
  fs.writeFileSync(serverEnvPath, serverEnvContent);
  console.log('✅ Server .env file created');
}

async function setupClientEnv() {
  console.log('\n📋 Client Environment Configuration');
  console.log('-----------------------------------');

  const apiUrl = await question('API URL (http://localhost:5000/api): ') || 'http://localhost:5000/api';

  const clientEnvContent = `# API Configuration
REACT_APP_API_URL=${apiUrl}

# App Configuration
REACT_APP_NAME=BasketCase
REACT_APP_VERSION=1.0.0

# Features
REACT_APP_ENABLE_GEOLOCATION=true
REACT_APP_DEFAULT_LOCATION_LAT=-26.2041
REACT_APP_DEFAULT_LOCATION_LNG=28.0473
`;

  const clientEnvPath = path.join(__dirname, '../client/.env');
  fs.writeFileSync(clientEnvPath, clientEnvContent);
  console.log('✅ Client .env file created');
}

async function createSampleStores() {
  console.log('\n📋 Creating Sample Store Data');
  console.log('-----------------------------');

  const sampleStoresScript = `
const mongoose = require('mongoose');
const Store = require('../server/models/Store');
require('dotenv').config({ path: '../server/.env' });

const sampleStores = [
  {
    name: 'SPAR',
    branch: 'Sandton City',
    location: {
      type: 'Point',
      coordinates: [28.0473, -26.1076]
    },
    address: {
      street: 'Sandton City Shopping Centre',
      city: 'Sandton',
      province: 'Gauteng',
      postalCode: '2196'
    },
    contact: {
      phone: '+27 11 784 7911'
    },
    scrapeUrl: 'https://www.spar.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Checkers',
    branch: 'Canal Walk',
    location: {
      type: 'Point',
      coordinates: [18.4896, -33.8938]
    },
    address: {
      street: 'Canal Walk Shopping Centre',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '7441'
    },
    contact: {
      phone: '+27 21 555 4000'
    },
    scrapeUrl: 'https://www.checkers.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Pick n Pay',
    branch: 'Gateway Theatre of Shopping',
    location: {
      type: 'Point',
      coordinates: [31.0218, -29.7833]
    },
    address: {
      street: 'Gateway Theatre of Shopping',
      city: 'Durban',
      province: 'KwaZulu-Natal',
      postalCode: '4319'
    },
    contact: {
      phone: '+27 31 566 0000'
    },
    scrapeUrl: 'https://www.picknpay.co.za',
    operatingHours: {
      monday: { open: '08:00', close: '20:00' },
      tuesday: { open: '08:00', close: '20:00' },
      wednesday: { open: '08:00', close: '20:00' },
      thursday: { open: '08:00', close: '20:00' },
      friday: { open: '08:00', close: '20:00' },
      saturday: { open: '08:00', close: '18:00' },
      sunday: { open: '09:00', close: '17:00' }
    }
  },
  {
    name: 'Woolworths',
    branch: 'V&A Waterfront',
    location: {
      type: 'Point',
      coordinates: [18.4194, -33.9025]
    },
    address: {
      street: 'V&A Waterfront',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '8001'
    },
    contact: {
      phone: '+27 21 408 7000'
    },
    scrapeUrl: 'https://www.woolworths.co.za',
    operatingHours: {
      monday: { open: '09:00', close: '21:00' },
      tuesday: { open: '09:00', close: '21:00' },
      wednesday: { open: '09:00', close: '21:00' },
      thursday: { open: '09:00', close: '21:00' },
      friday: { open: '09:00', close: '21:00' },
      saturday: { open: '09:00', close: '21:00' },
      sunday: { open: '09:00', close: '21:00' }
    }
  }
];

async function createStores() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    
    // Clear existing stores
    await Store.deleteMany({});
    console.log('Cleared existing stores');
    
    // Insert sample stores
    await Store.insertMany(sampleStores);
    console.log('Sample stores created successfully');
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating sample stores:', error);
    process.exit(1);
  }
}

createStores();
`;

  const sampleStoresPath = path.join(__dirname, 'create-sample-stores.js');
  fs.writeFileSync(sampleStoresPath, sampleStoresScript);
  console.log('✅ Sample stores script created at scripts/create-sample-stores.js');
  console.log('   Run "node scripts/create-sample-stores.js" to populate the database');
}

// Run setup if called directly
if (require.main === module) {
  setupEnvironment();
}

module.exports = { setupEnvironment };
