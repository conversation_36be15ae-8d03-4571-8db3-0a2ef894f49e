{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\pages\\\\ComparePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport SearchBar from '../components/SearchBar';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComparePage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const handleSearch = (query, filters) => {\n    console.log('Search:', query, filters);\n    // TODO: Implement search functionality\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"compare-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Compare Prices\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Search for products to compare prices across different stores.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(SearchBar, {\n          onSearch: handleSearch,\n          placeholder: \"Search products to compare...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        message: \"Searching products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        message: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"compare-results\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Start by searching for a product above to see price comparisons.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(ComparePage, \"Iz3ozxQ+abMaAIcGIvU8cKUcBeo=\");\n_c = ComparePage;\nexport default ComparePage;\nvar _c;\n$RefreshReg$(_c, \"ComparePage\");", "map": {"version": 3, "names": ["React", "useState", "SearchBar", "LoadingSpinner", "ErrorMessage", "jsxDEV", "_jsxDEV", "ComparePage", "_s", "loading", "setLoading", "error", "setError", "handleSearch", "query", "filters", "console", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSearch", "placeholder", "message", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/basketcase/client/src/pages/ComparePage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport SearchBar from '../components/SearchBar';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nconst ComparePage = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const handleSearch = (query, filters) => {\n    console.log('Search:', query, filters);\n    // TODO: Implement search functionality\n  };\n\n  return (\n    <div className=\"compare-page\">\n      <div className=\"container\">\n        <h1>Compare Prices</h1>\n        <p>Search for products to compare prices across different stores.</p>\n        \n        <div className=\"search-section\">\n          <SearchBar onSearch={handleSearch} placeholder=\"Search products to compare...\" />\n        </div>\n\n        {loading && <LoadingSpinner message=\"Searching products...\" />}\n        {error && <ErrorMessage message={error} />}\n\n        <div className=\"compare-results\">\n          <p>Start by searching for a product above to see price comparisons.</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ComparePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMY,YAAY,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IACvCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,KAAK,EAAEC,OAAO,CAAC;IACtC;EACF,CAAC;EAED,oBACET,OAAA;IAAKY,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3Bb,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBb,OAAA;QAAAa,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBjB,OAAA;QAAAa,QAAA,EAAG;MAA8D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAErEjB,OAAA;QAAKY,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7Bb,OAAA,CAACJ,SAAS;UAACsB,QAAQ,EAAEX,YAAa;UAACY,WAAW,EAAC;QAA+B;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,EAELd,OAAO,iBAAIH,OAAA,CAACH,cAAc;QAACuB,OAAO,EAAC;MAAuB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC7DZ,KAAK,iBAAIL,OAAA,CAACF,YAAY;QAACsB,OAAO,EAAEf;MAAM;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1CjB,OAAA;QAAKY,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9Bb,OAAA;UAAAa,QAAA,EAAG;QAAgE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CA5BID,WAAW;AAAAoB,EAAA,GAAXpB,WAAW;AA8BjB,eAAeA,WAAW;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}