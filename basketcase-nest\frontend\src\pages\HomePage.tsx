import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { pricesApi } from '@/services/api';
import { SavingsItem, TrendingProduct } from '@/types';

const HomePage: React.FC = () => {
  const [biggestSavings, setBiggestSavings] = useState<SavingsItem[]>([]);
  const [trendingProducts, setTrendingProducts] = useState<TrendingProduct[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch biggest savings
        const savingsResponse = await pricesApi.getBiggestSavings(6);
        if (savingsResponse.success) {
          setBiggestSavings(savingsResponse.biggestSavings || []);
        }

        // Fetch trending products
        const trendingResponse = await pricesApi.getTrending();
        if (trendingResponse.success) {
          setTrendingProducts(trendingResponse.trending || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner-border loading-spinner text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <h1 className="hero-title">
                Compare Grocery Prices
                <br />
                <span className="text-warning">Save Money</span>
              </h1>
              <p className="hero-subtitle">
                Find the best deals across South African grocery stores. 
                Compare prices from SPAR, Checkers, Pick n Pay, and more.
              </p>
              <div className="hero-buttons">
                <Link to="/products" className="btn btn-light btn-lg me-3">
                  <i className="fas fa-search me-2"></i>
                  Start Comparing
                </Link>
                <Link to="/stores" className="btn btn-outline-light btn-lg">
                  <i className="fas fa-store me-2"></i>
                  Find Stores
                </Link>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="hero-stats">
                <div className="row text-center">
                  <div className="col-4">
                    <div className="stat-card">
                      <h3 className="stat-number">1000+</h3>
                      <p className="stat-label">Products</p>
                    </div>
                  </div>
                  <div className="col-4">
                    <div className="stat-card">
                      <h3 className="stat-number">50+</h3>
                      <p className="stat-label">Stores</p>
                    </div>
                  </div>
                  <div className="col-4">
                    <div className="stat-card">
                      <h3 className="stat-number">R500+</h3>
                      <p className="stat-label">Avg Savings</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Biggest Savings Section */}
      <section className="py-5">
        <div className="container">
          <div className="row mb-4">
            <div className="col-12">
              <h2 className="section-title">
                <i className="fas fa-fire text-danger me-2"></i>
                Biggest Savings Right Now
              </h2>
              <p className="text-muted">Don't miss these amazing deals!</p>
            </div>
          </div>
          
          <div className="row">
            {biggestSavings.slice(0, 6).map((item, index) => (
              <div key={index} className="col-lg-4 col-md-6 mb-4">
                <div className="card savings-card h-100">
                  <div className="card-body">
                    <div className="d-flex justify-content-between align-items-start mb-3">
                      <div>
                        <h5 className="card-title">{item.product.name}</h5>
                        <p className="text-muted mb-1">{item.product.brand}</p>
                        <small className="text-muted">
                          <i className="fas fa-store me-1"></i>
                          {item.store.name}
                        </small>
                      </div>
                      <span className="badge bg-danger">
                        Save R{item.savings.amount.toFixed(2)}
                      </span>
                    </div>
                    
                    <div className="price-info">
                      <div className="current-price">
                        <span className="h4 text-success">R{item.price.current.toFixed(2)}</span>
                        <span className="original-price ms-2">R{item.price.original.toFixed(2)}</span>
                      </div>
                      <div className="savings-percentage">
                        <small className="text-success">
                          <i className="fas fa-arrow-down me-1"></i>
                          {item.savings.percentage.toFixed(1)}% off
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <Link to="/products" className="btn btn-primary btn-lg">
              <i className="fas fa-eye me-2"></i>
              View All Deals
            </Link>
          </div>
        </div>
      </section>

      {/* Trending Products Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row mb-4">
            <div className="col-12">
              <h2 className="section-title">
                <i className="fas fa-trending-up text-primary me-2"></i>
                Trending Products
              </h2>
              <p className="text-muted">Most compared products this week</p>
            </div>
          </div>
          
          <div className="row">
            {trendingProducts.slice(0, 4).map((product, index) => (
              <div key={product._id} className="col-lg-3 col-md-6 mb-4">
                <div className="card trending-card h-100">
                  <div className="card-body text-center">
                    <div className="trending-rank">
                      <span className="badge bg-primary rounded-pill">#{index + 1}</span>
                    </div>
                    <h5 className="card-title mt-3">{product.productName}</h5>
                    <div className="price-range">
                      <div className="avg-price">
                        <span className="h5 text-primary">R{product.avgPrice.toFixed(2)}</span>
                        <small className="text-muted d-block">Average Price</small>
                      </div>
                      <div className="price-range-info mt-2">
                        <small className="text-muted">
                          Range: R{product.minPrice.toFixed(2)} - R{product.maxPrice.toFixed(2)}
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-5">
        <div className="container">
          <div className="row mb-4">
            <div className="col-12 text-center">
              <h2 className="section-title">Why Choose BasketCase?</h2>
              <p className="text-muted">Save time and money with our smart comparison tools</p>
            </div>
          </div>
          
          <div className="row">
            <div className="col-lg-4 col-md-6 mb-4">
              <div className="feature-card text-center">
                <div className="feature-icon">
                  <i className="fas fa-sync-alt text-primary"></i>
                </div>
                <h4>Real-time Updates</h4>
                <p className="text-muted">
                  Prices updated every 30 minutes to ensure you get the latest deals.
                </p>
              </div>
            </div>
            
            <div className="col-lg-4 col-md-6 mb-4">
              <div className="feature-card text-center">
                <div className="feature-icon">
                  <i className="fas fa-map-marker-alt text-primary"></i>
                </div>
                <h4>Store Locator</h4>
                <p className="text-muted">
                  Find the nearest stores with the best prices in your area.
                </p>
              </div>
            </div>
            
            <div className="col-lg-4 col-md-6 mb-4">
              <div className="feature-card text-center">
                <div className="feature-icon">
                  <i className="fas fa-chart-line text-primary"></i>
                </div>
                <h4>Price History</h4>
                <p className="text-muted">
                  Track price trends and know when to buy for maximum savings.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
