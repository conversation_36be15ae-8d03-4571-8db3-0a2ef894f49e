{"version": 3, "file": "Request.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Request.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,sDAAqD;AACrD,kEAA0D;AAC1D,4DAAyD;AACzD,4DAAwE;AACxE,wDAA0D;AAI1D;;GAEG;IACU,OAAO;;sBAAS,8BAAY;;;iBAA5B,OAAQ,SAAQ,WAS3B;;;YAuPA,wKAAQ,OAAO,6DAEd;;;QAxPD,MAAM,CAAC,IAAI,CACT,eAAgC,EAChC,KAA+C;YAE/C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,uBAAuB,IAnBZ,mDAAO,EAmBqD,IAAI,EAAC;QAC5E,MAAM,CAAU;QAChB,SAAS,CAAW;QACpB,SAAS,CAA6B;QAC7B,gBAAgB,CAAkB;QAClC,YAAY,GAAG,IAAI,+BAAe,EAAE,CAAC;QACrC,MAAM,CAA2C;QAE1D,YACE,eAAgC,EAChC,KAA+C;YAE/C,KAAK,EAAE,CAAC;YAER,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;YACxC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACtB,CAAC;QAED,WAAW;YACT,MAAM,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAClD,IAAI,8BAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CACxC,CAAC;YACF,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBACjD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChC,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,8BAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAChC,CAAC;YACF,cAAc,CAAC,EAAE,CAAC,2BAA2B,EAAE,KAAK,CAAC,EAAE;gBACrD,IACE,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;oBAC1C,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;oBACjC,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,EACrD,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;gBAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtC,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,cAAc,CAAC,EAAE,CAAC,sBAAsB,EAAE,KAAK,CAAC,EAAE;gBAChD,IACE,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;oBAC1C,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;oBACjC,4DAA4D;oBAC5D,CAAC,KAAK,CAAC,SAAS,EAChB,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YACH,cAAc,CAAC,EAAE,CAAC,oBAAoB,EAAE,KAAK,CAAC,EAAE;gBAC9C,IACE,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;oBAC1C,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;oBACjC,IAAI,CAAC,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,EACjD,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC;gBAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChC,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,cAAc,CAAC,EAAE,CAAC,2BAA2B,EAAE,KAAK,CAAC,EAAE;gBACrD,IACE,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;oBAC1C,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;oBACjC,IAAI,CAAC,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,EACjD,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;gBACpD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrC,8BAA8B;gBAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;oBAChE,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;QAC3D,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QACpC,CAAC;QACD,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;QACD,IAAI,OAAO;YACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;QACrC,CAAC;QACD,IAAI,EAAE;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;QACrC,CAAC;QACD,IAAI,SAAS;YACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAC/B,CAAC;QACD,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,CAAC;QACD,IAAI,UAAU;YACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,SAAS,CAAC;QAC7C,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QACD,IAAI,YAAY;YACd,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9B,OAAO,QAAQ,EAAE,CAAC;gBAChB,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACpC,OAAO,QAAQ,CAAC;gBAClB,CAAC;gBACD,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC;YAChC,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QACD,IAAI,GAAG;YACL,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;QACjC,CAAC;QACD,IAAI,SAAS;YACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAC/B,CAAC;QAED,IAAI,YAAY;YACd,2CAA2C;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,SAAS,CAAC;QAC/D,CAAC;QAED,IAAI,QAAQ;YACV,2CAA2C;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,SAAS,CAAC;QAC3D,CAAC;QAED,IAAI,WAAW;YACb,2CAA2C;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,KAAK,CAAC;QAC1D,CAAC;QAED,KAAK,CAAC,eAAe,CAAC,EACpB,GAAG,EACH,MAAM,EACN,OAAO,EACP,OAAO,EACP,IAAI,GACoD;YACxD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAClD,OAAO,EAAE,IAAI,CAAC,EAAE;gBAChB,GAAG;gBACH,MAAM;gBACN,OAAO;gBACP,IAAI;gBACJ,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAED,KAAK,CAAC,WAAW;YACf,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC9C,OAAO,EAAE,IAAI,CAAC,EAAE;aACjB,CAAC,CAAC;QACL,CAAC;QAED,KAAK,CAAC,eAAe,CAAC,EACpB,UAAU,EACV,YAAY,EACZ,OAAO,EACP,IAAI,GACoD;YACxD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAClD,OAAO,EAAE,IAAI,CAAC,EAAE;gBAChB,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,IAAI;aACL,CAAC,CAAC;QACL,CAAC;QAED,KAAK,CAAC,kBAAkB;YACtB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAClC,IAAI,CAAC,uBAAuB,GAAG,CAAC,KAAK,IAAI,EAAE;oBACzC,IAAI,CAAC;wBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE;4BACvD,QAAQ,iDAAgC;4BACxC,OAAO,EAAE,IAAI,CAAC,EAAE;yBACjB,CAAC,CAAC;wBAEH,OAAO,IAAA,gCAAkB,EACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,CACpC,CAAC;oBACJ,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,IACE,KAAK,YAAY,yBAAa;4BAC9B,KAAK,CAAC,eAAe,CAAC,QAAQ,CAC5B,yCAAyC,CAC1C,EACD,CAAC;4BACD,MAAM,IAAI,yBAAa,CACrB,gGAAgG,CACjG,CAAC;wBACJ,CAAC;wBAED,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,CAAC;YACD,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC;QAC5C,CAAC;QAED,KAAK,CAAC,gBAAgB,CACpB,UAE8C;YAE9C,IAAI,UAAU,CAAC,MAAM,KAAK,oBAAoB,EAAE,CAAC;gBAC/C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;oBACnD,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,WAAW,EAAE,UAAU,CAAC,WAAW;iBACpC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE;oBACnD,OAAO,EAAE,IAAI,CAAC,EAAE;oBAChB,MAAM,EAAE,UAAU,CAAC,MAAM;iBAC1B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGO,OAAO;YACb,IAAI,CAAC,6BAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAEQ,yBALR,+BAAe,GAKN,6BAAa,EAAC;YACtB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,6BAAa,CAAC,EAAE,CAAC;QACzB,CAAC;QAED,MAAM;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;QACrC,CAAC;;;AA3QU,0BAAO"}