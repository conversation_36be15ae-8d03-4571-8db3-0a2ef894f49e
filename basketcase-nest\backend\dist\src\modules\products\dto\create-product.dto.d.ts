declare class ProductImageDto {
    url: string;
    isPrimary?: boolean;
    alt?: string;
}
declare class ProductSpecificationsDto {
    weight?: string;
    volume?: string;
    dimensions?: string;
    ingredients?: string[];
    nutritionalInfo?: Record<string, any>;
}
export declare class CreateProductDto {
    name: string;
    brand: string;
    category: string;
    description?: string;
    images?: ProductImageDto[];
    barcode?: string;
    specifications?: ProductSpecificationsDto;
    isActive?: boolean;
}
export {};
