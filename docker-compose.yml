version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: basketcase-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: basketcase
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - basketcase-network

  # Backend Server
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: basketcase-server
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: *********************************************************************
      CLIENT_URL: http://localhost:3000
      JWT_SECRET: your_production_jwt_secret_here
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
    volumes:
      - ./server:/app
      - /app/node_modules
    networks:
      - basketcase-network

  # Frontend Client
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: basketcase-client
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
    ports:
      - "3000:3000"
    depends_on:
      - server
    volumes:
      - ./client:/app
      - /app/node_modules
    networks:
      - basketcase-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: basketcase-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - client
      - server
    networks:
      - basketcase-network

volumes:
  mongodb_data:

networks:
  basketcase-network:
    driver: bridge
