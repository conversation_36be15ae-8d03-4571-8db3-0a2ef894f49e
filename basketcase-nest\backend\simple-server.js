const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Starting BasketCase API with live scraping...');

// Auto-scraping simulation - runs every 30 seconds for demo
let scrapingData = {
  lastRun: new Date(),
  totalProducts: 25,
  isRunning: false
};

// Dynamic product generation based on scraping
// Simulate scraping process
const runAutoScraping = () => {
  console.log('🔄 Auto-scraping running...');
  scrapingData.isRunning = true;

  setTimeout(() => {
    scrapingData.lastRun = new Date();
    scrapingData.totalProducts += Math.floor(Math.random() * 5) + 1;
    scrapingData.isRunning = false;
    console.log(`✅ Auto-scraping completed. Total products: ${scrapingData.totalProducts}`);
  }, 3000);
};

// Run scraping every 30 seconds for demo
setInterval(runAutoScraping, 30000);

// Run initial scraping after 5 seconds
setTimeout(runAutoScraping, 5000);

// Generate dynamic products based on scraping data
const generateProducts = (query = {}) => {
  const { search, category, brand } = query;

  // Generate products based on search or show variety
  const allProducts = [];

  if (search) {
    // Search results
    const searchTerms = search.toLowerCase();
    if (searchTerms.includes('coca') || searchTerms.includes('cola')) {
      allProducts.push({
        _id: 'search-1',
        name: 'Coca-Cola Original 2L',
        brand: 'Coca-Cola',
        category: 'Beverages',
        description: 'Refreshing Coca-Cola Original 2L bottle',
        images: [{ url: 'https://via.placeholder.com/300x200?text=Coca-Cola+2L', isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: true
      });
    }
    if (searchTerms.includes('bread') || searchTerms.includes('albany')) {
      allProducts.push({
        _id: 'search-2',
        name: 'Albany Superior White Bread 700g',
        brand: 'Albany',
        category: 'Bakery',
        description: 'Fresh Albany Superior White Bread 700g',
        images: [{ url: 'https://via.placeholder.com/300x200?text=Albany+Bread', isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: true
      });
    }
    if (searchTerms.includes('milk') || searchTerms.includes('clover')) {
      allProducts.push({
        _id: 'search-3',
        name: 'Clover Full Cream Milk 2L',
        brand: 'Clover',
        category: 'Dairy & Eggs',
        description: 'Fresh Clover Full Cream Milk 2L',
        images: [{ url: 'https://via.placeholder.com/300x200?text=Clover+Milk', isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: true
      });
    }
  } else {
    // Show variety of products when no search - these are "scraped" products
    const productTemplates = [
      { name: 'Coca-Cola Original 2L', brand: 'Coca-Cola', category: 'Beverages', text: 'Coca-Cola+2L' },
      { name: 'Albany White Bread 700g', brand: 'Albany', category: 'Bakery', text: 'Albany+Bread' },
      { name: 'Clover Full Cream Milk 2L', brand: 'Clover', category: 'Dairy & Eggs', text: 'Clover+Milk' },
      { name: 'Rainbow Chicken Breast 1kg', brand: 'Rainbow', category: 'Meat & Poultry', text: 'Chicken+Breast' },
      { name: 'Fresh Bananas per kg', brand: 'Fresh', category: 'Fruits & Vegetables', text: 'Fresh+Bananas' },
      { name: 'Barilla Pasta 500g', brand: 'Barilla', category: 'Pantry Staples', text: 'Barilla+Pasta' },
      { name: 'Nescafe Gold Coffee 200g', brand: 'Nescafe', category: 'Beverages', text: 'Nescafe+Coffee' },
      { name: 'Kelloggs Cornflakes 500g', brand: 'Kelloggs', category: 'Breakfast', text: 'Cornflakes' }
    ];

    productTemplates.forEach((template, index) => {
      allProducts.push({
        _id: `product-${index + 1}`,
        name: template.name,
        brand: template.brand,
        category: template.category,
        description: `Fresh ${template.name} - scraped from stores`,
        images: [{ url: `https://via.placeholder.com/300x200?text=${template.text}`, isPrimary: true }],
        lastScraped: scrapingData.lastRun,
        isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000
      });
    });
  }

  // Filter by category if specified
  let filteredProducts = allProducts;
  if (category && category !== 'All Categories') {
    filteredProducts = allProducts.filter(p => p.category === category);
  }

  // Filter by brand if specified
  if (brand && brand !== 'All Brands') {
    filteredProducts = filteredProducts.filter(p => p.brand === brand);
  }

  return filteredProducts;
};
// Generate dynamic savings based on current scraping data
const generateSavings = () => {
  return [
    {
      product: { _id: '1', name: 'Coca-Cola Original 2L', brand: 'Coca-Cola', category: 'Beverages' },
      store: { _id: '1', name: 'SPAR', branch: 'Sandton City' },
      price: { current: 18.99, original: 24.99 },
      savings: { amount: 6.00, percentage: 24.0 },
      lastScraped: scrapingData.lastRun,
      isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000,
    },
    {
      product: { _id: '2', name: 'Albany White Bread 700g', brand: 'Albany', category: 'Bakery' },
      store: { _id: '2', name: 'Checkers', branch: 'Canal Walk' },
      price: { current: 12.99, original: 16.99 },
      savings: { amount: 4.00, percentage: 23.5 },
      lastScraped: scrapingData.lastRun,
      isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000,
    },
    {
      product: { _id: '3', name: 'Clover Full Cream Milk 2L', brand: 'Clover', category: 'Dairy & Eggs' },
      store: { _id: '3', name: 'Pick n Pay', branch: 'Gateway' },
      price: { current: 22.99, original: 28.99 },
      savings: { amount: 6.00, percentage: 20.7 },
      lastScraped: scrapingData.lastRun,
      isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000,
    },
    {
      product: { _id: '4', name: 'Rainbow Chicken Breast 1kg', brand: 'Rainbow', category: 'Meat & Poultry' },
      store: { _id: '4', name: 'Woolworths', branch: 'V&A Waterfront' },
      price: { current: 89.99, original: 109.99 },
      savings: { amount: 20.00, percentage: 18.2 },
      lastScraped: scrapingData.lastRun,
      isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000,
    },
    {
      product: { _id: '5', name: 'Fresh Bananas 1kg', brand: 'Fresh', category: 'Fruits & Vegetables' },
      store: { _id: '1', name: 'SPAR', branch: 'Menlyn Park' },
      price: { current: 19.99, original: 24.99 },
      savings: { amount: 5.00, percentage: 20.0 },
      lastScraped: scrapingData.lastRun,
      isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000,
    },
    {
      product: { _id: '6', name: 'Barilla Pasta 500g', brand: 'Barilla', category: 'Pantry Staples' },
      store: { _id: '2', name: 'Checkers', branch: 'Eastgate' },
      price: { current: 15.99, original: 19.99 },
      savings: { amount: 4.00, percentage: 20.0 },
      lastScraped: scrapingData.lastRun,
      isRecentlyUpdated: (Date.now() - scrapingData.lastRun.getTime()) < 60000,
    }
  ];
};

// Generate dynamic trending based on scraping data
const generateTrending = () => {
  return [
    { _id: '1', productName: 'Coca-Cola Original 2L', avgPrice: 22.50, minPrice: 18.99, maxPrice: 24.99, priceCount: 4 },
    { _id: '2', productName: 'Albany White Bread 700g', avgPrice: 14.50, minPrice: 12.99, maxPrice: 16.99, priceCount: 4 },
    { _id: '3', productName: 'Clover Full Cream Milk 2L', avgPrice: 25.50, minPrice: 22.99, maxPrice: 28.99, priceCount: 4 },
    { _id: '4', productName: 'Rainbow Chicken Breast 1kg', avgPrice: 99.99, minPrice: 89.99, maxPrice: 109.99, priceCount: 4 },
    { _id: '5', productName: 'Fresh Bananas 1kg', avgPrice: 22.49, minPrice: 19.99, maxPrice: 24.99, priceCount: 4 }
  ];
};

// Generate stores data
const generateStores = () => {
  return [
    {
      _id: '1',
      name: 'SPAR',
      branch: 'Sandton City',
      address: { street: 'Sandton City Shopping Centre', city: 'Sandton', province: 'Gauteng', postalCode: '2196', country: 'South Africa' },
      contact: { phone: '+27 11 217 6000', email: '<EMAIL>' },
      isActive: true,
    },
    {
      _id: '2',
      name: 'Checkers',
      branch: 'Canal Walk',
      address: { street: 'Canal Walk Shopping Centre', city: 'Cape Town', province: 'Western Cape', postalCode: '7441', country: 'South Africa' },
      contact: { phone: '+27 21 555 4000', email: '<EMAIL>' },
      isActive: true,
    },
    {
      _id: '3',
      name: 'Pick n Pay',
      branch: 'Gateway',
      address: { street: 'Gateway Theatre of Shopping', city: 'Durban', province: 'KwaZulu-Natal', postalCode: '4319', country: 'South Africa' },
      contact: { phone: '+27 31 566 0000', email: '<EMAIL>' },
      isActive: true,
    },
    {
      _id: '4',
      name: 'Woolworths',
      branch: 'V&A Waterfront',
      address: { street: 'V&A Waterfront', city: 'Cape Town', province: 'Western Cape', postalCode: '8001', country: 'South Africa' },
      contact: { phone: '+27 21 408 7000', email: '<EMAIL>' },
      isActive: true,
    }
  ];
};

// Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date(), message: 'BasketCase API is running' });
});

app.get('/api/products', (req, res) => {
  const filteredProducts = generateProducts(req.query);

  res.json({
    success: true,
    data: filteredProducts,
    pagination: {
      current: 1,
      total: 1,
      totalItems: scrapingData.totalProducts
    },
    scrapingInfo: {
      lastRun: scrapingData.lastRun,
      totalProducts: scrapingData.totalProducts,
      isRunning: scrapingData.isRunning
    }
  });
});

app.get('/api/prices/biggest-savings', (req, res) => {
  const savings = generateSavings();
  res.json({
    success: true,
    data: savings,
    biggestSavings: savings,
    totalSavings: savings.length,
    scrapingInfo: {
      lastRun: scrapingData.lastRun,
      isRunning: scrapingData.isRunning
    }
  });
});

app.get('/api/prices/trending', (req, res) => {
  const trending = generateTrending();
  res.json({
    success: true,
    data: trending,
    trending: trending,
    scrapingInfo: {
      lastRun: scrapingData.lastRun,
      totalProducts: scrapingData.totalProducts
    }
  });
});

app.get('/api/stores', (req, res) => {
  const stores = generateStores();
  res.json({
    success: true,
    data: stores
  });
});

// Catch all
app.get('/api/*', (req, res) => {
  res.status(404).json({ success: false, message: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 BasketCase API started successfully!');
  console.log(`📖 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🔍 Health Check: http://localhost:${PORT}/api/health`);
  console.log('✅ Mock data loaded - ready for demo!');
});

console.log('✅ BasketCase API ready with live scraping system!');
