const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data
const mockProducts = [
  {
    _id: '1',
    name: 'Coca-Cola Original 2L',
    brand: 'Coca-Cola',
    category: 'Beverages',
    description: 'Refreshing Coca-Cola Original 2L bottle',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Coca-Cola+2L', isPrimary: true }],
    barcode: '5449000000996',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '2',
    name: 'Albany Superior White Bread 700g',
    brand: 'Albany',
    category: 'Bakery',
    description: 'Fresh Albany Superior White Bread 700g',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Albany+Bread', isPrimary: true }],
    barcode: '6001087007726',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '3',
    name: 'Clover Full Cream Milk 2L',
    brand: 'Clover',
    category: 'Dairy & Eggs',
    description: 'Fresh Clover Full Cream Milk 2L',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Clover+Milk', isPrimary: true }],
    barcode: '6001087351234',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '4',
    name: 'Rainbow Chicken Breast Fillets 1kg',
    brand: 'Rainbow',
    category: 'Meat & Poultry',
    description: 'Fresh Rainbow Chicken Breast Fillets 1kg',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Chicken+Breast', isPrimary: true }],
    barcode: '6009175123456',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '5',
    name: 'Fresh Bananas per kg',
    brand: 'Fresh Produce',
    category: 'Fruits & Vegetables',
    description: 'Fresh ripe bananas per kilogram',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Fresh+Bananas', isPrimary: true }],
    barcode: '2000000000001',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '6',
    name: 'Nescafe Gold Coffee 200g',
    brand: 'Nescafe',
    category: 'Beverages',
    description: 'Premium Nescafe Gold instant coffee 200g',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Nescafe+Gold', isPrimary: true }],
    barcode: '7613036931721',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '7',
    name: 'Kelloggs Cornflakes 500g',
    brand: 'Kelloggs',
    category: 'Breakfast',
    description: 'Crispy Kelloggs Cornflakes cereal 500g',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Cornflakes', isPrimary: true }],
    barcode: '5053827181457',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '8',
    name: 'Barilla Pasta Penne 500g',
    brand: 'Barilla',
    category: 'Pantry Staples',
    description: 'Italian Barilla Penne pasta 500g',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Barilla+Pasta', isPrimary: true }],
    barcode: '8076809513821',
    isActive: true,
    lastScraped: new Date(),
    isRecentlyUpdated: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockSavings = [
  {
    product: { _id: '1', name: 'Coca-Cola Original 2L', brand: 'Coca-Cola', category: 'Beverages' },
    store: { _id: '1', name: 'SPAR', branch: 'Sandton City' },
    price: { current: 18.99, original: 24.99 },
    savings: { amount: 6.00, percentage: 24.0 },
    lastScraped: new Date(),
    isRecentlyUpdated: true,
  },
  {
    product: { _id: '2', name: 'Albany White Bread 700g', brand: 'Albany', category: 'Bakery' },
    store: { _id: '2', name: 'Checkers', branch: 'Canal Walk' },
    price: { current: 12.99, original: 16.99 },
    savings: { amount: 4.00, percentage: 23.5 },
    lastScraped: new Date(),
    isRecentlyUpdated: true,
  },
  {
    product: { _id: '3', name: 'Clover Full Cream Milk 2L', brand: 'Clover', category: 'Dairy & Eggs' },
    store: { _id: '3', name: 'Pick n Pay', branch: 'Gateway' },
    price: { current: 22.99, original: 28.99 },
    savings: { amount: 6.00, percentage: 20.7 },
    lastScraped: new Date(),
    isRecentlyUpdated: true,
  },
];

const mockTrending = [
  { _id: '1', productName: 'Coca-Cola Original 2L', avgPrice: 22.50, minPrice: 18.99, maxPrice: 24.99, priceCount: 4 },
  { _id: '2', productName: 'Albany White Bread 700g', avgPrice: 14.50, minPrice: 12.99, maxPrice: 16.99, priceCount: 4 },
  { _id: '3', productName: 'Clover Full Cream Milk 2L', avgPrice: 25.50, minPrice: 22.99, maxPrice: 28.99, priceCount: 4 },
];

const mockStores = [
  {
    _id: '1',
    name: 'SPAR',
    branch: 'Sandton City',
    address: { street: 'Sandton City Shopping Centre', city: 'Sandton', province: 'Gauteng', postalCode: '2196', country: 'South Africa' },
    contact: { phone: '+27 11 217 6000', email: '<EMAIL>' },
    isActive: true,
  },
  {
    _id: '2',
    name: 'Checkers',
    branch: 'Canal Walk',
    address: { street: 'Canal Walk Shopping Centre', city: 'Cape Town', province: 'Western Cape', postalCode: '7441', country: 'South Africa' },
    contact: { phone: '+27 21 555 4000', email: '<EMAIL>' },
    isActive: true,
  },
];

// Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date(), message: 'BasketCase API is running' });
});

app.get('/api/products', (req, res) => {
  const { search, category, brand } = req.query;
  let filteredProducts = [...mockProducts];

  if (search) {
    const searchLower = search.toLowerCase();
    filteredProducts = filteredProducts.filter(p => 
      p.name.toLowerCase().includes(searchLower) || 
      p.brand.toLowerCase().includes(searchLower)
    );
  }

  if (category && category !== 'All Categories') {
    filteredProducts = filteredProducts.filter(p => p.category === category);
  }

  if (brand && brand !== 'All Brands') {
    filteredProducts = filteredProducts.filter(p => p.brand === brand);
  }

  res.json({
    success: true,
    data: filteredProducts,
    pagination: { current: 1, total: 1, totalItems: filteredProducts.length }
  });
});

app.get('/api/prices/biggest-savings', (req, res) => {
  res.json({
    success: true,
    data: mockSavings,
    biggestSavings: mockSavings,
    totalSavings: mockSavings.length
  });
});

app.get('/api/prices/trending', (req, res) => {
  res.json({
    success: true,
    data: mockTrending,
    trending: mockTrending
  });
});

app.get('/api/stores', (req, res) => {
  res.json({
    success: true,
    data: mockStores
  });
});

// Catch all
app.get('/api/*', (req, res) => {
  res.status(404).json({ success: false, message: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 BasketCase API started successfully!');
  console.log(`📖 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🔍 Health Check: http://localhost:${PORT}/api/health`);
  console.log('✅ Mock data loaded - ready for demo!');
});

// Simulate scraping logs
setInterval(() => {
  console.log('🔄 Auto-scraping completed. Products updated:', mockProducts.length);
}, 30000);
