{"name": "basketcase-api", "version": "1.0.0", "description": "BasketCase Price Comparison API - NestJS TypeScript", "author": "BasketCase Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^9.4.0", "@nestjs/core": "^9.4.0", "@nestjs/platform-express": "^9.4.0", "@nestjs/mongoose": "^9.2.2", "@nestjs/config": "^2.3.1", "@nestjs/schedule": "^2.2.1", "@nestjs/swagger": "^6.3.0", "mongoose": "^7.0.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "puppeteer": "^19.11.1", "cheerio": "^1.0.0-rc.12", "axios": "^1.4.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^9.4.2", "@nestjs/schematics": "^9.1.0", "@nestjs/testing": "^9.4.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/node": "^18.16.12", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}