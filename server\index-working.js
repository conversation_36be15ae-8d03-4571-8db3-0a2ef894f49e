const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/basketcase')
  .then(() => {
    console.log('✅ Connected to MongoDB');
  })
  .catch(err => {
    console.error('❌ MongoDB connection error:', err);
  });

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    message: 'Server is running!'
  });
});

// Mock data for testing
const mockProducts = [
  {
    _id: '1',
    name: 'Coca-Cola 2L',
    brand: 'Coca-Cola',
    category: 'Beverages',
    description: 'Refreshing cola drink',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Coca-Cola', isPrimary: true }]
  },
  {
    _id: '2',
    name: 'White Bread 700g',
    brand: 'Albany',
    category: 'Bakery',
    description: 'Fresh white bread',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Bread', isPrimary: true }]
  },
  {
    _id: '3',
    name: 'Full Cream Milk 2L',
    brand: 'Clover',
    category: 'Dairy & Eggs',
    description: 'Fresh full cream milk',
    images: [{ url: 'https://via.placeholder.com/300x200?text=Milk', isPrimary: true }]
  }
];

const mockStores = [
  {
    _id: '1',
    name: 'SPAR',
    branch: 'Sandton City',
    location: { type: 'Point', coordinates: [28.0473, -26.1076] },
    address: {
      street: 'Sandton City Shopping Centre',
      city: 'Sandton',
      province: 'Gauteng',
      postalCode: '2196'
    }
  },
  {
    _id: '2',
    name: 'Checkers',
    branch: 'Canal Walk',
    location: { type: 'Point', coordinates: [18.4896, -33.8938] },
    address: {
      street: 'Canal Walk Shopping Centre',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '7441'
    }
  }
];

// Simple API routes
app.get('/api/products', (req, res) => {
  res.json({
    success: true,
    products: mockProducts,
    pagination: {
      current: 1,
      total: 1,
      totalItems: mockProducts.length
    }
  });
});

app.get('/api/products/categories', (req, res) => {
  const categories = [...new Set(mockProducts.map(p => p.category))];
  res.json({ success: true, categories });
});

app.get('/api/products/brands', (req, res) => {
  const brands = [...new Set(mockProducts.map(p => p.brand))];
  res.json({ success: true, brands });
});

app.get('/api/stores', (req, res) => {
  res.json({
    success: true,
    stores: mockStores
  });
});

app.get('/api/stores/provinces', (req, res) => {
  const provinces = [...new Set(mockStores.map(s => s.address.province))];
  res.json({ success: true, provinces });
});

app.get('/api/stores/cities/:province', (req, res) => {
  const { province } = req.params;
  const cities = mockStores
    .filter(s => s.address.province === province)
    .map(s => s.address.city);
  res.json({ success: true, cities: [...new Set(cities)] });
});

app.get('/api/prices/trending/:days', (req, res) => {
  const mockTrending = [
    {
      _id: '1',
      productName: 'Coca-Cola 2L',
      avgPrice: 22.50,
      minPrice: 18.99,
      maxPrice: 24.99
    }
  ];
  res.json({ success: true, trending: mockTrending });
});

app.get('/api/prices/promotions', (req, res) => {
  const mockPromotions = [
    {
      product: { _id: '1', name: 'Coca-Cola 2L' },
      store: { _id: '1', name: 'SPAR' },
      price: { current: 18.99, original: 24.99 },
      promotion: { promotionDescription: 'Special offer' }
    }
  ];
  res.json({ success: true, promotions: mockPromotions });
});

app.get('/api/prices/biggest-savings', (req, res) => {
  const mockSavings = [
    {
      product: {
        _id: '1',
        name: 'Coca-Cola 2L Bottle',
        brand: 'Coca-Cola',
        category: 'Beverages'
      },
      store: {
        _id: '1',
        name: 'SPAR',
        branch: 'Sandton City'
      },
      price: {
        current: 18.99,
        original: 24.99
      },
      savings: {
        amount: 6.00,
        percentage: 24.0
      }
    },
    {
      product: {
        _id: '2',
        name: 'White Bread 700g',
        brand: 'Albany',
        category: 'Bakery'
      },
      store: {
        _id: '2',
        name: 'Checkers',
        branch: 'Canal Walk'
      },
      price: {
        current: 12.99,
        original: 16.99
      },
      savings: {
        amount: 4.00,
        percentage: 23.5
      }
    }
  ];
  
  res.json({
    success: true,
    biggestSavings: mockSavings,
    totalSavings: mockSavings.length
  });
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({ message: 'API route not found' });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ 
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

app.listen(PORT, () => {
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🌐 API available at: http://localhost:${PORT}/api/health`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}/api`);
});
