{"name": "source-map", "description": "Generates and consumes source maps", "version": "0.6.1", "homepage": "https://github.com/mozilla/source-map", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "usrbincc <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> Smith <<EMAIL>>", "<PERSON> <<EMAIL>>", "azu <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><PERSON>@walmartlabs.com>", "<PERSON> <jeff<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "djchie <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "typings": "source-map"}