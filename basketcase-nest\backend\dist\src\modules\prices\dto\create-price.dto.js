"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePriceDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class PriceAvailabilityDto {
    constructor() {
        this.inStock = true;
    }
}
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Is product in stock', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PriceAvailabilityDto.prototype, "inStock", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Stock level description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PriceAvailabilityDto.prototype, "stockLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Last availability check' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], PriceAvailabilityDto.prototype, "lastChecked", void 0);
class PricePromotionDto {
    constructor() {
        this.isOnPromotion = false;
    }
}
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Is product on promotion', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PricePromotionDto.prototype, "isOnPromotion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Promotion description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PricePromotionDto.prototype, "promotionDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Promotion type' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PricePromotionDto.prototype, "promotionType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Promotion valid from date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], PricePromotionDto.prototype, "validFrom", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Promotion valid until date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], PricePromotionDto.prototype, "validUntil", void 0);
class PriceHistoryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Historical price' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PriceHistoryDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Price date' }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], PriceHistoryDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Price source' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PriceHistoryDto.prototype, "source", void 0);
class ScrapingInfoDto {
}
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Last scraped timestamp' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], ScrapingInfoDto.prototype, "lastScraped", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Source URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ScrapingInfoDto.prototype, "sourceUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Scraping method used' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ScrapingInfoDto.prototype, "scrapingMethod", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Confidence score (0-1)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ScrapingInfoDto.prototype, "confidence", void 0);
class CreatePriceDto {
    constructor() {
        this.currency = 'ZAR';
    }
}
exports.CreatePriceDto = CreatePriceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Product ID' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePriceDto.prototype, "product", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store ID' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePriceDto.prototype, "store", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current price', example: 24.99 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePriceDto.prototype, "current", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Original price (before discount)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePriceDto.prototype, "original", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency code', default: 'ZAR' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePriceDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Product availability' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PriceAvailabilityDto),
    __metadata("design:type", PriceAvailabilityDto)
], CreatePriceDto.prototype, "availability", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Promotion information' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PricePromotionDto),
    __metadata("design:type", PricePromotionDto)
], CreatePriceDto.prototype, "promotion", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Price history' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PriceHistoryDto),
    __metadata("design:type", Array)
], CreatePriceDto.prototype, "priceHistory", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Scraping information' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ScrapingInfoDto),
    __metadata("design:type", ScrapingInfoDto)
], CreatePriceDto.prototype, "scrapingInfo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Is price active', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePriceDto.prototype, "isActive", void 0);
//# sourceMappingURL=create-price.dto.js.map