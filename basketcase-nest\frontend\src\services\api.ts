import axios from 'axios';
import {
  Product,
  Store,
  Price,
  ProductQuery,
  SavingsItem,
  TrendingProduct,
  ScrapingStatus,
  PaginatedResponse,
  ApiResponse
} from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Products API
export const productsApi = {
  getAll: async (query: ProductQuery = {}): Promise<PaginatedResponse<Product>> => {
    const response = await api.get('/products', { params: query });
    return response.data;
  },

  getById: async (id: string): Promise<Product> => {
    const response = await api.get(`/products/${id}`);
    return response.data;
  },

  search: async (searchTerm: string, limit?: number): Promise<ApiResponse<Product[]>> => {
    const response = await api.get('/products/search', {
      params: { q: searchTerm, limit },
    });
    return response.data;
  },

  getCategories: async (): Promise<ApiResponse<string[]>> => {
    const response = await api.get('/products/meta/categories');
    return response.data;
  },

  getBrands: async (category?: string): Promise<ApiResponse<string[]>> => {
    const response = await api.get('/products/meta/brands', {
      params: { category },
    });
    return response.data;
  },

  create: async (product: Partial<Product>): Promise<Product> => {
    const response = await api.post('/products', product);
    return response.data;
  },

  update: async (id: string, product: Partial<Product>): Promise<Product> => {
    const response = await api.patch(`/products/${id}`, product);
    return response.data;
  },

  delete: async (id: string): Promise<void> => {
    await api.delete(`/products/${id}`);
  },
};

// Stores API
export const storesApi = {
  getAll: async (province?: string, city?: string): Promise<{ success: boolean; data: Store[] }> => {
    const response = await api.get('/stores', {
      params: { province, city },
    });
    return { success: true, data: response.data.stores || [] };
  },

  getById: async (id: string): Promise<Store> => {
    const response = await api.get(`/stores/${id}`);
    return response.data;
  },

  getProvinces: async (): Promise<ApiResponse<string[]>> => {
    const response = await api.get('/stores/meta/provinces');
    return response.data;
  },

  getCitiesByProvince: async (province: string): Promise<ApiResponse<string[]>> => {
    const response = await api.get(`/stores/meta/cities/${province}`);
    return response.data;
  },

  getChains: async (): Promise<ApiResponse<string[]>> => {
    const response = await api.get('/stores/chains');
    return response.data;
  },

  getBranchesByChain: async (chainName: string): Promise<ApiResponse<Store[]>> => {
    const response = await api.get(`/stores/chains/${chainName}/branches`);
    return response.data;
  },

  findNearby: async (
    longitude: number,
    latitude: number,
    maxDistance?: number
  ): Promise<ApiResponse<Store[]>> => {
    const response = await api.get('/stores/nearby', {
      params: { lng: longitude, lat: latitude, maxDistance },
    });
    return response.data;
  },

  create: async (store: Partial<Store>): Promise<Store> => {
    const response = await api.post('/stores', store);
    return response.data;
  },

  update: async (id: string, store: Partial<Store>): Promise<Store> => {
    const response = await api.patch(`/stores/${id}`, store);
    return response.data;
  },

  delete: async (id: string): Promise<void> => {
    await api.delete(`/stores/${id}`);
  },
};

// Prices API
export const pricesApi = {
  getAll: async (): Promise<ApiResponse<Price[]>> => {
    const response = await api.get('/prices');
    return response.data;
  },

  getById: async (id: string): Promise<Price> => {
    const response = await api.get(`/prices/${id}`);
    return response.data;
  },

  getBiggestSavings: async (limit?: number): Promise<{ success: boolean; data: SavingsItem[] }> => {
    const response = await api.get('/prices/biggest-savings', {
      params: { limit },
    });
    return { success: true, data: response.data.biggestSavings || [] };
  },

  getTrending: async (days?: number): Promise<{ success: boolean; data: TrendingProduct[] }> => {
    const response = await api.get('/prices/trending', {
      params: { days },
    });
    return { success: true, data: response.data.trending || [] };
  },

  getPromotions: async (limit?: number): Promise<ApiResponse<Price[]>> => {
    const response = await api.get('/prices/promotions', {
      params: { limit },
    });
    return response.data;
  },

  getProductPrices: async (productId: string): Promise<ApiResponse<Price[]>> => {
    const response = await api.get(`/prices/product/${productId}`);
    return response.data;
  },

  getStorePrices: async (storeId: string): Promise<ApiResponse<Price[]>> => {
    const response = await api.get(`/prices/store/${storeId}`);
    return response.data;
  },

  compareProductPrices: async (
    productId: string,
    latitude?: number,
    longitude?: number
  ): Promise<ApiResponse<Price[]>> => {
    const response = await api.get(`/prices/compare/${productId}`, {
      params: { lat: latitude, lng: longitude },
    });
    return response.data;
  },

  getPriceHistory: async (
    productId: string,
    storeId: string
  ): Promise<ApiResponse<any[]>> => {
    const response = await api.get(`/prices/history/${productId}/${storeId}`);
    return response.data;
  },

  create: async (price: Partial<Price>): Promise<Price> => {
    const response = await api.post('/prices', price);
    return response.data;
  },

  update: async (id: string, price: Partial<Price>): Promise<Price> => {
    const response = await api.patch(`/prices/${id}`, price);
    return response.data;
  },

  delete: async (id: string): Promise<void> => {
    await api.delete(`/prices/${id}`);
  },
};

// Health API
export const healthApi = {
  check: async (): Promise<ApiResponse<any>> => {
    const response = await api.get('/health');
    return response.data;
  },

  getInfo: async (): Promise<ApiResponse<any>> => {
    const response = await api.get('/');
    return response.data;
  },
};

export default api;
