# Database
MONGODB_URI=mongodb://localhost:27017/basketcase

# Server
PORT=5000
NODE_ENV=development

# Frontend
CLIENT_URL=http://localhost:3000

# Scraping Configuration
SCRAPING_ENABLED=true
SCRAPING_INTERVAL_MINUTES=30
SCRAPING_TIMEOUT_SECONDS=30

# SPAR Configuration
SPAR_BASE_URL=https://www.spar.co.za
SPAR_ENABLED=true

# Checkers Configuration
CHECKERS_BASE_URL=https://www.checkers.co.za
CHECKERS_ENABLED=true

# Pick n Pay Configuration
PICKNPAY_BASE_URL=https://www.pnp.co.za
PICKNPAY_ENABLED=false

# Woolworths Configuration
WOOLWORTHS_BASE_URL=https://www.woolworths.co.za
WOOLWORTHS_ENABLED=false

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
