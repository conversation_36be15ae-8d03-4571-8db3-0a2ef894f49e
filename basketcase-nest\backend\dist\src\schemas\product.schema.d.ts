import { Document } from 'mongoose';
export type ProductDocument = Product & Document;
export declare class Product {
    name: string;
    brand: string;
    category: string;
    description?: string;
    images: Array<{
        url: string;
        isPrimary: boolean;
        alt?: string;
    }>;
    barcode?: string;
    specifications?: {
        weight?: string;
        volume?: string;
        dimensions?: string;
        ingredients?: string[];
        nutritionalInfo?: Record<string, any>;
    };
    isActive: boolean;
    lastScraped?: Date;
    isRecentlyUpdated?: boolean;
}
export declare const ProductSchema: import("mongoose").Schema<Product, import("mongoose").Model<Product, any, any, any, Document<unknown, any, Product> & Product & {
    _id: import("mongoose").Types.ObjectId;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Product, Document<unknown, {}, import("mongoose").FlatRecord<Product>> & import("mongoose").FlatRecord<Product> & {
    _id: import("mongoose").Types.ObjectId;
}>;
