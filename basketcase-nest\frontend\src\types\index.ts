// Product Types
export interface Product {
  _id: string;
  name: string;
  brand: string;
  category: string;
  description?: string;
  images: ProductImage[];
  barcode?: string;
  specifications?: ProductSpecifications;
  isActive: boolean;
  lastScraped?: Date;
  isRecentlyUpdated?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductImage {
  url: string;
  isPrimary: boolean;
  alt?: string;
}

export interface ProductSpecifications {
  weight?: string;
  volume?: string;
  dimensions?: string;
  ingredients?: string[];
  nutritionalInfo?: Record<string, any>;
}

// Store Types
export interface Store {
  _id: string;
  name: string;
  branch: string;
  location?: StoreLocation;
  address: StoreAddress;
  contact?: StoreContact;
  operatingHours?: StoreOperatingHours;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface StoreLocation {
  type: string;
  coordinates: [number, number]; // [longitude, latitude]
}

export interface StoreAddress {
  street: string;
  city: string;
  province: string;
  postalCode?: string;
  country: string;
}

export interface StoreContact {
  phone?: string;
  email?: string;
  website?: string;
}

export interface StoreOperatingHours {
  monday?: { open: string; close: string };
  tuesday?: { open: string; close: string };
  wednesday?: { open: string; close: string };
  thursday?: { open: string; close: string };
  friday?: { open: string; close: string };
  saturday?: { open: string; close: string };
  sunday?: { open: string; close: string };
}

// Price Types
export interface Price {
  _id: string;
  product: Product | string;
  store: Store | string;
  current: number;
  original?: number;
  currency: string;
  availability?: PriceAvailability;
  promotion?: PricePromotion;
  priceHistory?: PriceHistoryEntry[];
  scrapingInfo?: ScrapingInfo;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PriceAvailability {
  inStock: boolean;
  stockLevel?: string;
  lastChecked?: Date;
}

export interface PricePromotion {
  isOnPromotion: boolean;
  promotionDescription?: string;
  promotionType?: string;
  validFrom?: Date;
  validUntil?: Date;
}

export interface PriceHistoryEntry {
  price: number;
  date: Date;
  source?: string;
}

export interface ScrapingInfo {
  lastScraped?: Date;
  sourceUrl?: string;
  scrapingMethod?: string;
  confidence?: number;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    current: number;
    total: number;
    totalItems: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Search and Filter Types
export interface ProductQuery {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  brand?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Savings Types
export interface SavingsItem {
  product: Product;
  store: Store;
  price: {
    current: number;
    original: number;
  };
  savings: {
    amount: number;
    percentage: number;
  };
  lastScraped?: Date;
  isRecentlyUpdated?: boolean;
}

// Trending Types
export interface TrendingProduct {
  _id: string;
  productName: string;
  avgPrice: number;
  minPrice: number;
  maxPrice: number;
  priceCount: number;
}

// Scraping Types
export interface ScrapingStatus {
  isRunning: boolean;
  lastRun?: Date;
  totalProductsScraped: number;
  availableScrapers: string[];
  nextScheduledRun?: Date;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface FilterState {
  search: string;
  category: string;
  brand: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}
