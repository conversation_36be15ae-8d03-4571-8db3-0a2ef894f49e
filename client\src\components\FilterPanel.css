/* Filter Panel Styles */
.filter-panel {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  height: fit-content;
  position: sticky;
  top: 100px;
}

.filter-panel.collapsed .filter-content {
  display: none;
}

.filter-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.filter-title {
  margin: 0;
  color: var(--dark-color);
  font-size: 1.25rem;
}

.filter-header-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.clear-filters-button {
  background: none;
  border: none;
  color: var(--danger-color);
  font-size: 0.85rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: var(--transition);
}

.clear-filters-button:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.collapse-button {
  background: none;
  border: none;
  color: var(--dark-color);
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
  transition: var(--transition);
}

.collapse-button:hover {
  color: var(--primary-color);
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 0.9rem;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: var(--transition);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.filter-select:disabled {
  background-color: var(--secondary-color);
  cursor: not-allowed;
  opacity: 0.6;
}

.filter-loading {
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
  padding: 0.5rem;
}

.filter-hint {
  font-size: 0.8rem;
  color: #6c757d;
  font-style: italic;
  margin-top: 0.25rem;
}

.active-filters {
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.active-filters-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.75rem;
}

.active-filters-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.active-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: var(--secondary-color);
  border-radius: var(--border-radius);
  font-size: 0.85rem;
}

.filter-type {
  font-weight: 600;
  color: var(--primary-color);
}

.filter-value {
  color: var(--dark-color);
  flex: 1;
}

.remove-filter {
  background: none;
  border: none;
  color: var(--danger-color);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition);
}

.remove-filter:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.filter-tips {
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.tips-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 0.8rem;
  color: #6c757d;
}

.tips-list li {
  margin-bottom: 0.25rem;
  padding-left: 1rem;
  position: relative;
}

.tips-list li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .filter-panel {
    position: static;
    margin-bottom: 1rem;
  }
  
  .filter-content {
    gap: 1rem;
  }
  
  .active-filters-list {
    gap: 0.25rem;
  }
  
  .active-filter {
    padding: 0.4rem;
    font-size: 0.8rem;
  }
}
