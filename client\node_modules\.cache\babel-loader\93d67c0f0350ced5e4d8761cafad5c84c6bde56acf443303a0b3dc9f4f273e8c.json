{"ast": null, "code": "var _jsxFileName = \"c:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { productsAPI, pricesAPI } from '../services/api';\nimport ProductGrid from '../components/ProductGrid';\nimport FilterPanel from '../components/FilterPanel';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport './HomePage.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState(null);\n  const [trendingProducts, setTrendingProducts] = useState([]);\n  const [promotions, setPromotions] = useState([]);\n  const [biggestSavings, setBiggestSavings] = useState([]);\n\n  // Filter states\n  const [filters, setFilters] = useState({\n    query: searchParams.get('q') || '',\n    category: searchParams.get('category') || '',\n    brand: searchParams.get('brand') || '',\n    sortBy: searchParams.get('sortBy') || 'relevance',\n    page: parseInt(searchParams.get('page')) || 1\n  });\n\n  // Load products based on current filters\n  const loadProducts = async (currentFilters = filters) => {\n    setLoading(true);\n    setError(null);\n    try {\n      const searchFilters = {\n        ...currentFilters,\n        limit: 20\n      };\n\n      // Remove empty filters\n      Object.keys(searchFilters).forEach(key => {\n        if (!searchFilters[key]) {\n          delete searchFilters[key];\n        }\n      });\n      const response = await productsAPI.search(searchFilters);\n      setProducts(response.products || []);\n      setPagination(response.pagination);\n    } catch (err) {\n      setError('Failed to load products. Please try again.');\n      console.error('Error loading products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load trending products\n  const loadTrendingProducts = async () => {\n    try {\n      const response = await pricesAPI.getTrending(7);\n      setTrendingProducts(response.trending || []);\n    } catch (err) {\n      console.error('Error loading trending products:', err);\n    }\n  };\n\n  // Load current promotions\n  const loadPromotions = async () => {\n    try {\n      const response = await pricesAPI.getPromotions({\n        limit: 10\n      });\n      setPromotions(response.promotions || []);\n    } catch (err) {\n      console.error('Error loading promotions:', err);\n    }\n  };\n\n  // Load biggest savings\n  const loadBiggestSavings = async () => {\n    try {\n      const response = await pricesAPI.getAnalytics({\n        type: 'biggest_savings',\n        limit: 6\n      });\n      setBiggestSavings(response.biggestSavings || []);\n    } catch (err) {\n      console.error('Error loading biggest savings:', err);\n    }\n  };\n\n  // Update URL when filters change\n  useEffect(() => {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value && value !== 'relevance' && value !== 1) {\n        params.set(key, value.toString());\n      }\n    });\n    setSearchParams(params);\n  }, [filters, setSearchParams]);\n\n  // Load products when filters change\n  useEffect(() => {\n    loadProducts();\n  }, [filters]);\n\n  // Load initial data\n  useEffect(() => {\n    loadTrendingProducts();\n    loadPromotions();\n    loadBiggestSavings();\n  }, []);\n\n  // Handle filter changes\n  const handleFilterChange = newFilters => {\n    setFilters(prev => ({\n      ...prev,\n      ...newFilters,\n      page: 1 // Reset to first page when filters change\n    }));\n  };\n\n  // Handle page change\n  const handlePageChange = page => {\n    setFilters(prev => ({\n      ...prev,\n      page\n    }));\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  // Handle product click\n  const handleProductClick = product => {\n    navigate(`/product/${product._id}`);\n  };\n\n  // Check if we have active search/filters\n  const hasActiveFilters = filters.query || filters.category || filters.brand;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-container\",\n      children: [!hasActiveFilters && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: \"Compare Grocery Prices Across South Africa\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: \"Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n          className: \"filters-sidebar\",\n          children: /*#__PURE__*/_jsxDEV(FilterPanel, {\n            filters: filters,\n            onFilterChange: handleFilterChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"products-section\",\n          children: [hasActiveFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-results-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"results-title\",\n              children: filters.query ? `Search results for \"${filters.query}\"` : 'Products'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), pagination && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"results-info\",\n              children: [\"Showing \", (pagination.current - 1) * 20 + 1, \" - \", Math.min(pagination.current * 20, pagination.totalItems), \" of \", pagination.totalItems, \" products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 25\n          }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n            message: error,\n            onRetry: () => loadProducts()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 23\n          }, this), !loading && !error && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [products.length > 0 ? /*#__PURE__*/_jsxDEV(ProductGrid, {\n              products: products,\n              onProductClick: handleProductClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this) : hasActiveFilters ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-results\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No products found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Try adjusting your search criteria or filters.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 19\n            }, this) : null, pagination && pagination.total > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pagination\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pagination-button\",\n                disabled: pagination.current <= 1,\n                onClick: () => handlePageChange(pagination.current - 1),\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"pagination-info\",\n                children: [\"Page \", pagination.current, \" of \", pagination.total]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pagination-button\",\n                disabled: pagination.current >= pagination.total,\n                onClick: () => handlePageChange(pagination.current + 1),\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), !hasActiveFilters && !loading && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"featured-sections\",\n        children: [trendingProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"featured-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"Trending Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trending-grid\",\n            children: trendingProducts.slice(0, 6).map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"trending-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"trending-product-name\",\n                children: item.productName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trending-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"avg-price\",\n                  children: [\"Avg: R\", item.avgPrice.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-range\",\n                  children: [\"R\", item.minPrice.toFixed(2), \" - R\", item.maxPrice.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 23\n              }, this)]\n            }, item._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 15\n        }, this), promotions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"featured-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"Current Promotions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"promotions-grid\",\n            children: promotions.slice(0, 8).map(promo => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-product\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: promo.product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"promotion-store\",\n                  children: promo.store.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"current-price\",\n                  children: [\"R\", promo.price.current.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 25\n                }, this), promo.price.original > promo.price.current && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"original-price\",\n                  children: [\"R\", promo.price.original.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 23\n              }, this), promo.promotion.promotionDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-description\",\n                children: promo.promotion.promotionDescription\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 25\n              }, this)]\n            }, `${promo.product._id}-${promo.store._id}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"7f50XSA1s/ADvsOM98n5SNUBYoA=\", false, function () {\n  return [useSearchParams, useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "useNavigate", "productsAPI", "pricesAPI", "ProductGrid", "FilterPanel", "LoadingSpinner", "ErrorMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HomePage", "_s", "searchParams", "setSearchParams", "navigate", "products", "setProducts", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "trendingProducts", "setTrendingProducts", "promotions", "setPromotions", "biggestSavings", "setBiggestSavings", "filters", "setFilters", "query", "get", "category", "brand", "sortBy", "page", "parseInt", "loadProducts", "currentFilters", "searchFilters", "limit", "Object", "keys", "for<PERSON>ach", "key", "response", "search", "err", "console", "loadTrendingProducts", "getTrending", "trending", "loadPromotions", "getPromotions", "loadBiggestSavings", "getAnalytics", "type", "params", "URLSearchParams", "entries", "value", "set", "toString", "handleFilterChange", "newFilters", "prev", "handlePageChange", "window", "scrollTo", "top", "behavior", "handleProductClick", "product", "_id", "hasActiveFilters", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onFilterChange", "current", "Math", "min", "totalItems", "message", "onRetry", "length", "onProductClick", "total", "disabled", "onClick", "slice", "map", "item", "productName", "avgPrice", "toFixed", "minPrice", "maxPrice", "promo", "name", "store", "price", "original", "promotion", "promotionDescription", "_c", "$RefreshReg$"], "sources": ["c:/laragon/www/basketcase/client/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { productsAPI, pricesAPI } from '../services/api';\nimport ProductGrid from '../components/ProductGrid';\nimport FilterPanel from '../components/FilterPanel';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport './HomePage.css';\n\nconst HomePage = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const navigate = useNavigate();\n  \n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState(null);\n  const [trendingProducts, setTrendingProducts] = useState([]);\n  const [promotions, setPromotions] = useState([]);\n  const [biggestSavings, setBiggestSavings] = useState([]);\n\n  // Filter states\n  const [filters, setFilters] = useState({\n    query: searchParams.get('q') || '',\n    category: searchParams.get('category') || '',\n    brand: searchParams.get('brand') || '',\n    sortBy: searchParams.get('sortBy') || 'relevance',\n    page: parseInt(searchParams.get('page')) || 1\n  });\n\n  // Load products based on current filters\n  const loadProducts = async (currentFilters = filters) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const searchFilters = {\n        ...currentFilters,\n        limit: 20\n      };\n      \n      // Remove empty filters\n      Object.keys(searchFilters).forEach(key => {\n        if (!searchFilters[key]) {\n          delete searchFilters[key];\n        }\n      });\n      \n      const response = await productsAPI.search(searchFilters);\n      setProducts(response.products || []);\n      setPagination(response.pagination);\n      \n    } catch (err) {\n      setError('Failed to load products. Please try again.');\n      console.error('Error loading products:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load trending products\n  const loadTrendingProducts = async () => {\n    try {\n      const response = await pricesAPI.getTrending(7);\n      setTrendingProducts(response.trending || []);\n    } catch (err) {\n      console.error('Error loading trending products:', err);\n    }\n  };\n\n  // Load current promotions\n  const loadPromotions = async () => {\n    try {\n      const response = await pricesAPI.getPromotions({ limit: 10 });\n      setPromotions(response.promotions || []);\n    } catch (err) {\n      console.error('Error loading promotions:', err);\n    }\n  };\n\n  // Load biggest savings\n  const loadBiggestSavings = async () => {\n    try {\n      const response = await pricesAPI.getAnalytics({\n        type: 'biggest_savings',\n        limit: 6\n      });\n      setBiggestSavings(response.biggestSavings || []);\n    } catch (err) {\n      console.error('Error loading biggest savings:', err);\n    }\n  };\n\n  // Update URL when filters change\n  useEffect(() => {\n    const params = new URLSearchParams();\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value && value !== 'relevance' && value !== 1) {\n        params.set(key, value.toString());\n      }\n    });\n    \n    setSearchParams(params);\n  }, [filters, setSearchParams]);\n\n  // Load products when filters change\n  useEffect(() => {\n    loadProducts();\n  }, [filters]);\n\n  // Load initial data\n  useEffect(() => {\n    loadTrendingProducts();\n    loadPromotions();\n    loadBiggestSavings();\n  }, []);\n\n  // Handle filter changes\n  const handleFilterChange = (newFilters) => {\n    setFilters(prev => ({\n      ...prev,\n      ...newFilters,\n      page: 1 // Reset to first page when filters change\n    }));\n  };\n\n  // Handle page change\n  const handlePageChange = (page) => {\n    setFilters(prev => ({ ...prev, page }));\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  // Handle product click\n  const handleProductClick = (product) => {\n    navigate(`/product/${product._id}`);\n  };\n\n  // Check if we have active search/filters\n  const hasActiveFilters = filters.query || filters.category || filters.brand;\n\n  return (\n    <div className=\"home-page\">\n      <div className=\"home-container\">\n        \n        {/* Hero Section - only show when no active search */}\n        {!hasActiveFilters && (\n          <section className=\"hero-section\">\n            <div className=\"hero-content\">\n              <h1 className=\"hero-title\">\n                Compare Grocery Prices Across South Africa\n              </h1>\n              <p className=\"hero-subtitle\">\n                Find the best deals from SPAR, Checkers, Pick n Pay, and Woolworths\n              </p>\n            </div>\n          </section>\n        )}\n\n        <div className=\"main-content\">\n          {/* Filter Panel */}\n          <aside className=\"filters-sidebar\">\n            <FilterPanel\n              filters={filters}\n              onFilterChange={handleFilterChange}\n            />\n          </aside>\n\n          {/* Products Section */}\n          <main className=\"products-section\">\n            \n            {/* Search Results Header */}\n            {hasActiveFilters && (\n              <div className=\"search-results-header\">\n                <h2 className=\"results-title\">\n                  {filters.query ? `Search results for \"${filters.query}\"` : 'Products'}\n                </h2>\n                \n                {pagination && (\n                  <div className=\"results-info\">\n                    Showing {((pagination.current - 1) * 20) + 1} - {Math.min(pagination.current * 20, pagination.totalItems)} of {pagination.totalItems} products\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* Loading State */}\n            {loading && <LoadingSpinner />}\n\n            {/* Error State */}\n            {error && <ErrorMessage message={error} onRetry={() => loadProducts()} />}\n\n            {/* Products Grid */}\n            {!loading && !error && (\n              <>\n                {products.length > 0 ? (\n                  <ProductGrid\n                    products={products}\n                    onProductClick={handleProductClick}\n                  />\n                ) : hasActiveFilters ? (\n                  <div className=\"no-results\">\n                    <h3>No products found</h3>\n                    <p>Try adjusting your search criteria or filters.</p>\n                  </div>\n                ) : null}\n\n                {/* Pagination */}\n                {pagination && pagination.total > 1 && (\n                  <div className=\"pagination\">\n                    <button\n                      className=\"pagination-button\"\n                      disabled={pagination.current <= 1}\n                      onClick={() => handlePageChange(pagination.current - 1)}\n                    >\n                      Previous\n                    </button>\n                    \n                    <span className=\"pagination-info\">\n                      Page {pagination.current} of {pagination.total}\n                    </span>\n                    \n                    <button\n                      className=\"pagination-button\"\n                      disabled={pagination.current >= pagination.total}\n                      onClick={() => handlePageChange(pagination.current + 1)}\n                    >\n                      Next\n                    </button>\n                  </div>\n                )}\n              </>\n            )}\n          </main>\n        </div>\n\n        {/* Trending and Promotions - only show when no active search */}\n        {!hasActiveFilters && !loading && (\n          <section className=\"featured-sections\">\n            \n            {/* Trending Products */}\n            {trendingProducts.length > 0 && (\n              <div className=\"featured-section\">\n                <h2 className=\"section-title\">Trending Products</h2>\n                <div className=\"trending-grid\">\n                  {trendingProducts.slice(0, 6).map(item => (\n                    <div key={item._id} className=\"trending-item\">\n                      <h3 className=\"trending-product-name\">{item.productName}</h3>\n                      <div className=\"trending-stats\">\n                        <span className=\"avg-price\">Avg: R{item.avgPrice.toFixed(2)}</span>\n                        <span className=\"price-range\">\n                          R{item.minPrice.toFixed(2)} - R{item.maxPrice.toFixed(2)}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Current Promotions */}\n            {promotions.length > 0 && (\n              <div className=\"featured-section\">\n                <h2 className=\"section-title\">Current Promotions</h2>\n                <div className=\"promotions-grid\">\n                  {promotions.slice(0, 8).map(promo => (\n                    <div key={`${promo.product._id}-${promo.store._id}`} className=\"promotion-item\">\n                      <div className=\"promotion-product\">\n                        <h4>{promo.product.name}</h4>\n                        <span className=\"promotion-store\">{promo.store.name}</span>\n                      </div>\n                      <div className=\"promotion-price\">\n                        <span className=\"current-price\">R{promo.price.current.toFixed(2)}</span>\n                        {promo.price.original > promo.price.current && (\n                          <span className=\"original-price\">R{promo.price.original.toFixed(2)}</span>\n                        )}\n                      </div>\n                      {promo.promotion.promotionDescription && (\n                        <div className=\"promotion-description\">\n                          {promo.promotion.promotionDescription}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </section>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,WAAW,EAAEC,SAAS,QAAQ,iBAAiB;AACxD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGf,eAAe,CAAC,CAAC;EACzD,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC;IACrCmC,KAAK,EAAEnB,YAAY,CAACoB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE;IAClCC,QAAQ,EAAErB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;IAC5CE,KAAK,EAAEtB,YAAY,CAACoB,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;IACtCG,MAAM,EAAEvB,YAAY,CAACoB,GAAG,CAAC,QAAQ,CAAC,IAAI,WAAW;IACjDI,IAAI,EAAEC,QAAQ,CAACzB,YAAY,CAACoB,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI;EAC9C,CAAC,CAAC;;EAEF;EACA,MAAMM,YAAY,GAAG,MAAAA,CAAOC,cAAc,GAAGV,OAAO,KAAK;IACvDX,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMoB,aAAa,GAAG;QACpB,GAAGD,cAAc;QACjBE,KAAK,EAAE;MACT,CAAC;;MAED;MACAC,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;QACxC,IAAI,CAACL,aAAa,CAACK,GAAG,CAAC,EAAE;UACvB,OAAOL,aAAa,CAACK,GAAG,CAAC;QAC3B;MACF,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,MAAM9C,WAAW,CAAC+C,MAAM,CAACP,aAAa,CAAC;MACxDxB,WAAW,CAAC8B,QAAQ,CAAC/B,QAAQ,IAAI,EAAE,CAAC;MACpCO,aAAa,CAACwB,QAAQ,CAACzB,UAAU,CAAC;IAEpC,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ5B,QAAQ,CAAC,4CAA4C,CAAC;MACtD6B,OAAO,CAAC9B,KAAK,CAAC,yBAAyB,EAAE6B,GAAG,CAAC;IAC/C,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAM7C,SAAS,CAACkD,WAAW,CAAC,CAAC,CAAC;MAC/C3B,mBAAmB,CAACsB,QAAQ,CAACM,QAAQ,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,kCAAkC,EAAE6B,GAAG,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAM7C,SAAS,CAACqD,aAAa,CAAC;QAAEb,KAAK,EAAE;MAAG,CAAC,CAAC;MAC7Df,aAAa,CAACoB,QAAQ,CAACrB,UAAU,IAAI,EAAE,CAAC;IAC1C,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,2BAA2B,EAAE6B,GAAG,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAM7C,SAAS,CAACuD,YAAY,CAAC;QAC5CC,IAAI,EAAE,iBAAiB;QACvBhB,KAAK,EAAE;MACT,CAAC,CAAC;MACFb,iBAAiB,CAACkB,QAAQ,CAACnB,cAAc,IAAI,EAAE,CAAC;IAClD,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,gCAAgC,EAAE6B,GAAG,CAAC;IACtD;EACF,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACd,MAAM6D,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpCjB,MAAM,CAACkB,OAAO,CAAC/B,OAAO,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEgB,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,IAAIA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,CAAC,EAAE;QACjDH,MAAM,CAACI,GAAG,CAACjB,GAAG,EAAEgB,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;IAEFlD,eAAe,CAAC6C,MAAM,CAAC;EACzB,CAAC,EAAE,CAAC7B,OAAO,EAAEhB,eAAe,CAAC,CAAC;;EAE9B;EACAhB,SAAS,CAAC,MAAM;IACdyC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACT,OAAO,CAAC,CAAC;;EAEb;EACAhC,SAAS,CAAC,MAAM;IACdqD,oBAAoB,CAAC,CAAC;IACtBG,cAAc,CAAC,CAAC;IAChBE,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,kBAAkB,GAAIC,UAAU,IAAK;IACzCnC,UAAU,CAACoC,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,GAAGD,UAAU;MACb7B,IAAI,EAAE,CAAC,CAAC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM+B,gBAAgB,GAAI/B,IAAI,IAAK;IACjCN,UAAU,CAACoC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9B;IAAK,CAAC,CAAC,CAAC;IACvCgC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;IACtC3D,QAAQ,CAAC,YAAY2D,OAAO,CAACC,GAAG,EAAE,CAAC;EACrC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG9C,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACI,QAAQ,IAAIJ,OAAO,CAACK,KAAK;EAE3E,oBACE3B,OAAA;IAAKqE,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBtE,OAAA;MAAKqE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAG5B,CAACF,gBAAgB,iBAChBpE,OAAA;QAASqE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC/BtE,OAAA;UAAKqE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtE,OAAA;YAAIqE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1E,OAAA;YAAGqE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV,eAED1E,OAAA;QAAKqE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE3BtE,OAAA;UAAOqE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAChCtE,OAAA,CAACJ,WAAW;YACV0B,OAAO,EAAEA,OAAQ;YACjBqD,cAAc,EAAElB;UAAmB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGR1E,OAAA;UAAMqE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAG/BF,gBAAgB,iBACfpE,OAAA;YAAKqE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCtE,OAAA;cAAIqE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1BhD,OAAO,CAACE,KAAK,GAAG,uBAAuBF,OAAO,CAACE,KAAK,GAAG,GAAG;YAAU;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,EAEJ5D,UAAU,iBACTd,OAAA;cAAKqE,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAC,UACpB,EAAE,CAACxD,UAAU,CAAC8D,OAAO,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,EAAC,KAAG,EAACC,IAAI,CAACC,GAAG,CAAChE,UAAU,CAAC8D,OAAO,GAAG,EAAE,EAAE9D,UAAU,CAACiE,UAAU,CAAC,EAAC,MAAI,EAACjE,UAAU,CAACiE,UAAU,EAAC,WACvI;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGAhE,OAAO,iBAAIV,OAAA,CAACH,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAG7B9D,KAAK,iBAAIZ,OAAA,CAACF,YAAY;YAACkF,OAAO,EAAEpE,KAAM;YAACqE,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAAC;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAGxE,CAAChE,OAAO,IAAI,CAACE,KAAK,iBACjBZ,OAAA,CAAAE,SAAA;YAAAoE,QAAA,GACG9D,QAAQ,CAAC0E,MAAM,GAAG,CAAC,gBAClBlF,OAAA,CAACL,WAAW;cACVa,QAAQ,EAAEA,QAAS;cACnB2E,cAAc,EAAElB;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,GACAN,gBAAgB,gBAClBpE,OAAA;cAAKqE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtE,OAAA;gBAAAsE,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B1E,OAAA;gBAAAsE,QAAA,EAAG;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,GACJ,IAAI,EAGP5D,UAAU,IAAIA,UAAU,CAACsE,KAAK,GAAG,CAAC,iBACjCpF,OAAA;cAAKqE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtE,OAAA;gBACEqE,SAAS,EAAC,mBAAmB;gBAC7BgB,QAAQ,EAAEvE,UAAU,CAAC8D,OAAO,IAAI,CAAE;gBAClCU,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC9C,UAAU,CAAC8D,OAAO,GAAG,CAAC,CAAE;gBAAAN,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET1E,OAAA;gBAAMqE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,OAC3B,EAACxD,UAAU,CAAC8D,OAAO,EAAC,MAAI,EAAC9D,UAAU,CAACsE,KAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAEP1E,OAAA;gBACEqE,SAAS,EAAC,mBAAmB;gBAC7BgB,QAAQ,EAAEvE,UAAU,CAAC8D,OAAO,IAAI9D,UAAU,CAACsE,KAAM;gBACjDE,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC9C,UAAU,CAAC8D,OAAO,GAAG,CAAC,CAAE;gBAAAN,QAAA,EACzD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGL,CAACN,gBAAgB,IAAI,CAAC1D,OAAO,iBAC5BV,OAAA;QAASqE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAGnCtD,gBAAgB,CAACkE,MAAM,GAAG,CAAC,iBAC1BlF,OAAA;UAAKqE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BtE,OAAA;YAAIqE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpD1E,OAAA;YAAKqE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BtD,gBAAgB,CAACuE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,IAAI,iBACpCzF,OAAA;cAAoBqE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC3CtE,OAAA;gBAAIqE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEmB,IAAI,CAACC;cAAW;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D1E,OAAA;gBAAKqE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BtE,OAAA;kBAAMqE,SAAS,EAAC,WAAW;kBAAAC,QAAA,GAAC,QAAM,EAACmB,IAAI,CAACE,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnE1E,OAAA;kBAAMqE,SAAS,EAAC,aAAa;kBAAAC,QAAA,GAAC,GAC3B,EAACmB,IAAI,CAACI,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI,EAACH,IAAI,CAACK,QAAQ,CAACF,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAPEe,IAAI,CAACtB,GAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAxD,UAAU,CAACgE,MAAM,GAAG,CAAC,iBACpBlF,OAAA;UAAKqE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BtE,OAAA;YAAIqE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD1E,OAAA;YAAKqE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BpD,UAAU,CAACqE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACO,KAAK,iBAC/B/F,OAAA;cAAqDqE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7EtE,OAAA;gBAAKqE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCtE,OAAA;kBAAAsE,QAAA,EAAKyB,KAAK,CAAC7B,OAAO,CAAC8B;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7B1E,OAAA;kBAAMqE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEyB,KAAK,CAACE,KAAK,CAACD;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACN1E,OAAA;gBAAKqE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BtE,OAAA;kBAAMqE,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,GAAC,EAACyB,KAAK,CAACG,KAAK,CAACtB,OAAO,CAACgB,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvEqB,KAAK,CAACG,KAAK,CAACC,QAAQ,GAAGJ,KAAK,CAACG,KAAK,CAACtB,OAAO,iBACzC5E,OAAA;kBAAMqE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,GAAC,EAACyB,KAAK,CAACG,KAAK,CAACC,QAAQ,CAACP,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EACLqB,KAAK,CAACK,SAAS,CAACC,oBAAoB,iBACnCrG,OAAA;gBAAKqE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCyB,KAAK,CAACK,SAAS,CAACC;cAAoB;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CACN;YAAA,GAfO,GAAGqB,KAAK,CAAC7B,OAAO,CAACC,GAAG,IAAI4B,KAAK,CAACE,KAAK,CAAC9B,GAAG,EAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgB9C,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CA3RID,QAAQ;EAAA,QAC4BZ,eAAe,EACtCC,WAAW;AAAA;AAAA8G,EAAA,GAFxBnG,QAAQ;AA6Rd,eAAeA,QAAQ;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}