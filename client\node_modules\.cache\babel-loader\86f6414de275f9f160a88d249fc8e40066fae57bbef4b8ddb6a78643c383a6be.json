{"ast": null, "code": "var _jsxFileName = \"c:\\\\laragon\\\\www\\\\basketcase\\\\client\\\\src\\\\components\\\\SearchBar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { productsAPI, apiUtils } from '../services/api';\nimport './SearchBar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchBar = ({\n  onSearch,\n  initialQuery = '',\n  placeholder = 'Search for products...'\n}) => {\n  _s();\n  const [query, setQuery] = useState(initialQuery);\n  const [suggestions, setSuggestions] = useState([]);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const searchRef = useRef(null);\n  const suggestionsRef = useRef(null);\n\n  // Debounced search for suggestions\n  const debouncedSearch = useRef(apiUtils.debounce(async searchQuery => {\n    if (searchQuery.length < 2) {\n      setSuggestions([]);\n      setShowSuggestions(false);\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await productsAPI.search({\n        q: searchQuery,\n        limit: 8\n      });\n      setSuggestions(response.products || []);\n      setShowSuggestions(true);\n    } catch (error) {\n      console.error('Error fetching suggestions:', error);\n      setSuggestions([]);\n    } finally {\n      setLoading(false);\n    }\n  }, 300)).current;\n  useEffect(() => {\n    if (query) {\n      debouncedSearch(query);\n    } else {\n      setSuggestions([]);\n      setShowSuggestions(false);\n    }\n  }, [query, debouncedSearch]);\n\n  // Handle input change\n  const handleInputChange = e => {\n    const value = e.target.value;\n    setQuery(value);\n    setSelectedIndex(-1);\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (query.trim()) {\n      performSearch(query.trim());\n    }\n  };\n\n  // Perform search\n  const performSearch = searchQuery => {\n    var _searchRef$current;\n    onSearch(searchQuery, {});\n    setShowSuggestions(false);\n    setSelectedIndex(-1);\n    (_searchRef$current = searchRef.current) === null || _searchRef$current === void 0 ? void 0 : _searchRef$current.blur();\n  };\n\n  // Handle suggestion click\n  const handleSuggestionClick = product => {\n    setQuery(product.name);\n    performSearch(product.name);\n  };\n\n  // Handle keyboard navigation\n  const handleKeyDown = e => {\n    var _searchRef$current2;\n    if (!showSuggestions || suggestions.length === 0) return;\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => prev < suggestions.length - 1 ? prev + 1 : prev);\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {\n          handleSuggestionClick(suggestions[selectedIndex]);\n        } else {\n          handleSubmit(e);\n        }\n        break;\n      case 'Escape':\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n        (_searchRef$current2 = searchRef.current) === null || _searchRef$current2 === void 0 ? void 0 : _searchRef$current2.blur();\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Handle click outside to close suggestions\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (searchRef.current && !searchRef.current.contains(event.target) && suggestionsRef.current && !suggestionsRef.current.contains(event.target)) {\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-bar\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"search-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-input-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          ref: searchRef,\n          type: \"text\",\n          value: query,\n          onChange: handleInputChange,\n          onKeyDown: handleKeyDown,\n          onFocus: () => query && setShowSuggestions(true),\n          placeholder: placeholder,\n          className: \"search-input\",\n          autoComplete: \"off\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"search-button\",\n          disabled: !query.trim(),\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-loading\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), showSuggestions && suggestions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: suggestionsRef,\n        className: \"suggestions-dropdown\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"suggestions-list\",\n          children: suggestions.map((product, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: `suggestion-item ${index === selectedIndex ? 'selected' : ''}`,\n            onClick: () => handleSuggestionClick(product),\n            onMouseEnter: () => setSelectedIndex(index),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"suggestion-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"suggestion-name\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this), product.brand && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"suggestion-brand\",\n                children: product.brand\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"suggestion-category\",\n                children: product.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this)\n          }, product._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchBar, \"S/mx1Ga6Xl7ezTmMnAOCEgME31A=\");\n_c = SearchBar;\nexport default SearchBar;\nvar _c;\n$RefreshReg$(_c, \"SearchBar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "productsAPI", "apiUtils", "jsxDEV", "_jsxDEV", "SearchBar", "onSearch", "initialQuery", "placeholder", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "suggestions", "setSuggestions", "showSuggestions", "setShowSuggestions", "loading", "setLoading", "selectedIndex", "setSelectedIndex", "searchRef", "suggestionsRef", "debouncedSearch", "debounce", "searchQuery", "length", "response", "search", "q", "limit", "products", "error", "console", "current", "handleInputChange", "e", "value", "target", "handleSubmit", "preventDefault", "trim", "performSearch", "_searchRef$current", "blur", "handleSuggestionClick", "product", "name", "handleKeyDown", "_searchRef$current2", "key", "prev", "handleClickOutside", "event", "contains", "document", "addEventListener", "removeEventListener", "className", "children", "onSubmit", "ref", "type", "onChange", "onKeyDown", "onFocus", "autoComplete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "map", "index", "onClick", "onMouseEnter", "brand", "category", "_id", "_c", "$RefreshReg$"], "sources": ["c:/laragon/www/basketcase/client/src/components/SearchBar.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { productsAPI, apiUtils } from '../services/api';\nimport './SearchBar.css';\n\nconst SearchBar = ({ onSearch, initialQuery = '', placeholder = 'Search for products...' }) => {\n  const [query, setQuery] = useState(initialQuery);\n  const [suggestions, setSuggestions] = useState([]);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  \n  const searchRef = useRef(null);\n  const suggestionsRef = useRef(null);\n\n  // Debounced search for suggestions\n  const debouncedSearch = useRef(\n    apiUtils.debounce(async (searchQuery) => {\n      if (searchQuery.length < 2) {\n        setSuggestions([]);\n        setShowSuggestions(false);\n        return;\n      }\n\n      setLoading(true);\n      try {\n        const response = await productsAPI.search({\n          q: searchQuery,\n          limit: 8\n        });\n        \n        setSuggestions(response.products || []);\n        setShowSuggestions(true);\n      } catch (error) {\n        console.error('Error fetching suggestions:', error);\n        setSuggestions([]);\n      } finally {\n        setLoading(false);\n      }\n    }, 300)\n  ).current;\n\n  useEffect(() => {\n    if (query) {\n      debouncedSearch(query);\n    } else {\n      setSuggestions([]);\n      setShowSuggestions(false);\n    }\n  }, [query, debouncedSearch]);\n\n  // Handle input change\n  const handleInputChange = (e) => {\n    const value = e.target.value;\n    setQuery(value);\n    setSelectedIndex(-1);\n  };\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (query.trim()) {\n      performSearch(query.trim());\n    }\n  };\n\n  // Perform search\n  const performSearch = (searchQuery) => {\n    onSearch(searchQuery, {});\n    setShowSuggestions(false);\n    setSelectedIndex(-1);\n    searchRef.current?.blur();\n  };\n\n  // Handle suggestion click\n  const handleSuggestionClick = (product) => {\n    setQuery(product.name);\n    performSearch(product.name);\n  };\n\n  // Handle keyboard navigation\n  const handleKeyDown = (e) => {\n    if (!showSuggestions || suggestions.length === 0) return;\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => \n          prev < suggestions.length - 1 ? prev + 1 : prev\n        );\n        break;\n      \n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);\n        break;\n      \n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {\n          handleSuggestionClick(suggestions[selectedIndex]);\n        } else {\n          handleSubmit(e);\n        }\n        break;\n      \n      case 'Escape':\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n        searchRef.current?.blur();\n        break;\n      \n      default:\n        break;\n    }\n  };\n\n  // Handle click outside to close suggestions\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (\n        searchRef.current && \n        !searchRef.current.contains(event.target) &&\n        suggestionsRef.current &&\n        !suggestionsRef.current.contains(event.target)\n      ) {\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  return (\n    <div className=\"search-bar\">\n      <form onSubmit={handleSubmit} className=\"search-form\">\n        <div className=\"search-input-container\">\n          <input\n            ref={searchRef}\n            type=\"text\"\n            value={query}\n            onChange={handleInputChange}\n            onKeyDown={handleKeyDown}\n            onFocus={() => query && setShowSuggestions(true)}\n            placeholder={placeholder}\n            className=\"search-input\"\n            autoComplete=\"off\"\n          />\n          \n          <button \n            type=\"submit\" \n            className=\"search-button\"\n            disabled={!query.trim()}\n          >\n            🔍\n          </button>\n          \n          {loading && (\n            <div className=\"search-loading\">\n              <div className=\"spinner\"></div>\n            </div>\n          )}\n        </div>\n\n        {/* Suggestions Dropdown */}\n        {showSuggestions && suggestions.length > 0 && (\n          <div ref={suggestionsRef} className=\"suggestions-dropdown\">\n            <ul className=\"suggestions-list\">\n              {suggestions.map((product, index) => (\n                <li\n                  key={product._id}\n                  className={`suggestion-item ${\n                    index === selectedIndex ? 'selected' : ''\n                  }`}\n                  onClick={() => handleSuggestionClick(product)}\n                  onMouseEnter={() => setSelectedIndex(index)}\n                >\n                  <div className=\"suggestion-content\">\n                    <div className=\"suggestion-name\">{product.name}</div>\n                    {product.brand && (\n                      <div className=\"suggestion-brand\">{product.brand}</div>\n                    )}\n                    <div className=\"suggestion-category\">{product.category}</div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </form>\n    </div>\n  );\n};\n\nexport default SearchBar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,QAAQ,QAAQ,iBAAiB;AACvD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG,EAAE;EAAEC,WAAW,GAAG;AAAyB,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAACS,YAAY,CAAC;EAChD,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtD,MAAMsB,SAAS,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqB,cAAc,GAAGrB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMsB,eAAe,GAAGtB,MAAM,CAC5BE,QAAQ,CAACqB,QAAQ,CAAC,MAAOC,WAAW,IAAK;IACvC,IAAIA,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1BZ,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,KAAK,CAAC;MACzB;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMzB,WAAW,CAAC0B,MAAM,CAAC;QACxCC,CAAC,EAAEJ,WAAW;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;MAEFhB,cAAc,CAACa,QAAQ,CAACI,QAAQ,IAAI,EAAE,CAAC;MACvCf,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDlB,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,SAAS;MACRI,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,GAAG,CACR,CAAC,CAACgB,OAAO;EAETlC,SAAS,CAAC,MAAM;IACd,IAAIW,KAAK,EAAE;MACTY,eAAe,CAACZ,KAAK,CAAC;IACxB,CAAC,MAAM;MACLG,cAAc,CAAC,EAAE,CAAC;MAClBE,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,CAACL,KAAK,EAAEY,eAAe,CAAC,CAAC;;EAE5B;EACA,MAAMY,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BzB,QAAQ,CAACyB,KAAK,CAAC;IACfjB,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,MAAMmB,YAAY,GAAIH,CAAC,IAAK;IAC1BA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,IAAI7B,KAAK,CAAC8B,IAAI,CAAC,CAAC,EAAE;MAChBC,aAAa,CAAC/B,KAAK,CAAC8B,IAAI,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIjB,WAAW,IAAK;IAAA,IAAAkB,kBAAA;IACrCpC,QAAQ,CAACkB,WAAW,EAAE,CAAC,CAAC,CAAC;IACzBT,kBAAkB,CAAC,KAAK,CAAC;IACzBI,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpB,CAAAuB,kBAAA,GAAAtB,SAAS,CAACa,OAAO,cAAAS,kBAAA,uBAAjBA,kBAAA,CAAmBC,IAAI,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,OAAO,IAAK;IACzClC,QAAQ,CAACkC,OAAO,CAACC,IAAI,CAAC;IACtBL,aAAa,CAACI,OAAO,CAACC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIZ,CAAC,IAAK;IAAA,IAAAa,mBAAA;IAC3B,IAAI,CAAClC,eAAe,IAAIF,WAAW,CAACa,MAAM,KAAK,CAAC,EAAE;IAElD,QAAQU,CAAC,CAACc,GAAG;MACX,KAAK,WAAW;QACdd,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBpB,gBAAgB,CAAC+B,IAAI,IACnBA,IAAI,GAAGtC,WAAW,CAACa,MAAM,GAAG,CAAC,GAAGyB,IAAI,GAAG,CAAC,GAAGA,IAC7C,CAAC;QACD;MAEF,KAAK,SAAS;QACZf,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBpB,gBAAgB,CAAC+B,IAAI,IAAIA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD;MAEF,KAAK,OAAO;QACVf,CAAC,CAACI,cAAc,CAAC,CAAC;QAClB,IAAIrB,aAAa,IAAI,CAAC,IAAIA,aAAa,GAAGN,WAAW,CAACa,MAAM,EAAE;UAC5DmB,qBAAqB,CAAChC,WAAW,CAACM,aAAa,CAAC,CAAC;QACnD,CAAC,MAAM;UACLoB,YAAY,CAACH,CAAC,CAAC;QACjB;QACA;MAEF,KAAK,QAAQ;QACXpB,kBAAkB,CAAC,KAAK,CAAC;QACzBI,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACpB,CAAA6B,mBAAA,GAAA5B,SAAS,CAACa,OAAO,cAAAe,mBAAA,uBAAjBA,mBAAA,CAAmBL,IAAI,CAAC,CAAC;QACzB;MAEF;QACE;IACJ;EACF,CAAC;;EAED;EACA5C,SAAS,CAAC,MAAM;IACd,MAAMoD,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IACEhC,SAAS,CAACa,OAAO,IACjB,CAACb,SAAS,CAACa,OAAO,CAACoB,QAAQ,CAACD,KAAK,CAACf,MAAM,CAAC,IACzChB,cAAc,CAACY,OAAO,IACtB,CAACZ,cAAc,CAACY,OAAO,CAACoB,QAAQ,CAACD,KAAK,CAACf,MAAM,CAAC,EAC9C;QACAtB,kBAAkB,CAAC,KAAK,CAAC;QACzBI,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACtB;IACF,CAAC;IAEDmC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAMG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE/C,OAAA;IAAKqD,SAAS,EAAC,YAAY;IAAAC,QAAA,eACzBtD,OAAA;MAAMuD,QAAQ,EAAErB,YAAa;MAACmB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACnDtD,OAAA;QAAKqD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCtD,OAAA;UACEwD,GAAG,EAAExC,SAAU;UACfyC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAE1B,KAAM;UACboD,QAAQ,EAAE5B,iBAAkB;UAC5B6B,SAAS,EAAEhB,aAAc;UACzBiB,OAAO,EAAEA,CAAA,KAAMtD,KAAK,IAAIK,kBAAkB,CAAC,IAAI,CAAE;UACjDP,WAAW,EAAEA,WAAY;UACzBiD,SAAS,EAAC,cAAc;UACxBQ,YAAY,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAEFjE,OAAA;UACEyD,IAAI,EAAC,QAAQ;UACbJ,SAAS,EAAC,eAAe;UACzBa,QAAQ,EAAE,CAAC5D,KAAK,CAAC8B,IAAI,CAAC,CAAE;UAAAkB,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERrD,OAAO,iBACNZ,OAAA;UAAKqD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BtD,OAAA;YAAKqD,SAAS,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLvD,eAAe,IAAIF,WAAW,CAACa,MAAM,GAAG,CAAC,iBACxCrB,OAAA;QAAKwD,GAAG,EAAEvC,cAAe;QAACoC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACxDtD,OAAA;UAAIqD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7B9C,WAAW,CAAC2D,GAAG,CAAC,CAAC1B,OAAO,EAAE2B,KAAK,kBAC9BpE,OAAA;YAEEqD,SAAS,EAAE,mBACTe,KAAK,KAAKtD,aAAa,GAAG,UAAU,GAAG,EAAE,EACxC;YACHuD,OAAO,EAAEA,CAAA,KAAM7B,qBAAqB,CAACC,OAAO,CAAE;YAC9C6B,YAAY,EAAEA,CAAA,KAAMvD,gBAAgB,CAACqD,KAAK,CAAE;YAAAd,QAAA,eAE5CtD,OAAA;cAAKqD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCtD,OAAA;gBAAKqD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEb,OAAO,CAACC;cAAI;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACpDxB,OAAO,CAAC8B,KAAK,iBACZvE,OAAA;gBAAKqD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEb,OAAO,CAAC8B;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACvD,eACDjE,OAAA;gBAAKqD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEb,OAAO,CAAC+B;cAAQ;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC,GAbDxB,OAAO,CAACgC,GAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5D,EAAA,CA7LIJ,SAAS;AAAAyE,EAAA,GAATzE,SAAS;AA+Lf,eAAeA,SAAS;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}