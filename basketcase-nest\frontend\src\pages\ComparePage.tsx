import React from 'react';

const ComparePage: React.FC = () => {
  return (
    <div className="compare-page">
      <div className="container py-4">
        <div className="row mb-4">
          <div className="col-12">
            <h1 className="page-title">
              <i className="fas fa-balance-scale me-2"></i>
              Compare Prices
            </h1>
            <p className="text-muted">Compare product prices across different stores</p>
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <div className="alert alert-info">
              <i className="fas fa-info-circle me-2"></i>
              Price comparison tool is under development. This will allow you to compare specific products across multiple stores.
            </div>
            
            <div className="card">
              <div className="card-body">
                <h5 className="card-title">Coming Soon</h5>
                <p className="card-text">
                  This page will feature:
                </p>
                <ul>
                  <li>Side-by-side price comparisons</li>
                  <li>Store location mapping</li>
                  <li>Price history charts</li>
                  <li>Savings calculator</li>
                  <li>Best deal recommendations</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComparePage;
