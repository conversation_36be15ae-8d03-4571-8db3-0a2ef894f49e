"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreSchema = exports.Store = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let Store = class Store {
};
exports.Store = Store;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store name' }),
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], Store.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store branch name' }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Store.prototype, "branch", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store location coordinates' }),
    (0, mongoose_1.Prop)({
        type: {
            type: String,
            enum: ['Point'],
            default: 'Point',
        },
        coordinates: {
            type: [Number],
            index: '2dsphere',
        },
    }),
    __metadata("design:type", Object)
], Store.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store address' }),
    (0, mongoose_1.Prop)({
        street: { type: String, required: true },
        city: { type: String, required: true, index: true },
        province: { type: String, required: true, index: true },
        postalCode: String,
        country: { type: String, default: 'South Africa' },
    }),
    __metadata("design:type", Object)
], Store.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store contact information' }),
    (0, mongoose_1.Prop)({
        phone: String,
        email: String,
        website: String,
    }),
    __metadata("design:type", Object)
], Store.prototype, "contact", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Store operating hours' }),
    (0, mongoose_1.Prop)({
        monday: { open: String, close: String },
        tuesday: { open: String, close: String },
        wednesday: { open: String, close: String },
        thursday: { open: String, close: String },
        friday: { open: String, close: String },
        saturday: { open: String, close: String },
        sunday: { open: String, close: String },
    }),
    __metadata("design:type", Object)
], Store.prototype, "operatingHours", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether store is active' }),
    (0, mongoose_1.Prop)({ default: true, index: true }),
    __metadata("design:type", Boolean)
], Store.prototype, "isActive", void 0);
exports.Store = Store = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Store);
exports.StoreSchema = mongoose_1.SchemaFactory.createForClass(Store);
//# sourceMappingURL=store.schema.js.map