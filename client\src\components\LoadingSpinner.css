/* Loading Spinner Styles */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-spinner.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;
}

.loading-spinner.small {
  padding: 1rem;
}

.loading-spinner.large {
  padding: 3rem;
}

.spinner {
  position: relative;
  width: 40px;
  height: 40px;
}

.loading-spinner.small .spinner {
  width: 24px;
  height: 24px;
}

.loading-spinner.large .spinner {
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  animation-delay: 0.1s;
  border-top-color: var(--accent-color);
}

.spinner-ring:nth-child(3) {
  animation-delay: 0.2s;
  border-top-color: var(--info-color);
}

.spinner-ring:nth-child(4) {
  animation-delay: 0.3s;
  border-top-color: var(--warning-color);
}

.loading-message {
  margin-top: 1rem;
  color: var(--dark-color);
  font-size: 0.9rem;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
