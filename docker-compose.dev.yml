version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: basketcase-mongodb-dev
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: basketcase
    ports:
      - "27017:27017"
    volumes:
      - mongodb_dev_data:/data/db
    networks:
      - basketcase-dev-network

  # Backend Server (Development)
  server:
    build:
      context: ./server
      dockerfile: Dockerfile.dev
    container_name: basketcase-server-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
      MONGODB_URI: *********************************************************************
      CLIENT_URL: http://localhost:3000
      JWT_SECRET: dev_jwt_secret
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
    volumes:
      - ./server:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - basketcase-dev-network

  # Frontend Client (Development)
  client:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    container_name: basketcase-client-dev
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      CHOKIDAR_USEPOLLING: true
    ports:
      - "3000:3000"
    depends_on:
      - server
    volumes:
      - ./client:/app
      - /app/node_modules
    command: npm start
    networks:
      - basketcase-dev-network

volumes:
  mongodb_dev_data:

networks:
  basketcase-dev-network:
    driver: bridge
