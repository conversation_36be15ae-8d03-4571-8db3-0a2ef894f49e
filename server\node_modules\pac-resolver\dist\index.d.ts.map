{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AACA,OAAO,EAAE,cAAc,EAAW,MAAM,aAAa,CAAC;AAEtD;;GAEG;AACH,OAAO,SAAS,MAAM,aAAa,CAAC;AACpC,OAAO,WAAW,MAAM,eAAe,CAAC;AACxC,OAAO,eAAe,MAAM,mBAAmB,CAAC;AAChD,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,OAAO,MAAM,WAAW,CAAC;AAChC,OAAO,eAAe,MAAM,mBAAmB,CAAC;AAChD,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAC1C,OAAO,mBAAmB,MAAM,uBAAuB,CAAC;AACxD,OAAO,WAAW,MAAM,eAAe,CAAC;AACxC,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,SAAS,MAAM,aAAa,CAAC;AACpC,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAC1C,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AAEzE;;;GAGG;AACH,wBAAgB,iBAAiB,CAChC,GAAG,EAAE,iBAAiB,EACtB,IAAI,EAAE,MAAM,GAAG,MAAM,EACrB,KAAK,GAAE,kBAAuB,SA+BxB,MAAM,GAAG,GAAG,UACT,MAAM,KACZ,QAAQ,MAAM,CAAC,CAiBlB;AAED,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC;AACxB,MAAM,MAAM,IAAI,GACb,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,CAAC;AACN,MAAM,MAAM,GAAG,GACZ,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,CAAC;AACN,MAAM,MAAM,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC5E,MAAM,MAAM,KAAK,GACd,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,CAAC;AACT,MAAM,MAAM,kBAAkB,GAAG,cAAc,CAAC;AAChD,MAAM,WAAW,uBAAuB;IACvC,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC5C;AACD,MAAM,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,iBAAiB,CAAC,CAAC;AAEnE,eAAO,MAAM,OAAO;;;;;;;;;;;;;;EAclB,CAAC"}