# BasketCase - NestJS + TypeScript Version

A professional price comparison platform for South African grocery stores, built with NestJS, TypeScript, MongoDB, and React.

## 🚀 Features

### Backend (NestJS + TypeScript)
- **RESTful API** with automatic Swagger documentation
- **MongoDB** with Mongoose ODM and proper schemas
- **Automated Web Scraping** with scheduled jobs
- **Type Safety** throughout the entire application
- **Dependency Injection** and modular architecture
- **Input Validation** with class-validator
- **Error Handling** and logging
- **CORS** and security middleware

### Frontend (React + TypeScript)
- **React 18** with TypeScript
- **React Query** for data fetching and caching
- **React Router** for navigation
- **Bootstrap 5** for responsive design
- **Type-safe API** integration
- **Real-time updates** and notifications

### Core Functionality
- **Product Management** - CRUD operations with search and filtering
- **Store Management** - Multi-location store chains
- **Price Tracking** - Historical price data and comparisons
- **Automated Scraping** - SPAR, Checkers, and more stores
- **Savings Calculator** - Find the best deals automatically
- **Trending Products** - Popular items and price trends
- **Promotions** - Current deals and special offers

## 📁 Project Structure

```
basketcase-nest/
├── backend/                 # NestJS API Server
│   ├── src/
│   │   ├── modules/        # Feature modules
│   │   │   ├── products/   # Product management
│   │   │   ├── stores/     # Store management
│   │   │   ├── prices/     # Price tracking
│   │   │   └── scraping/   # Web scraping
│   │   ├── schemas/        # MongoDB schemas
│   │   ├── app.module.ts   # Main app module
│   │   └── main.ts         # Application entry point
│   ├── package.json
│   └── .env
├── frontend/               # React TypeScript App
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── types/          # TypeScript types
│   │   └── App.tsx         # Main app component
│   ├── package.json
│   └── public/
└── README.md
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- MongoDB (local or cloud)
- Git

### 1. Clone the Repository
```bash
git clone <repository-url>
cd basketcase-nest
```

### 2. Backend Setup
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your MongoDB connection string
npm run start:dev
```

The backend will start on http://localhost:5000
- API Documentation: http://localhost:5000/api/docs
- Health Check: http://localhost:5000/api/health

### 3. Frontend Setup
```bash
cd frontend
npm install
npm start
```

The frontend will start on http://localhost:3000

## 🔧 Environment Variables

### Backend (.env)
```env
# Database
MONGODB_URI=mongodb://localhost:27017/basketcase

# Server
PORT=5000
NODE_ENV=development

# Frontend
CLIENT_URL=http://localhost:3000

# Scraping Configuration
SCRAPING_ENABLED=true
SCRAPING_INTERVAL_MINUTES=30
```

## 📚 API Documentation

The API is fully documented with Swagger/OpenAPI. Once the backend is running, visit:
http://localhost:5000/api/docs

### Key Endpoints

#### Products
- `GET /api/products` - Get all products with filtering
- `GET /api/products/search?q=term` - Search products
- `GET /api/products/meta/categories` - Get categories
- `GET /api/products/meta/brands` - Get brands

#### Stores
- `GET /api/stores` - Get all stores
- `GET /api/stores/chains` - Get store chains
- `GET /api/stores/nearby?lat=&lng=` - Find nearby stores

#### Prices
- `GET /api/prices/biggest-savings` - Get biggest savings
- `GET /api/prices/trending` - Get trending products
- `GET /api/prices/promotions` - Get current promotions
- `GET /api/prices/compare/:productId` - Compare product prices

## 🤖 Automated Scraping

The system automatically scrapes prices from supported stores:

### Supported Stores
- **SPAR** - Multiple locations
- **Checkers** - Multiple locations
- **Pick n Pay** (planned)
- **Woolworths** (planned)

### Scraping Schedule
- **Development**: Every 30 minutes
- **Production**: Daily at 2 AM

### Scraping Features
- **Intelligent parsing** of product information
- **Price history tracking** with change detection
- **Promotion detection** and categorization
- **Error handling** and retry logic
- **Health monitoring** for each scraper

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm run test
npm run test:e2e
npm run test:cov
```

### Frontend Tests
```bash
cd frontend
npm test
npm run test:coverage
```

## 🚀 Deployment

### Backend Deployment
1. Build the application:
```bash
npm run build
```

2. Start production server:
```bash
npm run start:prod
```

### Frontend Deployment
1. Build for production:
```bash
npm run build
```

2. Serve the build folder with any static server

### Docker Deployment (Optional)
```bash
# Build and run with Docker Compose
docker-compose up --build
```

## 🔍 Development

### Code Quality
- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type safety
- **Husky** for git hooks (optional)

### Development Commands
```bash
# Backend
npm run start:dev     # Start with hot reload
npm run start:debug   # Start with debugging
npm run lint          # Run ESLint
npm run format        # Format code

# Frontend
npm start             # Start development server
npm run build         # Build for production
npm run type-check    # TypeScript type checking
```

## 📊 Monitoring & Logging

### Backend Logging
- Structured logging with Winston
- Request/response logging
- Error tracking and reporting
- Performance monitoring

### Health Checks
- Database connectivity
- Scraper health status
- API endpoint monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the API documentation at `/api/docs`
- Review the logs for debugging information

## 🎯 Roadmap

### Phase 1 (Current)
- ✅ Core API with TypeScript
- ✅ Basic scraping for SPAR and Checkers
- ✅ React frontend with TypeScript
- ✅ Price comparison and savings

### Phase 2 (Planned)
- 🔄 Additional store scrapers
- 🔄 User accounts and favorites
- 🔄 Price alerts and notifications
- 🔄 Mobile app (React Native)

### Phase 3 (Future)
- 🔄 Machine learning for price predictions
- 🔄 Advanced analytics dashboard
- 🔄 API rate limiting and authentication
- 🔄 Multi-region support

---

**Built with ❤️ for South African shoppers**
