import React, { useState, useEffect } from 'react';
import { productsApi } from '@/services/api';
import { Product, ProductQuery } from '@/types';

const ProductsPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState<ProductQuery>({
    page: 1,
    limit: 20,
    search: '',
    category: '',
    brand: '',
  });

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await productsApi.getAll(query);
        if (response.success) {
          setProducts(response.data || []);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [query]);

  const handleSearch = (searchTerm: string) => {
    setQuery(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner-border loading-spinner text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="products-page">
      <div className="container py-4">
        <div className="row mb-4">
          <div className="col-12">
            <h1 className="page-title">
              <i className="fas fa-box me-2"></i>
              Products
            </h1>
            <p className="text-muted">Compare prices across all stores</p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="row mb-4">
          <div className="col-lg-6">
            <div className="search-container">
              <i className="fas fa-search search-icon"></i>
              <input
                type="text"
                className="form-control search-input"
                placeholder="Search products..."
                value={query.search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="row">
          {products.length > 0 ? (
            products.map((product) => (
              <div key={product._id} className="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div className="card product-card h-100">
                  <div className="card-img-top product-image d-flex align-items-center justify-content-center">
                    {product.images && product.images.length > 0 ? (
                      <img
                        src={product.images[0].url}
                        alt={product.name}
                        className="img-fluid"
                        style={{ maxHeight: '150px', objectFit: 'contain' }}
                      />
                    ) : (
                      <div className="text-muted">
                        <i className="fas fa-image fa-3x"></i>
                      </div>
                    )}
                  </div>
                  <div className="card-body">
                    <h5 className="product-title">{product.name}</h5>
                    <p className="product-brand">{product.brand}</p>
                    <span className="badge bg-secondary">{product.category}</span>
                    {product.isRecentlyUpdated && (
                      <span className="badge bg-success ms-1">Updated</span>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-12">
              <div className="text-center py-5">
                <i className="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>No products found</h4>
                <p className="text-muted">Try adjusting your search criteria</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;
