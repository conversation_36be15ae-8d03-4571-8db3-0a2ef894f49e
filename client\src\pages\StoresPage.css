/* Stores Page Styles */
.stores-page {
  min-height: calc(100vh - 80px);
  padding: 1rem 0;
}

.stores-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Page Header */
.stores-header {
  text-align: center;
  margin-bottom: 2rem;
}

.stores-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin: 0 0 0.5rem 0;
}

.stores-header p {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0;
}

/* Filters */
.stores-filters {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 0.9rem;
}

.filter-group select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: var(--transition);
}

.filter-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.filter-group select:disabled {
  background-color: var(--secondary-color);
  cursor: not-allowed;
  opacity: 0.6;
}

.location-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  white-space: nowrap;
}

.location-button:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.location-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.results-count {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  color: #6c757d;
  font-size: 0.9rem;
  text-align: center;
}

/* Main Content */
.stores-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  align-items: start;
}

.stores-map {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.stores-list {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  height: 500px;
  overflow-y: auto;
}

.stores-list h3 {
  margin: 0 0 1rem 0;
  color: var(--dark-color);
  font-size: 1.25rem;
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.no-stores {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.no-stores p {
  margin-bottom: 1rem;
}

.no-stores button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.no-stores button:hover {
  background-color: #1e3d72;
}

/* Store Cards */
.store-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.store-card {
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  background: white;
}

.store-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.store-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(44, 90, 160, 0.05);
  box-shadow: var(--shadow);
}

.store-card-header {
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.store-card-header .store-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 0.25rem 0;
}

.store-card-header .store-branch {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
}

.store-card-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.store-address {
  font-size: 0.85rem;
  color: var(--dark-color);
  line-height: 1.4;
}

.store-distance {
  font-size: 0.85rem;
  color: var(--accent-color);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.store-contact {
  font-size: 0.85rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.store-card-actions {
  display: flex;
  justify-content: center;
}

.directions-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: var(--transition);
}

.directions-button:hover {
  background-color: #218838;
  transform: translateY(-1px);
}

/* Custom Scrollbar for Store List */
.stores-list::-webkit-scrollbar {
  width: 6px;
}

.stores-list::-webkit-scrollbar-track {
  background: var(--secondary-color);
  border-radius: 3px;
}

.stores-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.stores-list::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .stores-header h1 {
    font-size: 2rem;
  }
  
  .stores-header p {
    font-size: 1rem;
  }
  
  .filter-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stores-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stores-list {
    height: 400px;
    order: -1;
  }
  
  .store-card {
    padding: 0.75rem;
  }
  
  .store-card-header .store-name {
    font-size: 1rem;
  }
  
  .store-card-content {
    gap: 0.4rem;
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 480px) {
  .stores-container {
    padding: 0 0.5rem;
  }
  
  .stores-filters {
    padding: 1rem;
  }
  
  .stores-list {
    padding: 1rem;
    height: 300px;
  }
  
  .store-card {
    padding: 0.75rem;
  }
  
  .store-card-header {
    margin-bottom: 0.5rem;
  }
  
  .store-card-content {
    margin-bottom: 0.5rem;
  }
  
  .directions-button {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }
}
