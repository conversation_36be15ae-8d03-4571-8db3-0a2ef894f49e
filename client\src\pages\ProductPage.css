/* Product Page Styles */
.product-page {
  min-height: calc(100vh - 80px);
  padding: 1rem 0;
}

.product-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.back-button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: var(--transition);
}

.back-button:hover {
  background-color: rgba(44, 90, 160, 0.1);
}

.breadcrumb-separator {
  color: #dee2e6;
}

.breadcrumb-category {
  color: var(--primary-color);
}

.breadcrumb-current {
  color: var(--dark-color);
  font-weight: 500;
}

/* Product Header */
.product-header {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.image-gallery {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.main-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.thumbnail-images {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
}

.thumbnail-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
}

.thumbnail-image:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.no-image-placeholder {
  width: 100%;
  height: 300px;
  background-color: var(--secondary-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
}

.placeholder-icon {
  font-size: 4rem;
  opacity: 0.5;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.product-title-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.product-brand {
  font-size: 0.9rem;
  color: var(--primary-color);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin: 0;
  line-height: 1.2;
}

.product-category-badge {
  background-color: var(--secondary-color);
  color: var(--dark-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  width: fit-content;
}

.product-description {
  color: #6c757d;
  line-height: 1.6;
}

/* Price Summary */
.price-summary {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border-left: 4px solid var(--accent-color);
}

.price-range {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.price-label {
  font-weight: 600;
  color: var(--dark-color);
}

.price-values {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--accent-color);
}

.cheapest-price {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.price-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--accent-color);
}

.store-name {
  color: var(--primary-color);
  font-weight: 500;
}

.store-count {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Product Specifications */
.product-specifications {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.product-specifications h3 {
  margin: 0 0 1rem 0;
  color: var(--dark-color);
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.spec-label {
  font-weight: 500;
  color: var(--dark-color);
}

.spec-value {
  color: #6c757d;
}

/* Tabs */
.product-tabs {
  margin-top: 2rem;
}

.tab-buttons {
  display: flex;
  border-bottom: 2px solid var(--border-color);
  margin-bottom: 2rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: #6c757d;
  border-bottom: 2px solid transparent;
  transition: var(--transition);
}

.tab-button:hover {
  color: var(--primary-color);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  min-height: 400px;
}

/* Price Comparison Tab */
.prices-tab {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.price-card {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
}

.price-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.price-card.cheapest {
  border-color: var(--accent-color);
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.1) 100%);
}

.price-card-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.price-card-header .store-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 0.25rem 0;
}

.price-card-header .store-branch {
  color: #6c757d;
  font-size: 0.9rem;
}

.price-info {
  margin-bottom: 1rem;
}

.current-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
}

.original-price {
  color: #6c757d;
  text-decoration: line-through;
  font-size: 0.9rem;
}

.savings-info {
  margin-top: 0.5rem;
}

.cheapest-badge {
  background-color: var(--accent-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.savings-amount {
  color: var(--danger-color);
  font-size: 0.85rem;
  font-weight: 500;
}

.availability-info {
  margin-bottom: 1rem;
}

.stock-status {
  font-size: 0.85rem;
  font-weight: 500;
}

.stock-status.in-stock {
  color: var(--accent-color);
}

.stock-status.out-of-stock {
  color: var(--danger-color);
}

.promotion-badge {
  background-color: var(--warning-color);
  color: var(--dark-color);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.no-prices {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

/* Map Tab */
.map-tab {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

/* Price History Tab */
.history-tab {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.price-history h4 {
  margin: 0 0 1rem 0;
  color: var(--dark-color);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-entry {
  display: grid;
  grid-template-columns: 120px 1fr auto;
  gap: 1rem;
  padding: 0.75rem;
  background-color: var(--secondary-color);
  border-radius: var(--border-radius);
  align-items: center;
}

.history-date {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.history-price {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dark-color);
}

.history-change {
  font-size: 0.85rem;
  font-weight: 500;
  text-align: right;
}

.history-change.increase {
  color: var(--danger-color);
}

.history-change.decrease {
  color: var(--accent-color);
}

.history-change.no_change {
  color: #6c757d;
}

.select-store-message {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

/* Similar Products */
.similar-products {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.similar-products h3 {
  margin: 0 0 1.5rem 0;
  color: var(--dark-color);
  text-align: center;
}

.similar-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .product-header {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .product-title {
    font-size: 1.5rem;
  }
  
  .tab-buttons {
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .price-comparison-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .history-entry {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
  }
  
  .similar-products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .product-container {
    padding: 0 0.5rem;
  }
  
  .main-image {
    height: 250px;
  }
  
  .price-summary,
  .product-specifications,
  .history-tab {
    padding: 1rem;
  }
  
  .price-card {
    padding: 1rem;
  }
}
