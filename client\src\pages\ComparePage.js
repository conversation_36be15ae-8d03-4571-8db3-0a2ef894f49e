import React, { useState } from 'react';
import SearchBar from '../components/SearchBar';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const ComparePage = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSearch = (query, filters) => {
    console.log('Search:', query, filters);
    // TODO: Implement search functionality
  };

  return (
    <div className="compare-page">
      <div className="container">
        <h1>Compare Prices</h1>
        <p>Search for products to compare prices across different stores.</p>
        
        <div className="search-section">
          <SearchBar onSearch={handleSearch} placeholder="Search products to compare..." />
        </div>

        {loading && <LoadingSpinner message="Searching products..." />}
        {error && <ErrorMessage message={error} />}

        <div className="compare-results">
          <p>Start by searching for a product above to see price comparisons.</p>
        </div>
      </div>
    </div>
  );
};

export default ComparePage;
