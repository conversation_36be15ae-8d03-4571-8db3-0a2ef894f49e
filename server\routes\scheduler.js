const express = require('express');
const router = express.Router();
const SchedulerService = require('../services/SchedulerService');

// Initialize scheduler service
const schedulerService = new SchedulerService();

// GET /api/scheduler/status - Get scheduler status
router.get('/status', async (req, res) => {
  try {
    const status = schedulerService.getAllJobsStatus();
    res.json(status);
  } catch (error) {
    console.error('Error getting scheduler status:', error);
    res.status(500).json({ message: 'Error getting scheduler status', error: error.message });
  }
});

// GET /api/scheduler/health - Get health check
router.get('/health', async (req, res) => {
  try {
    const health = await schedulerService.performHealthCheck();
    res.json(health);
  } catch (error) {
    console.error('Error performing health check:', error);
    res.status(500).json({ message: 'Error performing health check', error: error.message });
  }
});

// POST /api/scheduler/start - Start the scheduler
router.post('/start', async (req, res) => {
  try {
    const success = schedulerService.startScheduler();
    if (success) {
      res.json({ message: 'Scheduler started successfully' });
    } else {
      res.status(500).json({ message: 'Failed to start scheduler' });
    }
  } catch (error) {
    console.error('Error starting scheduler:', error);
    res.status(500).json({ message: 'Error starting scheduler', error: error.message });
  }
});

// POST /api/scheduler/stop - Stop the scheduler
router.post('/stop', async (req, res) => {
  try {
    const success = schedulerService.stopScheduler();
    if (success) {
      res.json({ message: 'Scheduler stopped successfully' });
    } else {
      res.status(500).json({ message: 'Failed to stop scheduler' });
    }
  } catch (error) {
    console.error('Error stopping scheduler:', error);
    res.status(500).json({ message: 'Error stopping scheduler', error: error.message });
  }
});

// POST /api/scheduler/run - Manually trigger scraping job
router.post('/run', async (req, res) => {
  try {
    const { jobType = 'manual' } = req.body;
    
    if (schedulerService.isRunning) {
      return res.status(409).json({ 
        message: 'Scraping job is already running',
        currentJob: schedulerService.currentJob
      });
    }

    // Start the job asynchronously
    schedulerService.runJobManually(jobType)
      .then(result => {
        console.log('Manual scraping job completed:', result);
      })
      .catch(error => {
        console.error('Manual scraping job failed:', error);
      });

    res.json({ 
      message: 'Scraping job started',
      jobType,
      startTime: new Date()
    });
    
  } catch (error) {
    console.error('Error starting manual scraping job:', error);
    res.status(500).json({ message: 'Error starting scraping job', error: error.message });
  }
});

// GET /api/scheduler/jobs/:jobName - Get specific job status
router.get('/jobs/:jobName', async (req, res) => {
  try {
    const { jobName } = req.params;
    const status = schedulerService.getJobStatus(jobName);
    
    if (status.error) {
      return res.status(404).json(status);
    }
    
    res.json(status);
  } catch (error) {
    console.error('Error getting job status:', error);
    res.status(500).json({ message: 'Error getting job status', error: error.message });
  }
});

// GET /api/scheduler/results/latest - Get latest scraping results
router.get('/results/latest', async (req, res) => {
  try {
    const results = schedulerService.lastRunResults;
    
    if (!results) {
      return res.status(404).json({ message: 'No scraping results available' });
    }
    
    res.json(results);
  } catch (error) {
    console.error('Error getting latest results:', error);
    res.status(500).json({ message: 'Error getting latest results', error: error.message });
  }
});

// Initialize scheduler service when module is loaded
(async () => {
  try {
    await schedulerService.initialize();
    console.log('Scheduler service initialized');
    
    // Auto-start scheduler in production
    if (process.env.NODE_ENV === 'production') {
      schedulerService.startScheduler();
      console.log('Scheduler auto-started in production mode');
    }
  } catch (error) {
    console.error('Failed to initialize scheduler service:', error);
  }
})();

module.exports = router;
