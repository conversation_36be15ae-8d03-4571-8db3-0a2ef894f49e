/* Global Styles */
.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Custom Bootstrap Overrides */
:root {
  --bs-primary: #2196f3;
  --bs-primary-rgb: 33, 150, 243;
  --bs-secondary: #6c757d;
  --bs-success: #28a745;
  --bs-danger: #dc3545;
  --bs-warning: #ffc107;
  --bs-info: #17a2b8;
  --bs-light: #f8f9fa;
  --bs-dark: #343a40;
}

/* Navbar Styles */
.navbar-brand {
  font-weight: bold;
  font-size: 1.5rem;
}

.navbar-nav .nav-link {
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
  color: var(--bs-primary) !important;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
  color: white;
  padding: 4rem 0;
  margin-bottom: 2rem;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

/* Card Styles */
.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
  color: white;
  border-bottom: none;
  font-weight: 600;
}

/* Product Cards */
.product-card {
  height: 100%;
  cursor: pointer;
}

.product-image {
  height: 200px;
  object-fit: cover;
  background: #f8f9fa;
}

.product-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.product-brand {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.product-price {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--bs-success);
}

.product-original-price {
  text-decoration: line-through;
  color: #6c757d;
  font-size: 1rem;
  margin-left: 0.5rem;
}

/* Savings Badge */
.savings-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--bs-danger);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Filter Panel */
.filter-panel {
  background: #f8f9fa;
  border-radius: 0.375rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.filter-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

/* Search Bar */
.search-container {
  position: relative;
  margin-bottom: 2rem;
}

.search-input {
  padding-left: 3rem;
  border-radius: 2rem;
  border: 2px solid #e9ecef;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

/* Loading Spinner */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Error Message */
.error-container {
  text-align: center;
  padding: 2rem;
  color: var(--bs-danger);
}

/* Pagination */
.pagination {
  justify-content: center;
  margin-top: 2rem;
}

.page-link {
  color: var(--bs-primary);
  border-color: #dee2e6;
}

.page-link:hover {
  color: #0056b3;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-item.active .page-link {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

/* Footer */
.footer {
  background: #343a40;
  color: white;
  padding: 2rem 0;
  margin-top: auto;
}

.footer-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer-link {
  color: #adb5bd;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .filter-panel {
    padding: 1rem;
  }
  
  .product-card {
    margin-bottom: 1rem;
  }
}

/* Utility Classes */
.text-primary-custom {
  color: var(--bs-primary) !important;
}

.bg-primary-custom {
  background-color: var(--bs-primary) !important;
}

.border-primary-custom {
  border-color: var(--bs-primary) !important;
}

.shadow-custom {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.rounded-custom {
  border-radius: 0.375rem !important;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
